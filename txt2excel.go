package main

import (
	"fmt"
	"io/ioutil"
	"strings"

	"github.com/xuri/excelize/v2"
)

// FileData 用来保存文件名和内容
type FileData struct {
	Title   string
	Content string
}

// ReadFiles 从指定目录读取所有txt文件并返回文件数据
func ReadFiles(dir string) ([]FileData, error) {
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return nil, err
	}

	var fileDataList []FileData
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".txt") {
			// 读取txt文件的内容
			content, err := ioutil.ReadFile(fmt.Sprintf("%s/%s", dir, file.Name()))
			if err != nil {
				return nil, err
			}

			// 创建FileData实例，并将文件名和内容填充进去
			fileData := FileData{
				Title:   file.Name(),
				Content: string(content),
			}
			fileDataList = append(fileDataList, fileData)
		}
	}
	return fileDataList, nil
}

// WriteToExcel 将文件数据写入Excel表格
func WriteToExcel(fileDataList []FileData, outputFile string) error {
	// 创建一个新的Excel文件
	f := excelize.NewFile()

	// 设置标题行
	f.SetCellValue("Sheet1", "A2", "Title")
	f.SetCellValue("Sheet1", "B2", "Content")

	// 写入文件数据
	for i, data := range fileDataList {
		row := i + 3 // Excel的行是从1开始的，标题占用第一行
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), strings.ReplaceAll(data.Title, ".txt", ""))
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), data.Content)
	}

	// 保存Excel文件
	if err := f.SaveAs(outputFile); err != nil {
		return err
	}

	return nil
}

//func main() {
//	var dir string
//	// 获取用户输入的文件夹路径
//	fmt.Print("请输入文件夹路径: ")
//	fmt.Scanln(&dir)
//
//	// 输出Excel文件路径
//	outputFile := "output.xlsx"
//
//	// 从文件夹中读取txt文件
//	fileDataList, err := ReadFiles(dir)
//	if err != nil {
//		fmt.Println("Error reading files:", err)
//		return
//	}
//
//	// 将数据写入Excel
//	if err := WriteToExcel(fileDataList, outputFile); err != nil {
//		fmt.Println("Error writing to Excel:", err)
//		return
//	}
//
//	fmt.Println("Excel file created successfully:", outputFile)
//}
