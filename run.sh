#!/bin/bash

#################################### 更新内网 ##################################

if [ -f manifest/config/config.yaml ]; then
    mv manifest/config/config.yaml manifest/config/config.yaml.bakkkk
fi

cp manifest/config/config.yaml.base manifest/config/config.yaml

# 删除本地镜像
sudo docker rmi $(docker images -q gtcms/aapi)

# 运行 make build 构建项目
make build

# 运行 make image 生成 Docker 镜像
sudo make image

# 检查镜像是否被成功构建
if sudo docker images gtcms/aapi | grep -q "gtcms/aapi"; then
    echo "Docker镜像构建成功"

    # 使用 docker save 将镜像保存到文件
    sudo docker save gtcms/aapi > gtcms_aapi

    # 使用 SCP 将文件上传到目标服务器
    scp gtcms_aapi admin1@*************:/home/<USER>
    scp /home/<USER>/ng-word/cms/h5/dist.zip admin1@*************:/home/<USER>

    # 删除本地文件
    rm -rf gtcms_aapi

    # 使用 SSH 执行目标服务器脚本
    ssh admin1@************* 'echo fDxi29.#iG@!LA8zv5PbrMnq | sudo -S /home/<USER>/run_gtcms.sh'

    # 成功日志输出
    echo "所有步骤执行成功"
else
    echo "Docker镜像构建失败"
fi

if [ -f manifest/config/config.yaml.bakkkk ]; then
    mv manifest/config/config.yaml.bakkkk manifest/config/config.yaml
fi

