# halal-admin

## Getting started

```
cd existing_repo
git remote add origin https://hcode.halaladmin.vip:6243/halal/halal-admin.git
git branch -M main
git push -uf origin main
```

* 添加下面配置到ssh配置文件 ~/.ssh/config

登录测试服使用隧道： ssh halal-dev
测试服数据库 localhost:3406

```
Host halal-dev
  HostName ************
  Port 2542
  User ec2-user
  IdentityFile ~/.ssh/id_rsa
  LocalForward 3406 halal.cluster-c76u4qak81vs.ap-southeast-3.rds.amazonaws.com:3306
  LocalForward 8501 127.0.0.1:8500
  LocalForward 9001 127.0.0.1:9000
  LocalForward 3001 127.0.0.1:3000
```
