package main

import (
	"fmt"
	"net/http"
	"sync"
	"time"
)

// 发送请求的函数
func sendRequest2(url string, wg *sync.WaitGroup, ch chan<- bool) {
	defer wg.Done() // 处理完一个请求后调用 Done

	// 发起 GET 请求
	startTime := time.Now() // 记录开始时间
	resp, err := http.Get(url)
	elapsedTime := time.Since(startTime) // 计算请求消耗的时间

	if err != nil {
		fmt.Println("Error:", err)
		ch <- false
		return
	}
	defer resp.Body.Close()

	// 打印响应状态和耗时
	fmt.Printf("Response Status: %s, Time: %v\n", resp.Status, elapsedTime)
	ch <- true
}

// 压测函数
func loadTest(url string, concurrency int, requests int) {
	var wg sync.WaitGroup
	ch := make(chan bool, requests) // 通道，用来接收请求的结果

	for i := 0; i < requests; i++ {
		wg.Add(1)
		go sendRequest2(url, &wg, ch)
	}

	// 限制并发数量
	// 并发数限制：如果并发数大于 concurrency，阻塞
	concurrentLimiter := make(chan struct{}, concurrency)

	for i := 0; i < requests; i++ {
		concurrentLimiter <- struct{}{} // 并发数限制
		go func() {
			<-concurrentLimiter
			<-ch // 获取请求结果
		}()
	}

	wg.Wait()
	close(ch)
}

//func main() {
//	// 要测试的目标 URL
//	url := "http://www.222.com" // 替换为你想要压测的 URL
//
//	// 并发数和请求数
//	concurrency := 300 // 每次最多并发请求数
//	requests := 2000   // 总共发起的请求数
//
//	// 开始压测
//	fmt.Printf("Starting load test for %s with %d requests and concurrency %d...\n", url, requests, concurrency)
//	loadTest(url, concurrency, requests)
//}
