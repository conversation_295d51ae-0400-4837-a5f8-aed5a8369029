-- 古兰经数据库表结构
-- 基于 https://equran.id/apidev/v1
-- 包含三个主要接口的数据结构：
-- 1. GET /api/surat - 获取所有章节列表
-- 2. GET /api/surat/{id} - 获取章节详情及经文
-- 3. GET /api/tafsir/{id} - 获取章节注释

-- 1. 章节表 (Surat/Surah) - 对应接口1和接口2、3的基本信息
DROP TABLE IF EXISTS surat_daftar;
CREATE TABLE surat_daftar (
                              id INT PRIMARY KEY AUTO_INCREMENT,
                              nomor INT NOT NULL UNIQUE COMMENT '章节编号 (1-114)',
                              nama VARCHAR(255) NOT NULL COMMENT '阿拉伯语章节名',
                              nama_latin VARCHAR(255) NOT NULL COMMENT '拉丁化章节名',
                              jumlah_ayat INT NOT NULL COMMENT '经文数量',
                              tempat_turun ENUM('mekah', 'madinah') NOT NULL COMMENT '降示地点',
                              arti VARCHAR(255) NOT NULL COMMENT '章节含义',
                              deskripsi TEXT COMMENT '章节描述',
                              audio VARCHAR(500) COMMENT '音频文件URL',
                              status BOOLEAN DEFAULT TRUE COMMENT '状态标识',
                              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                              INDEX idx_nomor (nomor)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='古兰经章节表';

-- 2. 经文表 (Ayat) - 对应接口2中的ayat数组
DROP TABLE IF EXISTS surat_ayat;
CREATE TABLE surat_ayat (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            ayat_id INT NOT NULL COMMENT '经文全局ID',
                            surah_id INT NOT NULL COMMENT '所属章节ID',
                            nomor INT NOT NULL COMMENT '经文在章节中的编号',
                            ar TEXT NOT NULL COMMENT '阿拉伯语经文',
                            tr TEXT COMMENT '音译文本',
                            idn TEXT COMMENT '印尼语翻译',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                            UNIQUE KEY unique_ayat (surah_id, nomor),
                            INDEX idx_ayat_id (ayat_id),
                            INDEX idx_surah_id (surah_id),
                            INDEX idx_nomor (nomor)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='古兰经经文表';

-- 3. 注释表 (Tafsir) - 对应接口3中的tafsir数组
DROP TABLE IF EXISTS surat_tafsir;
CREATE TABLE surat_tafsir (
                              id INT PRIMARY KEY AUTO_INCREMENT,
                              tafsir_id INT NOT NULL COMMENT '注释全局ID',
                              surah_id INT NOT NULL COMMENT '所属章节ID',
                              ayat_nomor INT NOT NULL COMMENT '对应经文编号',
                              tafsir LONGTEXT NOT NULL COMMENT '注释内容',
                              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

                              UNIQUE KEY unique_tafsir (surah_id, ayat_nomor),
                              INDEX idx_tafsir_id (tafsir_id),
                              INDEX idx_surah_id (surah_id),
                              INDEX idx_ayat_nomor (ayat_nomor)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='古兰经注释表';

-- new columns for surat_daftar max
alter table surat_ayat add column juz INT NOT NULL default  0 COMMENT 'juz编号';
alter table surat_ayat add column page INT NOT NULL default  0 COMMENT '所在页码';