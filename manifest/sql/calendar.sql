-- 伊斯兰日历系统相关表结构

-- Hijriah日历数据表
-- 公历对应的伊斯兰历
-- method_code: LFNU, UMMUL_QURA。LFNU暂时没找到数据来源，UMMUL_QURA可以通过计算获取。所以很有可能初期阶段这个表是空的
CREATE TABLE calendar_hijriah (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    gregorian_year INT NOT NULL COMMENT '公历年',
    gregorian_month INT NOT NULL COMMENT '公历月',
    gregorian_day INT NOT NULL COMMENT '公历日',
    hijriah_year INT NOT NULL COMMENT 'Hijriah年',
    hijriah_month INT NOT NULL COMMENT 'Hijriah月',
    hijriah_day INT NOT NULL COMMENT 'Hijriah日',
    method_code VARCHAR(20) NOT NULL COMMENT '计算方法代码，如：LFNU, UMMUL_QURA',
    weekday INT NOT NULL COMMENT '星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)',
    pasaran INT NOT NULL COMMENT 'Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_date_method (gregorian_year, gregorian_month, gregorian_day, method_code) COMMENT '公历日期+计算方法唯一索引',
    INDEX idx_hijriah_date (hijriah_year, hijriah_month, hijriah_day) COMMENT 'Hijriah日期索引',
    INDEX idx_gregorian_date (gregorian_year, gregorian_month, gregorian_day) COMMENT '公历日期索引',
    INDEX idx_method_code (method_code) COMMENT '计算方法代码索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Hijriah日历数据表';

-- 日历事件表
CREATE TABLE calendar_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_type ENUM('HARI_BESAR', 'LIBUR_NASIONAL', 'PUASA') NOT NULL COMMENT '事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒',
    title VARCHAR(200) NOT NULL COMMENT '事件标题',
    description TEXT COMMENT '事件描述',
    gregorian_year INT NOT NULL COMMENT '公历年',
    gregorian_month INT NOT NULL COMMENT '公历月',
    gregorian_day INT NOT NULL COMMENT '公历日',
    jump_url VARCHAR(500) COMMENT '点击跳转链接',
    data_source VARCHAR(20) DEFAULT 'MANUAL' COMMENT '数据来源：MANUAL-人工录入，CRAWLER-爬虫获取',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_event_type (event_type) COMMENT '事件类型索引',
    INDEX idx_gregorian_date (gregorian_year, gregorian_month, gregorian_day) COMMENT '公历日期索引',
    INDEX idx_data_source (data_source) COMMENT '数据来源索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日历事件表';
