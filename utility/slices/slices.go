package slices

import (
	"github.com/gogf/gf/v2/util/gconv"
	"strings"
)

func Index[S ~[]E, E comparable](s S, v E) int {
	for i := range s {
		if v == s[i] {
			return i
		}
	}
	return -1
}

func Contains[S ~[]E, E comparable](s S, v E) bool {
	return Index(s, v) >= 0
}

func Equal[S ~[]E, E comparable](s1, s2 S) bool {
	if len(s1) != len(s2) {
		return false
	}
	for i := range s1 {
		if s1[i] != s2[i] {
			return false
		}
	}
	return true
}

// Unique 去重
func Unique[S ~[]E, E comparable](s1 S) S {
	res := make([]E, 0, len(s1))
	m := make(map[E]struct{})
	for _, item := range s1 {
		if _, ok := m[item]; ok {
			continue
		}
		m[item] = struct{}{}
		res = append(res, item)
	}
	return res
}

func Join[S ~[]E, E any](s S, sep string) string {
	data := make([]string, len(s))
	for _, r := range s {
		gs := gconv.String(r)
		if len(gs) < 1 {
			continue
		}
		data = append(data, gs)
	}
	return strings.Join(data, sep)
}
