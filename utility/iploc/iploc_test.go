package iploc

import (
	"testing"
)

func TestIPLoc(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name            string
		args            args
		wantCountryCode string
		wantCity        string
	}{
		{
			name: "Test1",
			args: args{
				ip: "*************",
			},
			wantCountryCode: "US",
			wantCity:        "Monroe",
		},
		{
			name: "Test2",
			args: args{
				ip: "*************",
			},
			wantCountryCode: "CN",
			wantCity:        "Dongguan",
		},

		{
			name: "Test3",
			args: args{
				ip: "**************",
			},
			wantCountryCode: "CN",
			wantCity:        "Heyuan",
		},

		{
			name: "Test4",
			args: args{
				ip: "*************",
			},
			wantCountryCode: "-",
			wantCity:        "-",
		},

		{
			name: "Test5",
			args: args{
				ip: "fe80:0000:0001:0000:0440:44ff:1233:5678",
			},
			wantCountryCode: "-",
			wantCity:        "-",
		},
	}

	ipv4Db := "../." + defaultIPv4DB
	ipv6Db := "../." + defaultIPv6DB
	ReloadDB(ipv4Db, ipv6Db)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotCountryCode, gotCity := IPLoc(tt.args.ip)
			if gotCountryCode != tt.wantCountryCode {
				t.Errorf("IPLoc() gotCountryCode = %v, want %v", gotCountryCode, tt.wantCountryCode)
			}
			if gotCity != tt.wantCity {
				t.Errorf("IPLoc() gotCity = %v, want %v", gotCity, tt.wantCity)
			}
		})
	}
}
