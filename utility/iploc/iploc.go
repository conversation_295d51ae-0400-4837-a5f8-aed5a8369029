package iploc

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/ipipdotnet/ipdb-go"
)

// 通用的ip地址库， 使用离线数据库

var globalIpLoc *ipdb.City

const (
	defaultIPv4DB       = "./resource/ipdb/IP2Location.ipdb"
	defaultDockerIPv4DB = "./ipdb/IP2Location.ipdb"
	defaultIPv6DB       = "./resource/ipdb/IP2LOCATION-LITE-DB3.IPV6.BIN"
)

func init() {
	ctx := gctx.New()
	cfg := g.Cfg()
	if cfg == nil {
		ReloadDB(defaultIPv4DB, defaultIPv6DB)
		return
	}
	ipv4Db := cfg.MustGet(ctx, "ipdb.ipv4")
	ipv6Db := cfg.MustGet(ctx, "ipdb.ipv6")
	ReloadDB(ipv4Db.String(), ipv6Db.String())
}

// ReloadDB 使用数据库文件路径重新加载
func ReloadDB(ipv4Addr, ipv6Addr string) {
	var err error
	ctx := context.Background()

	globalIpLoc, err = ipdb.NewCity(ipv4Addr)
	if err != nil {
		g.Log().Line().Warningf(ctx, "Failed to load IPDB! ERR:%s", err)
		globalIpLoc, err = ipdb.NewCity(defaultDockerIPv4DB)
		if err != nil {
			g.Log().Line().Warningf(ctx, "Failed again to load IPDB! ERR:%s", err)
			return
		}
	}
	g.Log().Line().Infof(ctx, "Success to load IPDB! val:%s", defaultIPv4DB)

	fmt.Println("whether support ipv6: ", globalIpLoc.IsIPv6())
	ip := "*******"                             // 您要查询的 IP 地址
	info, err := globalIpLoc.FindInfo(ip, "CN") // CN表示中国地区
	if err != nil {
		fmt.Println("Failed to find IP info:", err)
		return
	}

	fmt.Println("IP:", ip)
	fmt.Println("Country:", info.CountryName)
	fmt.Println("Province:", info.RegionName)
	fmt.Println("City:", info.CityName)

}

func IPLoc(ip string) (regionName, city string) {
	record, _ := globalIpLoc.FindInfo(ip, "CN")
	//fmt.Printf("IP loc: %+v", record)
	regionName = record.RegionName
	if g.IsEmpty(regionName) {
		regionName = record.CountryName
	}
	city = record.CityName
	return
}

//func IPv6Loc(ip string) (countryCode, city string) {
//	record, _ := ipv6db.Get_all(ip)
//	return record.Country_short, record.City
//}
//
//// IPLoc 自动识别ipv4/ipv6，再查它的地址
//func IPLoc(ip string) (countryCode, city string) {
//	ipInst := net.ParseIP(ip)
//	if ipInst == nil { // ip format invalid
//		return "", ""
//	}
//	if ipInst.To4() != nil {
//		return IPv4Loc(ip)
//	} else {
//		return IPv6Loc(ip)
//	}
//}
