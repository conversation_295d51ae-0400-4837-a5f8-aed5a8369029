package utility

import (
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/consts"
	"gtcms/internal/service"
)

var FieldCreator string = "creater"
var FieldSiteID string = "site_id"
var FieldID string = "id"
var FieldRoleLevel string = "role_level"

func AccountFilter(m *gdb.Model) *gdb.Model {
	ctx := m.GetCtx()
	ac, err := service.Utility().GetAccountRole(ctx)
	if err != nil {
		return m
	}
	if consts.RoleLevelAdmin == ac.RoleLevel {
		return m
	}

	isExist, err := m.<PERSON>(FieldCreator)
	if err != nil {
		return m
	}
	if !isExist {
		return m
	}

	switch ac.RoleLevel {
	case consts.RoleLevelSiteLeader:
		//站长能看到自己创建的 以及所有自己下属创建的
		var accountIDs []uint
		accountIDs = append(accountIDs, ac.SelfId)
		accountIDs = append(accountIDs, ac.ChildIds...)
		m = m.WhereIn(FieldID, accountIDs)
	case consts.RoleLevelSiteMember:
		//站员能看到自己的
		m = m.WherePri(ac.SelfId)
	case consts.RoleLevelDomainAdmin:
		// 域名管理员只能看到非管理员的
		accountId := service.Account().GetAdminAccount(ctx)
		m = m.WhereNotIn(FieldID, accountId)
	default:
		g.Log().Line().Warning(ctx, "RoleLevel=%d", ac.RoleLevel)
	}

	return m
}

func SiteFilter(m *gdb.Model) *gdb.Model {
	ctx := m.GetCtx()
	ac, err := service.Utility().GetAccountRole(ctx)
	if err != nil {
		return m
	}

	if consts.RoleLevelAdmin == ac.RoleLevel {
		return m
	}

	isExist, err := m.HasField(FieldCreator)
	if err != nil {
		return m
	}
	if !isExist {
		return m
	}

	isExist, err = m.HasField(FieldID)
	if err != nil {
		return m
	}
	if !isExist {
		return m
	}

	switch ac.RoleLevel {
	case consts.RoleLevelSiteLeader:
		//站长能看到自己创建的 以及所有自己下属创建的
		var accountIDs []uint
		accountIDs = append(accountIDs, ac.SelfId)
		accountIDs = append(accountIDs, ac.ChildIds...)
		m = m.WhereIn(FieldCreator, accountIDs)
	case consts.RoleLevelSiteMember:
		//站员能看到自己创建的 或者 站长勾选的
		m = m.Where(FieldCreator, ac.SelfId)
		siteIDs, _ := service.AccountSiteLink().GetSiteIDsByAccount(ctx, ac.SelfId)
		if !g.IsEmpty(siteIDs) {
			m = m.WhereOrIn(FieldID, siteIDs)
		}
	case consts.RoleLevelDomainAdmin:
		m = m.Where(FieldCreator, ac.SelfId)
	default:
		g.Log().Line().Warning(ctx, "RoleLevel=%d", ac.RoleLevel)
		return m
	}

	return m
}

func GroupFilter(m *gdb.Model) *gdb.Model {
	ctx := m.GetCtx()
	ac, err := service.Utility().GetAccountRole(ctx)
	if err != nil {
		return m
	}
	if consts.RoleLevelAdmin == ac.RoleLevel {
		return m
	}

	isExist, err := m.HasField(FieldCreator)
	if err != nil {
		return m
	}
	if !isExist {
		return m
	}

	var accountIDs []uint
	switch ac.RoleLevel {
	case consts.RoleLevelSiteLeader:
		//站长能看到自己创建的 以及所有自己下属创建的
		accountIDs = append(accountIDs, ac.SelfId)
		accountIDs = append(accountIDs, ac.ChildIds...)
		m = m.WhereIn(FieldCreator, accountIDs)
	case consts.RoleLevelSiteMember:
		//站员能看到自己创建的 或者 站长勾选的
		m = m.Where(FieldCreator, ac.SelfId)
		groupIDs, _ := service.AccountSiteLink().GetGroupIDsByAccount(ctx, ac.SelfId)
		if !g.IsEmpty(groupIDs) {
			m = m.WhereOrIn(FieldID, groupIDs)
		}
	default:
		g.Log().Line().Warning(ctx, "RoleLevel=%d", ac.RoleLevel)
		return m
	}

	return m
}

func CreatorFilter(m *gdb.Model) *gdb.Model {
	ctx := m.GetCtx()
	ac, err := service.Utility().GetAccountRole(ctx)
	if err != nil {
		return m
	}

	if consts.RoleLevelAdmin == ac.RoleLevel {
		return m
	}

	isExist, err := m.HasField(FieldCreator)
	if err != nil {
		return m
	}
	if !isExist {
		return m
	}

	var accountIDs []uint
	switch ac.RoleLevel {
	case consts.RoleLevelSiteLeader:
		//站长能看到自己创建的 以及所有自己下属创建的
		accountIDs = append(accountIDs, ac.SelfId)
		accountIDs = append(accountIDs, ac.ChildIds...)
		m = m.WhereIn(FieldCreator, accountIDs)
	case consts.RoleLevelSiteMember:
		//站员能看到自己创建的 或者站长的
		accountIDs = append(accountIDs, ac.SelfId)
		accountIDs = append(accountIDs, ac.ParentId)
		m = m.WhereIn(FieldCreator, accountIDs)
	default:
		g.Log().Line().Warning(ctx, "RoleLevel=%d", ac.RoleLevel)
		return m
	}

	return m
}

func RoleFilter(m *gdb.Model) *gdb.Model {
	ctx := m.GetCtx()
	ac, err := service.Utility().GetAccountRole(ctx)
	if err != nil {
		return m
	}

	if consts.RoleLevelAdmin == ac.RoleLevel {
		return m
	}

	m = m.WhereGTE(FieldRoleLevel, ac.RoleLevel)
	return m
}
