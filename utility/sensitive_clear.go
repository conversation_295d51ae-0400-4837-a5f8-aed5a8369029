package utility

import (
	"strings"
	"unicode"
)

// 对银行卡进行脱敏，保留后四位，其他用星号替代
func MaskBankCard(bankCard string) string {
	if len(bankCard) < 5 {
		return bankCard
	}
	lastName := bankCard[len(bankCard)-4:]
	maskedName := strings.Repeat("*", len(bankCard)-4)
	return maskedName + lastName
}

func MaskUserName(username string) string {
	if isContainsChinese(username) {
		return maskChineseName(username)
	}

	return maskEnglishName(username)
}

// 判断字符串中是否包含中文字符
func isContainsChinese(str string) bool {
	for _, char := range str {
		if unicode.Is(unicode.Han, char) {
			return true
		}
	}
	return false
}

// 中文用户名进行脱敏，保留第一个字符
func maskChineseName(name string) string {
	// 如果名字为空或只有一个字符，不进行脱敏
	if len(name) <= 1 {
		return name
	}
	// 提取姓氏的第一个字符
	firstName := string([]rune(name)[:1])
	// 将剩余字符替换为星号
	maskedName := firstName + strings.Repeat("*", len([]rune(name))-1)
	return maskedName
}

// 英文用户名进行脱敏，仅保留前三个
func maskEnglishName(username string) string {
	if len(username) <= 3 {
		return username
	}
	//return string(username[0]) + "*" + string(username[len(username)-1])
	// 提取姓氏的第一个字符
	firstName := string(username[:3])
	// 将剩余字符替换为星号
	maskedName := firstName + strings.Repeat("*", len(username)-3)
	return maskedName
}
