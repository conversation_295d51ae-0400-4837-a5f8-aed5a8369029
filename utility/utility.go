package utility

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/util/gutil"
	"math"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/i18n/gi18n"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gregex"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
)

func WhereLike(in string) string {
	res := strings.Builder{}
	res.WriteString("%")
	res.WriteString(in)
	res.WriteString("%")
	return res.String()
}

// GetFloat64Format 将float64浮点数保留后二位，且四舍五入
func GetFloat64Format(in float64) float64 {
	return gvar.New(fmt.Sprintf("%.2f", math.Round(in*100)/100)).Float64()
}

type VarPointer interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~string
}

// GetVarPointer 获取变量的指针变量类型
func GetVarPointer[T VarPointer](v T) *T {
	vNew := v
	return &vNew
}

// Map 将切片转map
func Map[T any, S comparable](data []T, f func(row T) S) map[S]T {
	res := make(map[S]T, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res[f(data[i])] = data[i]
	}
	return res
}

// MapPr 将切片转map(返回指针)
func MapPr[T any, S comparable](data []T, f func(row T) S) map[S]*T {
	res := make(map[S]*T, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res[f(data[i])] = &data[i]
	}
	return res
}

// MapDecide 从一个切片获取一个对应map
func MapDecide[T any, S1 comparable, S2 comparable](data []T, f func(row T) (S1, S2)) map[S1]S2 {
	res := make(map[S1]S2, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		s1, s2 := f(data[i])
		res[s1] = s2
	}
	return res
}

// Slice 将一个切片的结构体转成切片
func Slice[T any, S comparable](data []T, f func(row T) S) []S {
	res := make([]S, 0, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res = append(res, f(data[i]))
	}
	return res
}

// SliceByMap 将一个map转成切片
func SliceByMap[T any, S1 comparable, S2 comparable](data map[S1]T, f func(row T) S2) []S2 {
	res := make([]S2, 0, len(data))
	if data == nil {
		return res
	}
	for _, row := range data {
		res = append(res, f(row))
	}
	return res
}

// MapSlice 将切片转map[]slice
func MapSlice[T any, S comparable](data []T, f func(row T) S) map[S][]*T {
	res := make(map[S][]*T, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res[f(data[i])] = append(res[f(data[i])], &data[i])
	}
	return res
}

// ComparisonSet t1和t2对比,不同的话, 使用t2返回一个新值
func ComparisonSet[T comparable](t1 T, t2 *T) (T, bool) {
	if t2 == nil {
		return t1, false
	}
	if t1 != *t2 {
		t1 = *t2
		return t1, true
	}
	return t1, false
}

// PointerSet 将指针转成非指针
func PointerSet[T comparable](t1 *T) T {
	var res T
	if t1 != nil {
		res = *t1
	}
	return res
}

// PointerSetSlice 判断切片是否nil,是则初始化并返回, 不是则原样返回
func PointerSetSlice[T comparable](t1 []T) []T {
	var res []T
	if t1 == nil {
		res = make([]T, 0)
	} else {
		res = t1
	}
	return res
}

// ReplaceErrMsg 转换错误提示
func ReplaceErrMsg(ctx context.Context, errMsg string, replace ...string) string {
	if len(replace) < 1 || len(errMsg) < 1 {
		return errMsg
	}
	replaceMap := make(map[string]string, 0)
	l := len(replace)
	if l > 0 {
		replaceMap["{field}"] = replace[0]
	}
	if l > 1 {
		replaceMap["{value}"] = replace[1]
	}
	if l > 2 {
		replaceMap["{pattern}"] = replace[2]
	}
	if l > 2 {
		replaceMap["{attribute}"] = replace[3]
	}

	errMsg = gstr.ReplaceByMap(gi18n.T(ctx, errMsg), replaceMap)
	errMsg, _ = gregex.ReplaceString(`\s{2,}`, ` `, errMsg)
	return errMsg
}

// RemoveSymbols 复制了gf的utils包的函数
func RemoveSymbols(s string) string {
	var b = make([]rune, 0, len(s))
	for _, c := range s {
		if c > 127 {
			b = append(b, c)
		} else if (c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') {
			b = append(b, c)
		}
	}
	return string(b)
}

// EqualFoldWithoutChars checks string `s1` and `s2` equal case-insensitively,
// with/without chars '-'/'_'/'.'/' '.
// EqualFoldWithoutChars 复制了gf的utils包的函数
func EqualFoldWithoutChars(s1, s2 string) bool {
	return strings.EqualFold(RemoveSymbols(s1), RemoveSymbols(s2))
}

func EqualFoldWithoutCharsSlice(s []string, s2 string) (string, bool) {
	for _, s1 := range s {
		if EqualFoldWithoutChars(s1, s2) {
			return s1, true
		}
	}
	return "", false
}

func MergeSliceAndSort(nums1, nums2 []int) []int {
	m := len(nums1)
	n := len(nums2)
	if m == 0 && n == 0 {
		return []int{}
	}

	sort.Ints(nums1)
	sort.Ints(nums2)
	if m == 0 {
		return nums2
	}
	if n == 0 {
		return nums1
	}

	sortedArr := append(nums1, nums2...)
	fmt.Println(sortedArr)
	sort.Ints(sortedArr)
	fmt.Println(sortedArr)
	// 慢指针i用于记录不重复元素的位置
	i := 0
	for j := 1; j < len(sortedArr); j++ {
		if sortedArr[j] != sortedArr[i] {
			i++
			sortedArr[i] = sortedArr[j]
		}
	}
	fmt.Println(sortedArr)
	fmt.Println(sortedArr[:i+1])
	return sortedArr[:i+1]

	// sorted := make([]int, 0, int(math.Min(float64(m), float64(n))))
	// p1, p2 := 0, 0
	// for {
	//	if p1 == m {
	//		sorted = append(sorted, nums2[p2:]...)
	//		break
	//	}
	//	if p2 == n {
	//		sorted = append(sorted, nums1[p1:]...)
	//		break
	//	}
	//
	//	if nums1[p1] == nums2[p2] {
	//		p1++
	//		continue
	//	}
	//
	//	if nums1[p1] < nums2[p2] {
	//		sorted = append(sorted, nums1[p1])
	//		p1++
	//	} else {
	//		sorted = append(sorted, nums2[p2])
	//		p2++
	//	}
	// }

	// return sorted
}

func PageLimitModify(current, pageSize *int) {
	if current != nil && *current <= 0 {
		*current = 1
	}

	if pageSize != nil && (*pageSize <= 0 || *pageSize > 1000) {
		*pageSize = 50
	}
}

// N 随机返回 t-m 到 t的范围
func N(t int, m int) int64 {
	return int64(grand.N(t-m, t))
}

// 当前是否是线上生产环境
func IsProd(ctx context.Context) bool {
	gv, err := g.Cfg().Get(ctx, "server.env")
	return err == nil && gv.String() == "prod"
}

func GetDiffKeyValue(old, new interface{}) (attrDiff, oldDiff, newDiff string) {
	filterFields := map[string]struct{}{
		"Id":            struct{}{},
		"CreateAccount": struct{}{},
		"CreateTime":    struct{}{},
		"UpdateAccount": struct{}{},
		"DeleteTime":    struct{}{},
		"UpdateTime":    struct{}{},
		"SupplierId":    struct{}{},
	}

	//arr := gconv.Convert(old, "[]interface")
	//fmt.Println(arr)

	mapStr1 := gconv.MapStrStr(old)
	mapStr2 := gconv.MapStrStr(new)
	mapAnyOld := make(g.MapAnyAny, len(mapStr1))
	for k, v := range mapStr1 {
		//首字母转为大写
		k := strings.ToUpper(k[:1]) + k[1:]
		mapAnyOld[k] = v
	}
	mapAnyNew := make(g.MapAnyAny, len(mapStr2))
	for k, v := range mapStr2 {
		k := strings.ToUpper(k[:1]) + k[1:]
		mapAnyNew[k] = v
	}

	sortMapOld := gmap.NewTreeMapFrom(gutil.ComparatorString, mapAnyOld)
	sortMapNew := gmap.NewTreeMapFrom(gutil.ComparatorString, mapAnyNew)

	isAdd := g.IsEmpty(mapAnyOld) && !g.IsEmpty(mapAnyNew)
	isDel := !g.IsEmpty(mapAnyOld) && g.IsEmpty(mapAnyNew)
	isEdit := !g.IsEmpty(mapAnyOld) && !g.IsEmpty(mapAnyNew)

	var modifyAttrs, oldAttrsVal, newAttrsVal []string
	var emptyVal string = ""

	if isAdd {
		sortMapNew.IteratorAsc(func(key, value interface{}) bool {
			k := gconv.String(key)
			nv := gconv.String(value)
			if _, ok := filterFields[k]; ok {
				return true
			}
			modifyAttrs = append(modifyAttrs, k)
			newAttrsVal = append(newAttrsVal, nv)
			return true
		})
		attrDiff, _ = gjson.EncodeString(modifyAttrs)
		newDiff, _ = gjson.EncodeString(newAttrsVal)
		return
	}

	if isDel {
		sortMapOld.IteratorAsc(func(key, value interface{}) bool {
			k := gconv.String(key)
			nv := gconv.String(value)
			if _, ok := filterFields[k]; ok {
				return true
			}
			modifyAttrs = append(modifyAttrs, k)
			oldAttrsVal = append(oldAttrsVal, nv)
			return true
		})
		attrDiff, _ = gjson.EncodeString(modifyAttrs)
		oldDiff, _ = gjson.EncodeString(oldAttrsVal)
		return
	}

	if isEdit {
		sortMapOld.IteratorAsc(func(key, value interface{}) bool {
			k := gconv.String(key)
			ov := gconv.String(value)
			if _, ok := filterFields[k]; ok {
				return true
			}
			nvVar := sortMapNew.GetVar(k)
			if nvVar.Val() == nil {
				//旧记录有 新记录没有
				modifyAttrs = append(modifyAttrs, k)
				oldAttrsVal = append(oldAttrsVal, ov)
			} else {
				//两者都有 且值不等 再继续加
				nv := nvVar.String()
				if !compareInterfaces(ov, nv) {
					modifyAttrs = append(modifyAttrs, k)
					oldAttrsVal = append(oldAttrsVal, ov)
					newAttrsVal = append(newAttrsVal, nv)
				}
			}
			return true
		})

		sortMapNew.IteratorAsc(func(key, value interface{}) bool {
			// 计算新加的字段
			k := gconv.String(key)
			nv := gconv.String(value)
			if _, ok := filterFields[k]; ok {
				return true
			}
			lvVar := sortMapOld.GetVar(k)
			if lvVar.Val() == nil {
				modifyAttrs = append(modifyAttrs, k)
				oldAttrsVal = append(oldAttrsVal, emptyVal)
				newAttrsVal = append(newAttrsVal, nv)
			}
			return true
		})

		attrDiff, _ = gjson.EncodeString(modifyAttrs)
		oldDiff, _ = gjson.EncodeString(oldAttrsVal)
		newDiff, _ = gjson.EncodeString(newAttrsVal)
	}

	return
}

func compareInterfaces(a, b interface{}) bool {
	if a == nil || b == nil {
		return false
	}

	// 获取接口的值和类型信息
	aValue := reflect.ValueOf(a)
	bValue := reflect.ValueOf(b)

	// 如果类型不同，返回 false
	if aValue.Type() != bValue.Type() {
		return false
	}

	// 比较值
	return reflect.DeepEqual(a, b)
}

// 获取一天的周一所在日期
func GetMondayOfWeek(input *gtime.Time) *gtime.Time {
	// 获取当前时间所在的星期一
	weekday := input.Weekday()

	// 计算距离周一的天数差
	daysUntilMonday := int(time.Monday - weekday)
	if daysUntilMonday > 0 {
		daysUntilMonday -= 7
	}

	// 计算周一的日期
	monday := input.AddDate(0, 0, daysUntilMonday)
	return monday
}

// 获取下周一的0点0分0秒
func GetNextMonday(t time.Time) time.Time {
	weekday := int(t.Weekday())              // 计算当前时间是周几
	daysUntilNextMonday := (8 - weekday) % 7 // 计算距离下周一的天数差
	// 计算下周一的时间
	nextMonday := t.AddDate(0, 0, daysUntilNextMonday)
	// 设置时间为0点0分0秒
	return time.Date(nextMonday.Year(), nextMonday.Month(), nextMonday.Day(), 0, 0, 0, 0, nextMonday.Location())
}

// If 三目运算
func If[T any](condition bool, trueVal T, falseVal T) (rs T) {
	if condition {
		rs = trueVal
	} else {
		rs = falseVal
	}
	return
}

// 检查切片里的每一项是否都满足条件
func Every[T any](ts []T, f func(int, T) bool) bool {
	l := len(ts)
	for i := 0; i < l; i++ {
		if !f(i, ts[i]) {
			return false
		}
	}
	return true
}

// 检查切片里是否有项满足条件
func Some[T any](ts []T, f func(int, T) bool) bool {
	l := len(ts)
	for i := 0; i < l; i++ {
		if f(i, ts[i]) {
			return true
		}
	}
	return false
}

func GetRealType(in interface{}) reflect.Type {
	var reflectValue reflect.Value
	if v, ok := in.(reflect.Value); ok {
		reflectValue = v
		in = v.Interface()
	} else {
		reflectValue = reflect.ValueOf(in)
	}
	reflectKind := reflectValue.Kind()
	// If it is a pointer, we should find its real data type.
	for reflectKind == reflect.Ptr {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}
	return reflect.TypeOf(reflectValue)
}

func GetRealValue(in interface{}) reflect.Value {
	var reflectValue reflect.Value
	if v, ok := in.(reflect.Value); ok {
		reflectValue = v
		in = v.Interface()
	} else {
		reflectValue = reflect.ValueOf(in)
	}
	reflectKind := reflectValue.Kind()
	// If it is a pointer, we should find its real data type.
	for reflectKind == reflect.Ptr {
		reflectValue = reflectValue.Elem()
		reflectKind = reflectValue.Kind()
	}
	return reflectValue
}
