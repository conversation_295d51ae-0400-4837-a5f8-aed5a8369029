package utility

import "testing"

func TestMaskUserName(t *testing.T) {
	type args struct {
		username string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test1",
			args: args{
				username: "张三",
			},
			want: "张*",
		},
		{
			name: "Test2",
			args: args{
				username: "John chen",
			},
			want: "Joh******",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MaskUserName(tt.args.username); got != tt.want {
				t.Errorf("MaskUserName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMaskBankCard(t *testing.T) {
	type args struct {
		bankCard string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Test1",
			args: args{
				bankCard: "***************",
			},
			want: "***********3121",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MaskBankCard(tt.args.bankCard); got != tt.want {
				t.Errorf("MaskBankCard() = %v, want %v", got, tt.want)
			}
		})
	}
}
