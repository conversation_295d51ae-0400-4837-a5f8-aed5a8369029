package utility

import (
	"github.com/gogf/gf/v2/os/gtime"
	"time"
)

func GetDateHour() (string, int) {
	t := time.Now()
	//等1秒多 保证超过 整点
	//time.Sleep(1100 * time.Millisecond)
	//zeroTime := time.Hour(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	//fmt.Printf("hour = %d", gtime.Now().StartOfHour().Unix())
	return t.Format("2006-01-02"), t.Hour()
}

func GetLastDate() string {
	t := time.Now().AddDate(0, 0, -1)
	//等1秒多 保证超过 整点
	//time.Sleep(1100 * time.Millisecond)
	//zeroTime := time.Hour(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	//fmt.Printf("hour = %d", gtime.Now().StartOfHour().Unix())
	return t.Format("2006-01-02")
}

func GetLastHour() (lastHour, curHour int64) {
	//处理定时器偏差的问题  可能定时器超时时间无限接近整小时
	// 获取的当前时间 分钟必须 在 [0 1]之间
	for {
		if gtime.Now().Minute() >= 59 && gtime.Now().Second() >= 58 {
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	now := gtime.Now().StartOfHour()
	lastHour = now.Add(-time.Hour).UnixMilli()
	curHour = now.UnixMilli()
	return
}

func GetTodayRange() (todayBegin, todayEnd int64) {
	//处理定时器偏差的问题  可能定时器超时时间无限接近整小时
	// 获取的当前时间 分钟必须 在 [0 1]之间
	for {
		if gtime.Now().Minute() > 59 && gtime.Now().Second() > 58 {
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	now := gtime.Now().StartOfDay()
	todayEnd = now.Add(24 * time.Hour).UnixMilli()
	todayBegin = now.UnixMilli()
	return
}

func GetLastTwoDaysDate() (string, string) {
	t := time.Now()
	return t.Format("2006-01-02"), t.Add(-24 * time.Hour).Format("2006-01-02")
}

func GetLastTwoDayRange() (begin, end int64) {
	//处理定时器偏差的问题  可能定时器超时时间无限接近整小时
	// 获取的当前时间 分钟必须 在 [0 1]之间
	for {
		if gtime.Now().Minute() > 59 && gtime.Now().Second() > 58 {
			time.Sleep(100 * time.Millisecond)
		} else {
			break
		}
	}

	now := gtime.Now().StartOfDay()
	begin = now.Add(-24 * time.Hour).UnixMilli()
	end = now.UnixMilli()
	return
}
