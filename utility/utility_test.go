package utility

import (
	"gtcms/internal/consts"
	"reflect"
	"testing"
)

func TestGetFloat64Format(t *testing.T) {
	in := 1.23456
	out := GetFloat64Format(in)
	expect := 1.23
	if out != expect {
		t.<PERSON><PERSON><PERSON>("GetFloat64Format error, in:%f, out:%f, expect:%f", in, out, expect)
	}
	in = 1.23756
	out = GetFloat64Format(in)
	expect = 1.24
	if out != expect {
		t.<PERSON><PERSON><PERSON>("GetFloat64Format error, in:%f, out:%f, expect:%f", in, out, expect)
	}
}

type TEntity struct {
	Id   uint
	Name string
}

func TestMap(t *testing.T) {
	in := []TEntity{
		{
			Id:   1,
			Name: "a",
		},
		{
			Id:   2,
			Name: consts.SportTypeBasketball,
		},
	}
	out := Map(in, func(row TEntity) uint {
		return row.Id
	})

	if out[1].Id != 1 || out[1].Name != "a" {
		t.<PERSON>ail()
	}

}

func TestComparisonSet(t *testing.T) {
	var (
		c int
		d *int
	)
	c = 3
	d1 := 4
	d = &d1
	c, res2 := ComparisonSet(c, d)
	if res2 != true {
		t.Fatal("res is false")
	}
	if c != 4 {
		t.Fatal("c not is 4")
	}
}

func TestMergeSliceAndSort(t *testing.T) {
	type args struct {
		nums1 []int
		nums2 []int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "TestMergeSliceAndSort TestCase1: 空数组",
			args: args{
				nums1: []int{},
				nums2: []int{},
			},
			want: []int{},
		},

		{
			name: "TestMergeSliceAndSort TestCase3: 空数组",
			args: args{
				nums1: []int{},
				nums2: []int{9, 12, 32, 90, 2, 3},
			},
			want: []int{2, 3, 9, 12, 32, 90},
		},

		{
			name: "TestMergeSliceAndSort TestCase1: 空数组",
			args: args{
				nums1: []int{2, 33, 9, 7, 8, 22, 26},
				nums2: []int{3, 2, 1, 26, 22, 8, 25, 16, 12, 7},
			},
			want: []int{1, 2, 3, 7, 8, 9, 12, 16, 22, 25, 26, 33},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MergeSliceAndSort(tt.args.nums1, tt.args.nums2); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MergeSliceAndSort() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDiffKeyValue(t *testing.T) {
	type TestSubInfo struct {
		SubName string `json:"SubName"  `
		SubId   uint   `json:"SubId"       `
	}
	type TestBankcard struct {
		BankName      string `json:"bankName"       description:"银行卡名称"`
		BankId        uint   `json:"bankId"        description:"银行id（所属银行）"`
		AccountNum    string `json:"accountNum"    description:"银行卡卡号（账号）"`
		SubBank       string `json:"subBank"       description:"分行名称（默认为空字符串）"`
		Title         string `json:"title"         description:"别名"`
		CreateTime    int64  `json:"createTime"    description:"添加时间"`
		CreateAccount string `json:"createAccount" description:"添加者账号"`
		Sub           TestSubInfo
	}

	type args struct {
		old interface{}
		new interface{}
	}
	tests := []struct {
		name            string
		args            args
		wantModifyAttrs []string
		wantOldAttrsVal []string
		wantNewAttrsVal []string
	}{
		{
			name: "Test1",
			args: args{
				old: TestBankcard{
					BankName:      "name1",
					BankId:        56,
					AccountNum:    "AccountNum",
					SubBank:       "SubBank1",
					CreateTime:    333,
					CreateAccount: "",
					//Sub: TestSubInfo{
					//	SubName: "subBank1",
					//	SubId:   1,
					//},
				},
				new: TestBankcard{
					BankName:      "name2",
					BankId:        58,
					AccountNum:    "AccountNum",
					SubBank:       "SubBank1",
					Title:         "tile",
					CreateAccount: "",
					//Sub: TestSubInfo{
					//	SubName: "subBank2",
					//	SubId:   2,
					//},
				},
			},
			wantModifyAttrs: []string{"title", "createTime", "bankName", "bankId"},
			wantOldAttrsVal: []string{"name1", "56", "\"\"", "333"},
			wantNewAttrsVal: []string{"name2", "58", "tile", "0"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotModifyAttrs, gotOldAttrsVal, gotNewAttrsVal := GetDiffKeyValue(tt.args.old, tt.args.new)
			if !reflect.DeepEqual(gotModifyAttrs, tt.wantModifyAttrs) {
				t.Errorf("GetDiffKeyValue() gotModifyAttrs = %v, want %v", gotModifyAttrs, tt.wantModifyAttrs)
			}
			if !reflect.DeepEqual(gotOldAttrsVal, tt.wantOldAttrsVal) {
				t.Errorf("GetDiffKeyValue() gotOldAttrsVal = %v, want %v", gotOldAttrsVal, tt.wantOldAttrsVal)
			}
			if !reflect.DeepEqual(gotNewAttrsVal, tt.wantNewAttrsVal) {
				t.Errorf("GetDiffKeyValue() gotNewAttrsVal = %v, want %v", gotNewAttrsVal, tt.wantNewAttrsVal)
			}
		})
	}
}
