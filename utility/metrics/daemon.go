package metrics

import (
	"context"
	"git.go123.dev/lib/golang/watcher.git"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gutil"
	"gtcms/internal/service"
	"time"
)

// 收集各种需要定时统计数据的任务
func daemon() {
	//tickerFast := time.NewTicker(5 * time.Second)
	//tickerMid := time.NewTicker(30 * time.Second)
	tickerSlow := time.NewTicker(5 * time.Second)

	gutil.Go(gctx.New(), func(ctx context.Context) {
		for {
			select {
			//case <-tickerFast.C:
			//	vm, _ := mem.VirtualMemory()
			//	watcher.Gauge(GaugeMem, vm.UsedPercent)
			//	loadAvg, _ := load.Avg()
			//	watcher.Gauge(GaugeLoad1, loadAvg.Load1)
			//	watcher.Gauge(GaugeLoad5, loadAvg.Load5)
			//	watcher.Gauge(GaugeLoad15, loadAvg.Load15)
			//case <-tickerMid.C:
			//	rootStat, err := disk.Usage("/")
			//	if err == nil {
			//		watcher.Gauge(GaugeDiskUsage, rootStat.UsedPercent, "/")
			//	}
			//	diskStat, err := disk.Usage("/data")
			//	if err == nil {
			//		watcher.Gauge(GaugeDiskUsage, diskStat.UsedPercent, "/data")
			//	}
			//	pct, _ := cpu.Percent(0, false)
			//	if len(pct) > 0 {
			//		watcher.Gauge(GaugeCpuPercent, pct[0])
			//	}
			case <-tickerSlow.C:
				//count, _ := cpu.Counts(true)
				//watcher.Gauge(GaugeCores, float64(count))
				//iostat, _ := netstat.IOCounters(true)
				//if len(iostat) > 0 {
				//	for _, io := range iostat {
				//		watcher.Gauge(GaugeNetByteIn, float64(io.BytesRecv), io.Name)
				//		watcher.Gauge(GaugeNetByteOut, float64(io.BytesSent), io.Name)
				//		watcher.Gauge(GaugeNetPktIn, float64(io.PacketsRecv), io.Name)
				//		watcher.Gauge(GaugeNetPktOut, float64(io.PacketsSent), io.Name)
				//	}
				//}
				watcher.Gauge(GaugeHeartbeat, service.Heartbeat().CheckSiteHeartbeat(ctx))
			}
		}
	}, nil)
}
