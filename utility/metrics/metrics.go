package metrics

import (
	watcher "git.go123.dev/lib/golang/watcher.git"
)

const (
	// CounterHttpReq       = "httpreq_status"
	// CounterHttpReqDomain = "domainreq_domain"
	// CounterSpider        = "spider_name"
	// CounterTrigger    = "trigger_event_user_tasksub"
	// CounterTransStats = "transstats_event_user"
	CounterCollect = "collect_task"
)

const (
	GaugeMem        = "memusage"
	GaugeLoad1      = "load1"
	GaugeLoad5      = "load5"
	GaugeLoad15     = "load15"
	GaugeCpuPercent = "cpupercent"
	GaugeCores      = "cores" // 核数
	GaugeDiskUsage  = "diskusage_path"
	GaugeNetByteIn  = "netbytein_name" // name:网卡名
	GaugeNetByteOut = "netbyteout_name"
	GaugeNetPktIn   = "netpktin_name" // name:网卡名
	GaugeNetPktOut  = "netpktout_name"
	GaugeHeartbeat  = "heartbeat" // 心跳
)

const (
// HistHttpReqCost = "httpreqcost_path_status"
)

func init() {
	watcher.Init("gt", "aapi", []string{GaugeHeartbeat}, []string{
		CounterCollect,
	}, []watcher.HistInfo{
		//{
		//	Name:    HistHttpReqCost,
		//	Buckets: []float64{20, 50, 100, 200, 300}, // 单位 ms
		//},
	})
	//daemon()
}
