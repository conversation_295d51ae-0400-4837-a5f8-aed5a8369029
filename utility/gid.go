package utility

import (
	"bytes"
	"errors"
	"strconv"
	"sync"
)

const (
	sequenceMax = 999 // 最大值 999
)

// Gid 生成唯一数字
// 12位时间格式(yymdhis)+2位机器号+用户数字后2位+3位随机数
type Gid struct {
	workerId  string // 机器号
	timestamp int64
	sequence  int // 序列号
	data      *bytes.Buffer
	mu        sync.Mutex
}

func NewGid(workerId int) (*Gid, error) {
	if workerId <= 0 || workerId > 99 {
		return nil, errors.New("NewGid:workerId must by 1~99")
	}
	workerS := ""
	if workerId < 10 {
		workerS += "0"
	}
	workerS += strconv.Itoa(workerId)
	return &Gid{
		workerId: workerS,
		data:     bytes.NewBuffer(make([]byte, 0, 20)),
	}, nil
}
