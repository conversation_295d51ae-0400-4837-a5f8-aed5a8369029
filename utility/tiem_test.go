package utility

import (
	"testing"
)

func TestGetDateHour(t *testing.T) {
	tests := []struct {
		name  string
		want  string
		want1 int
	}{
		{
			name:  "Test1",
			want:  "2024-01-11",
			want1: 16,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := GetDateHour()
			if got != tt.want {
				t.<PERSON><PERSON>("GetDateHour() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.<PERSON><PERSON>("GetDateHour() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetLastHour(t *testing.T) {
	tests := []struct {
		name         string
		wantLastHour int64
		wantCurHour  int64
	}{
		{
			name:         "Test1",
			wantLastHour: 1704960000000,
			wantCurHour:  1704963600000,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotLastHour, gotCurHour := GetLastHour()
			if gotLastHour != tt.wantLastHour {
				t.<PERSON><PERSON><PERSON>("GetLastHour() gotLastHour = %v, want %v", gotLastHour, tt.wantLastHour)
			}
			if gotCurHour != tt.wantCurHour {
				t.Errorf("GetLastHour() gotCurHour = %v, want %v", gotCurHour, tt.wantCurHour)
			}
		})
	}
}
