package main

//
//import (
//	"bufio"
//	"bytes"
//	"fmt"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gctx"
//	"github.com/nfnt/resize"
//	"image"
//	"image/gif"
//	"image/jpeg"
//	"image/png"
//	"io"
//	"log"
//	"net/http"
//	"net/url"
//	"os"
//	"strings"
//)
//
//var ctx = gctx.New()
//var file *os.File
//var fileSuccess *os.File
//
//func init() {
//	file, _ = os.OpenFile("output.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
//	fileSuccess, _ = os.OpenFile("success.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
//}
//
//// **ResaveImg 处理图片**
//func ResaveImg(originUrl string) {
//	resp, err := http.DefaultClient.Get(originUrl)
//	if err != nil {
//		g.Log().Error(ctx, "ResaveImg: http GET error:", err)
//		return
//	}
//	defer resp.Body.Close()
//
//	// 读取图片数据
//	b, err := io.ReadAll(resp.Body)
//	if err != nil {
//		g.Log().Error(ctx, "ResaveImg: ReadAll error:", err)
//		return
//	}
//	if len(b) == 0 {
//		g.Log().Error(ctx, "ResaveImg: empty image data")
//		return
//	}
//
//	rr := bytes.NewBuffer(b)
//	fileType := http.DetectContentType(b) // 获取 MIME 类型
//
//	// **处理 GIF，直接存储**
//	if strings.HasSuffix(strings.ToLower(originUrl), ".gif") || fileType == "image/gif" {
//		path, err := saveGIF(rr, originUrl)
//		if err != nil {
//			g.Log().Error(ctx, "ResaveImg: saveGIF error:", err)
//			file.WriteString(originUrl + "\n")
//		} else {
//			g.Log().Info(ctx, "ResaveImg: saveGIF finish:", path)
//			fileSuccess.WriteString(originUrl + "\n")
//		}
//		return
//	}
//
//	// **处理 PNG/JPEG**
//	compressedImg, err := compressImage(rr)
//	if compressedImg == nil || err != nil {
//		g.Log().Error(ctx, "ResaveImg: compressImage error:", err)
//		compressedImg = &CompressedImage{Buf: rr}
//	}
//
//	path, err := saveResizeImage(compressedImg, originUrl)
//	if err != nil {
//		g.Log().Error(ctx, "ResaveImg: saveResizeImage error:", err)
//		file.WriteString(originUrl + "\n")
//	} else {
//		g.Log().Info(ctx, "ResaveImg: saveResizeImage finish:", path)
//		fileSuccess.WriteString(originUrl + "\n")
//	}
//}
//
//// **GIF 直接存储**
//func saveGIF(buf *bytes.Buffer, originUrl string) (string, error) {
//	u, _ := url.Parse(originUrl)
//	fileName := u.RequestURI()
//	if strings.HasPrefix(fileName, "/") {
//		fileName = fileName[1:]
//	}
//
//	newPath := "./img/" + fileName
//	if err := os.MkdirAll("img", os.ModePerm); err != nil {
//		return "", fmt.Errorf("creating img directory failed: %v", err)
//	}
//
//	outFile, err := os.Create(newPath)
//	if err != nil {
//		return "", fmt.Errorf("creating file failed: %v", err)
//	}
//	defer outFile.Close()
//
//	_, err = outFile.Write(buf.Bytes())
//	if err != nil {
//		return "", fmt.Errorf("writing GIF to file failed: %v", err)
//	}
//	return newPath, nil
//}
//
//// **图片压缩**
//type CompressedImage struct {
//	Buf    *bytes.Buffer
//	Width  int
//	Height int
//	Size   int64
//}
//
//func compressImage(imgst io.Reader) (*CompressedImage, error) {
//	img, imgType, err := image.Decode(imgst)
//	if err != nil {
//		return nil, err
//	}
//	bounds := img.Bounds()
//
//	// **调整尺寸**
//	var xvalue, yvalue uint
//	xratio := uint(bounds.Dx()) / 100
//	yratio := uint(bounds.Dy()) / 100
//	minratio := uint(1)
//	if xratio >= 1 && yratio >= 1 {
//		minratio = min(xratio, yratio)
//	}
//	xvalue = uint(bounds.Dx()) / minratio
//	yvalue = uint(bounds.Dy()) / minratio
//
//	cimg := resize.Resize(xvalue, yvalue, img, resize.Bilinear)
//	buffer := new(bytes.Buffer)
//
//	// **编码并存储**
//	switch imgType {
//	case "png":
//		err = png.Encode(buffer, cimg)
//	case "jpeg", "jpg":
//		err = jpeg.Encode(buffer, cimg, nil)
//	case "gif":
//		err = gif.Encode(buffer, cimg, nil)
//	default:
//		return nil, fmt.Errorf("unsupported image format: %s", imgType)
//	}
//	if err != nil {
//		return nil, err
//	}
//
//	return &CompressedImage{
//		Buf:    buffer,
//		Width:  bounds.Dx(),
//		Height: bounds.Dy(),
//		Size:   int64(buffer.Len()),
//	}, nil
//}
//
//// **存储压缩图片**
//func saveResizeImage(compressedImg *CompressedImage, originUrl string) (string, error) {
//	u, _ := url.Parse(originUrl)
//	fileName := u.RequestURI()
//	if strings.HasPrefix(fileName, "/") {
//		fileName = fileName[1:]
//	}
//
//	newPath := "./img/" + fileName
//	if err := os.MkdirAll("img", os.ModePerm); err != nil {
//		return "", fmt.Errorf("creating img directory failed: %v", err)
//	}
//
//	outFile, err := os.Create(newPath)
//	if err != nil {
//		return "", fmt.Errorf("creating file failed: %v", err)
//	}
//	defer outFile.Close()
//
//	_, err = outFile.Write(compressedImg.Buf.Bytes())
//	if err != nil {
//		return "", fmt.Errorf("writing compressed image to file failed: %v", err)
//	}
//	return newPath, nil
//}
//
//// **读取图片路径**
//func getImagePathsFromFile(filePath string) ([]string, error) {
//	file, err := os.Open(filePath)
//	if err != nil {
//		return nil, err
//	}
//	defer file.Close()
//
//	var paths []string
//	scanner := bufio.NewScanner(file)
//	for scanner.Scan() {
//		paths = append(paths, scanner.Text())
//	}
//	if err := scanner.Err(); err != nil {
//		return nil, err
//	}
//	return paths, nil
//}
//
//// **主函数**
//func main() {
//	imagePaths, err := getImagePathsFromFile("./img.txt")
//	if err != nil {
//		log.Fatalf("Error reading file: %v", err)
//	}
//
//	fmt.Println("Image Paths:")
//	for _, path := range imagePaths {
//		ResaveImg(path)
//	}
//	defer file.Close()
//	defer fileSuccess.Close()
//}
//
//// **计算最小值**
//func min(a, b uint) uint {
//	if a < b {
//		return a
//	}
//	return b
//}
