/*
 Navicat MySQL Dump SQL

 Source Server         : dev-gtcms
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : gtcms-test.cluster-c9k28m0w8151.ap-east-1.rds.amazonaws.com:3306
 Source Schema         : gtcms

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 22/07/2025 18:20:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account
-- ----------------------------
DROP TABLE IF EXISTS `account`;
CREATE TABLE `account` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account` varchar(32) COLLATE utf8mb4_general_ci NOT NULL COMMENT '帐号',
  `password` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '密码',
  `nick_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `contact` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系方式',
  `remark` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `role_id` int unsigned NOT NULL COMMENT '角色id',
  `audit_password` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '私人密码',
  `is_online` tinyint NOT NULL DEFAULT '0' COMMENT '1:在线 2:离线',
  `is_affect` tinyint NOT NULL COMMENT '1:启用 2:停用',
  `last_signin_time` bigint NOT NULL DEFAULT '0' COMMENT '上次登录时间',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `is_require_google_auth` tinyint NOT NULL DEFAULT '1' COMMENT '是否谷歌验证码登录  1:需要 2:不用',
  `google_auth_secret` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '谷歌验证秘钥',
  `template_ids` json DEFAULT NULL COMMENT '可访问的模板id集合',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account` (`account`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `account_chk_1` CHECK (json_valid(`template_ids`))
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员表';

-- ----------------------------
-- Table structure for account_audit_log
-- ----------------------------
DROP TABLE IF EXISTS `account_audit_log`;
CREATE TABLE `account_audit_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int unsigned NOT NULL COMMENT '管理员id',
  `account` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '管理员账号',
  `login_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录IP',
  `node_id` int NOT NULL COMMENT '节点ID',
  `path` varchar(500) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作路径  eg: 会员管理/会员列表/所有玩家',
  `object` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作对象 eg：会员属性 | 黑名单 | 产品名称 | ',
  `modify_type` int NOT NULL COMMENT '修改类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7覆盖',
  `modify_item` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作属性  ；eg： 标签 | 状态 | 备注 | 踢下线 | 优质 ',
  `value_before` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改前记录',
  `value_after` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改后记录',
  `diff_old` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改前变动的值',
  `diff_new` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '修改后变动的值',
  `extend_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '扩展信息 eg：存放批量编辑影响的会员等',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  CONSTRAINT `account_audit_log_chk_1` CHECK (json_valid(`extend_info`))
) ENGINE=InnoDB AUTO_INCREMENT=349325 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员操作日志表';

-- ----------------------------
-- Table structure for account_login_log
-- ----------------------------
DROP TABLE IF EXISTS `account_login_log`;
CREATE TABLE `account_login_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL COMMENT '账户id',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账户名',
  `signin_time` bigint NOT NULL COMMENT '登录时间',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录ip（ip6长度为39字符）',
  `ip_region` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'ip地址位置',
  `oper_type` int NOT NULL COMMENT '操作类型 1 登入 2 登出 3 修改密码',
  `status` int NOT NULL COMMENT '状态 1 成功 2 失败',
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '设备编号',
  `device_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'chrome ie ',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `type` (`account_id`,`signin_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2924 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='管理员登录日志表';

-- ----------------------------
-- Table structure for account_node_config
-- ----------------------------
DROP TABLE IF EXISTS `account_node_config`;
CREATE TABLE `account_node_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点名称',
  `level` tinyint NOT NULL COMMENT '层级',
  `parent_id` int unsigned NOT NULL COMMENT '父id 第一层级为0',
  `api_node` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应的api接口名称',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4074 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台管理节点配置表';

-- ----------------------------
-- Table structure for account_site_link
-- ----------------------------
DROP TABLE IF EXISTS `account_site_link`;
CREATE TABLE `account_site_link` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_id` int unsigned NOT NULL COMMENT '账号id',
  `group_id` int NOT NULL COMMENT '组ID',
  `site_id` int unsigned NOT NULL COMMENT '站点id',
  `is_affect` tinyint NOT NULL DEFAULT '1' COMMENT '1:启用 2:停用',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_account_id` (`account_id`,`site_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='账号站点关联表';

-- ----------------------------
-- Table structure for ad
-- ----------------------------
DROP TABLE IF EXISTS `ad`;
CREATE TABLE `ad` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组/站点id',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `belong` tinyint NOT NULL DEFAULT '0' COMMENT '所属(0:自身 1:分组 2:站点)',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型(1:文字 2:图片 3:视频)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '链接',
  `content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `expire_time` bigint NOT NULL DEFAULT '0' COMMENT '到期时间',
  `size` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '尺寸',
  `image` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '广告图片',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '备注',
  `show_pc` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示pc端(1:是 2:否)',
  `show_mobile` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示移动端(1:是 2:否)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `image_mobile` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '广告图片(mobile)',
  `open_referer` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='广告';

-- ----------------------------
-- Table structure for ad_click_record
-- ----------------------------
DROP TABLE IF EXISTS `ad_click_record`;
CREATE TABLE `ad_click_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `ad_id` int NOT NULL DEFAULT '0' COMMENT '广告id',
  `clicks` int NOT NULL DEFAULT '0' COMMENT '当天点击量',
  `record_day` date NOT NULL COMMENT '当天日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告点击记录表';

-- ----------------------------
-- Table structure for article_transformation_config
-- ----------------------------
DROP TABLE IF EXISTS `article_transformation_config`;
CREATE TABLE `article_transformation_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tab_type` int NOT NULL DEFAULT '0' COMMENT '栏目分类[1 翻译接口, 2 伪原创接口, 3 其他接口]',
  `choice` int NOT NULL DEFAULT '0' COMMENT '选择[1 百度翻译, 2 腾讯翻译, 3 有道翻译]',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '启用 [ 1 开 2 关]',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'AppID',
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'token',
  `is_auto_detect` int NOT NULL DEFAULT '0' COMMENT '自动检测 [ 1 开  2 关 ]',
  `source_language` int NOT NULL DEFAULT '0' COMMENT '源语言[ 1 中文  2 英文 ]',
  `dest_language` int NOT NULL DEFAULT '0' COMMENT '目标语言[ 1 中文 2 英文 ]',
  `domain` int NOT NULL DEFAULT '0' COMMENT '领域[ 1 体育 ]',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_tab_type` (`tab_type`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=153 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统配置-第三方文章转换配置';

-- ----------------------------
-- Table structure for attachment
-- ----------------------------
DROP TABLE IF EXISTS `attachment`;
CREATE TABLE `attachment` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分类id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对象key',
  `size` bigint NOT NULL DEFAULT '0' COMMENT '大小(单位字节)',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型[1  图片 2视频]',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key` (`key`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='附件记录表';

-- ----------------------------
-- Table structure for attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `attachment_category`;
CREATE TABLE `attachment_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `pid` int unsigned NOT NULL DEFAULT '0' COMMENT '父分类',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型[1  图片 2视频]',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `order_by` int NOT NULL DEFAULT '0' COMMENT '排序值(值大排前)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='附件记录表';

-- ----------------------------
-- Table structure for bi_rank_site_inclusion_day
-- ----------------------------
DROP TABLE IF EXISTS `bi_rank_site_inclusion_day`;
CREATE TABLE `bi_rank_site_inclusion_day` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '汇总日期',
  `site_id` int NOT NULL COMMENT '站点ID',
  `num_inclusion` int unsigned NOT NULL COMMENT '收录数',
  `create_at` timestamp NOT NULL COMMENT '创建时间 单位秒',
  `update_at` timestamp NOT NULL COMMENT '更新时间 单位秒',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_site` (`date`,`site_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页排行-站点收录-按天统计';

-- ----------------------------
-- Table structure for bi_rank_site_pv_day
-- ----------------------------
DROP TABLE IF EXISTS `bi_rank_site_pv_day`;
CREATE TABLE `bi_rank_site_pv_day` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '汇总日期',
  `site_id` int NOT NULL COMMENT '站点ID',
  `num_visit` int unsigned NOT NULL COMMENT '访问数',
  `create_at` timestamp NOT NULL COMMENT '创建时间 单位秒',
  `update_at` timestamp NOT NULL COMMENT '更新时间 单位秒',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_site` (`date`,`site_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页排行-站点PV-按天统计';

-- ----------------------------
-- Table structure for bi_rank_tag_pv_day
-- ----------------------------
DROP TABLE IF EXISTS `bi_rank_tag_pv_day`;
CREATE TABLE `bi_rank_tag_pv_day` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '汇总日期',
  `tag_id` int NOT NULL COMMENT '标签ID',
  `num_visit` int unsigned NOT NULL COMMENT '访问次数',
  `create_at` timestamp NOT NULL COMMENT '创建时间 单位秒',
  `update_at` timestamp NOT NULL COMMENT '更新时间 单位秒',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_site` (`date`,`tag_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页排行-热门标签-按天统计';

-- ----------------------------
-- Table structure for bi_stat_spider_pv_hour
-- ----------------------------
DROP TABLE IF EXISTS `bi_stat_spider_pv_hour`;
CREATE TABLE `bi_stat_spider_pv_hour` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '汇总日期',
  `hour` tinyint NOT NULL COMMENT '汇总小时',
  `engine_id` int NOT NULL COMMENT '搜索引擎ID',
  `num_visit` int unsigned NOT NULL COMMENT '访问次数',
  `create_at` timestamp NOT NULL COMMENT '创建时间 单位秒',
  `update_at` timestamp NOT NULL COMMENT '更新时间 单位秒',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_site` (`date`,`hour`,`engine_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页-今日蜘蛛统计-小时统计';

-- ----------------------------
-- Table structure for bi_stat_website
-- ----------------------------
DROP TABLE IF EXISTS `bi_stat_website`;
CREATE TABLE `bi_stat_website` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '汇总日期',
  `num_uptime` int unsigned NOT NULL COMMENT '运行天数',
  `num_site` int unsigned NOT NULL COMMENT '总站个数',
  `num_news` int unsigned NOT NULL COMMENT '新闻资讯',
  `num_live` int unsigned NOT NULL COMMENT '赛场直播 场',
  `num_column` int unsigned NOT NULL COMMENT '栏目总数',
  `num_key` int unsigned NOT NULL COMMENT '关键词总数',
  `num_tag` int unsigned NOT NULL COMMENT 'Tag标签总数',
  `num_link` int unsigned NOT NULL COMMENT '友情链接数',
  `create_at` timestamp NOT NULL COMMENT '创建时间 单位秒',
  `update_at` timestamp NOT NULL COMMENT '更新时间 单位秒',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_site` (`date`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='首页-网站数据';

-- ----------------------------
-- Table structure for cms_login_config
-- ----------------------------
DROP TABLE IF EXISTS `cms_login_config`;
CREATE TABLE `cms_login_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `is_open` tinyint NOT NULL COMMENT '后台开关： 1:开 2:关',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '后台名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '后台网址',
  `is_ip_bind` tinyint NOT NULL DEFAULT '0' COMMENT 'IP绑定： 1:开 2:关',
  `is_mac_bind` tinyint NOT NULL DEFAULT '0' COMMENT '机器码绑定： 1:开 2:关',
  `is_google_bind` tinyint NOT NULL DEFAULT '0' COMMENT '谷歌验证码绑定： 1:开 2:关',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台设置-基本设置';

-- ----------------------------
-- Table structure for cms_login_risk
-- ----------------------------
DROP TABLE IF EXISTS `cms_login_risk`;
CREATE TABLE `cms_login_risk` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tab_type` int NOT NULL DEFAULT '0' COMMENT '分类 [1 IP绑定管理, 2 机器码管理]',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '（1） tab_type = 1时 对应 IP地址（2） tab_type = 2时 对应机器码',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '状态 [ 1 开 2 关]',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=227 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='后台设置-IP绑定管理-机器码管理';

-- ----------------------------
-- Table structure for collect_basketball_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_competition`;
CREATE TABLE `collect_basketball_competition` (
  `id` int NOT NULL,
  `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '赛事类型： 赛事类型，0-未知、1-联赛、2-杯赛、3-友谊赛',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `introduction` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '简介',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球-赛事';

-- ----------------------------
-- Table structure for collect_basketball_competition_stats
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_competition_stats`;
CREATE TABLE `collect_basketball_competition_stats` (
  `id` int NOT NULL COMMENT '赛事id',
  `season_id` int NOT NULL,
  `promotions` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `teams_stats` mediumtext COLLATE utf8mb3_bin,
  `players_stats` mediumtext COLLATE utf8mb3_bin,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';

-- ----------------------------
-- Table structure for collect_basketball_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_honor`;
CREATE TABLE `collect_basketball_honor` (
  `id` int NOT NULL COMMENT '荣誉id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '中文名',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '粤语名',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '英文名',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '荣誉logo',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球员荣誉';

-- ----------------------------
-- Table structure for collect_basketball_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_match`;
CREATE TABLE `collect_basketball_match` (
  `id` int NOT NULL COMMENT '比赛id',
  `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
  `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
  `kind` tinyint NOT NULL DEFAULT '0' COMMENT '比赛类型  1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无',
  `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
  `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
  `home_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '主队比分， 四节+加时',
  `away_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '客队比分 四节+加时',
  `over_time_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '加时赛比分',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
  PRIMARY KEY (`id`),
  KEY `match_away` (`away_team_id`),
  KEY `match_home` (`home_team_id`),
  KEY `match_ts_index` (`match_ts`),
  KEY `collect_basketball_match_competition_id_index` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球比赛数据';

-- ----------------------------
-- Table structure for collect_basketball_match_edit
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_match_edit`;
CREATE TABLE `collect_basketball_match_edit` (
  `id` int NOT NULL AUTO_INCREMENT,
  `match_id` int NOT NULL,
  `home_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '主队比分',
  `away_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '客队比分',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛比分修改';

-- ----------------------------
-- Table structure for collect_basketball_match_live
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_match_live`;
CREATE TABLE `collect_basketball_match_live` (
  `id` int NOT NULL COMMENT '赛事id',
  `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `timer` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `status` int NOT NULL DEFAULT '0' COMMENT '赛事状态',
  `seconds` int NOT NULL COMMENT '小节剩余时间(秒)',
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';

-- ----------------------------
-- Table structure for collect_basketball_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_player`;
CREATE TABLE `collect_basketball_player` (
  `id` int NOT NULL,
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员logo',
  `country_id` int NOT NULL COMMENT '国家id',
  `national_logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员logo(国家队，',
  `birthday` int NOT NULL DEFAULT '0' COMMENT '生日',
  `age` int NOT NULL DEFAULT '0' COMMENT '年龄',
  `height` int NOT NULL DEFAULT '0' COMMENT '身高',
  `weight` int NOT NULL DEFAULT '0' COMMENT '体重',
  `contract_until` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '合同截止时间',
  `position` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '擅长位置 C-中锋、SF-小前锋、PF-大前锋、SG-得分后卫、PG-组织后卫、F-前锋、G-后卫，其它都为未知 ',
  `drafted` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '选秀顺位',
  `league_career_age` int NOT NULL DEFAULT '0' COMMENT '联盟球龄',
  `salary` int NOT NULL DEFAULT '0' COMMENT '年薪$',
  `shirt_number` int NOT NULL DEFAULT '0' COMMENT '球衣号',
  `school` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '毕业学校',
  `city` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '数据更新时间(纳米数据）',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '时间戳毫秒',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '时间戳毫秒',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='数据采集：篮球球员';

-- ----------------------------
-- Table structure for collect_basketball_player_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_player_honor`;
CREATE TABLE `collect_basketball_player_honor` (
  `id` int NOT NULL COMMENT '球员id',
  `honors` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员荣誉项: {honor_id int, season str, team_id int, competition_id int, season_id int, country_id int}',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球员荣誉列表';

-- ----------------------------
-- Table structure for collect_basketball_schedule_diary
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_schedule_diary`;
CREATE TABLE `collect_basketball_schedule_diary` (
  `id` int NOT NULL COMMENT '赛事id',
  `season_id` int NOT NULL COMMENT '赛季id',
  `competition_id` int NOT NULL COMMENT '联赛id',
  `match_time` int DEFAULT NULL COMMENT '比赛时间',
  `stage_id` int DEFAULT '0' COMMENT '阶段id',
  `group_num` int DEFAULT '0' COMMENT '第几组，1-A、2-B以此类推',
  `round_num` int DEFAULT '0' COMMENT '第几轮',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球-场馆';

-- ----------------------------
-- Table structure for collect_basketball_season
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_season`;
CREATE TABLE `collect_basketball_season` (
  `id` int NOT NULL,
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
  `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
  `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
  `has_player_stats` int NOT NULL COMMENT '是否有球员统计，1-是、0-否',
  `has_team_stats` int NOT NULL COMMENT '是否有球队统计，1-是、0-否',
  `has_table` int NOT NULL COMMENT '是否有积分榜，1-是、0-否',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球赛季';

-- ----------------------------
-- Table structure for collect_basketball_squad
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_squad`;
CREATE TABLE `collect_basketball_squad` (
  `team_id` int NOT NULL COMMENT '球队id',
  `player_id` int NOT NULL COMMENT '球员id',
  `position` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知',
  `shirt_number` int NOT NULL DEFAULT '0' COMMENT '球衣号',
  `update_at` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球-阵容';

-- ----------------------------
-- Table structure for collect_basketball_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_team`;
CREATE TABLE `collect_basketball_team` (
  `id` int NOT NULL,
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id（球队所属联赛，杯赛不关联）',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `coach_id` int NOT NULL DEFAULT '0' COMMENT '教练id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球队logo',
  `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
  `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息';

-- ----------------------------
-- Table structure for collect_basketball_team_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_team_honor`;
CREATE TABLE `collect_basketball_team_honor` (
  `id` int NOT NULL COMMENT '球队id',
  `honors` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT ' {honor_id int, season str, competition_id int, season_id int}',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队荣誉id';

-- ----------------------------
-- Table structure for collect_basketball_vc
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_vc`;
CREATE TABLE `collect_basketball_vc` (
  `id` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `image` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `duration` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `type` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `sysdate` bigint NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for collect_basketball_venue
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_venue`;
CREATE TABLE `collect_basketball_venue` (
  `id` int NOT NULL COMMENT '场馆id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `capacity` int NOT NULL DEFAULT '0' COMMENT '球场容量',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球-场馆';

-- ----------------------------
-- Table structure for collect_basketball_video_collection
-- ----------------------------
DROP TABLE IF EXISTS `collect_basketball_video_collection`;
CREATE TABLE `collect_basketball_video_collection` (
  `id` int NOT NULL COMMENT '赛事id',
  `type` int NOT NULL COMMENT '类型，1-集锦、2-录像',
  `title` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '名称',
  `mobile_link` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'wap直播地址',
  `pc_link` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT 'web直播地址',
  `cover` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT '图片',
  `duration` int NOT NULL DEFAULT '0' COMMENT '时长-秒（s）',
  `competition_id` int NOT NULL COMMENT '联赛id',
  `match_ts` int NOT NULL COMMENT '赛事时间',
  PRIMARY KEY (`id`,`mobile_link`,`pc_link`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球集锦';

-- ----------------------------
-- Table structure for collect_country
-- ----------------------------
DROP TABLE IF EXISTS `collect_country`;
CREATE TABLE `collect_country` (
  `id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id, 1国际， 2欧，3美，4亚，5大洋，6非，7沙滩',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '英文名称',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `updated_at` bigint NOT NULL COMMENT '纳米的更新时间',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间 时间戳毫秒',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='数据采集：国家';

-- ----------------------------
-- Table structure for collect_cs_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_cs_competition`;
CREATE TABLE `collect_cs_competition` (
  `id` int NOT NULL COMMENT '赛事ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `status_id` int NOT NULL COMMENT '状态，详见状态码',
  `logo` varchar(255) DEFAULT '' COMMENT '赛事logo',
  `cover` varchar(255) DEFAULT '' COMMENT '封面',
  `start_time` int NOT NULL COMMENT '开始时间（时间戳）',
  `end_time` int NOT NULL COMMENT '结束时间（时间戳）',
  `type` tinyint NOT NULL COMMENT '赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）',
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 中文名称',
  `city_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 英文名称',
  `price_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '奖金池',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='CSGO赛事信息表';

-- ----------------------------
-- Table structure for collect_cs_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_cs_match`;
CREATE TABLE `collect_cs_match` (
  `id` int NOT NULL COMMENT '比赛ID',
  `box` int NOT NULL COMMENT '总局数（BO1、BO3等）',
  `tournament_id` int NOT NULL COMMENT '赛事ID',
  `stage_id` int NOT NULL COMMENT '阶段ID',
  `home_team_id` int NOT NULL COMMENT '主队战队ID',
  `home_score` int DEFAULT '0' COMMENT '主队获胜局数',
  `away_team_id` int NOT NULL COMMENT '客队战队ID',
  `away_score` int DEFAULT '0' COMMENT '客队获胜局数',
  `status_id` int NOT NULL COMMENT '比赛状态（详见状态码）',
  `match_time` int NOT NULL COMMENT '比赛时间（时间戳）',
  `mlive` tinyint(1) DEFAULT '0' COMMENT '是否有动画（1是，0否）',
  `description` varchar(255) DEFAULT '' COMMENT '比赛说明/备注',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  PRIMARY KEY (`id`),
  KEY `idx_tournament_id` (`tournament_id`),
  KEY `idx_stage_id` (`stage_id`),
  KEY `idx_match_time` (`match_time`),
  KEY `idx_status_id` (`status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL 比赛信息表';

-- ----------------------------
-- Table structure for collect_cs_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_cs_player`;
CREATE TABLE `collect_cs_player` (
  `id` int NOT NULL COMMENT '选手ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '选手Logo',
  `team_id` int DEFAULT '0' COMMENT '所属战队ID',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '真实名称',
  `birthday` int DEFAULT '0' COMMENT '生日（时间戳）',
  `retired` tinyint(1) DEFAULT '0' COMMENT '是否退役（1-是，0-否）',
  `status` tinyint DEFAULT '1' COMMENT '选手状态（1-首发，2-替补）',
  `position` tinyint DEFAULT '0' COMMENT '位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_retired` (`retired`),
  KEY `idx_position` (`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL选手信息表';

-- ----------------------------
-- Table structure for collect_cs_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_cs_team`;
CREATE TABLE `collect_cs_team` (
  `id` int NOT NULL COMMENT '战队ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '战队Logo',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `region_id` int DEFAULT '0' COMMENT '所属赛区ID',
  `create_time` int DEFAULT '0' COMMENT '战队成立时间（时间戳）',
  `total_earnings` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '总奖金（字符串）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL战队信息表';

-- ----------------------------
-- Table structure for collect_dota_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_dota_competition`;
CREATE TABLE `collect_dota_competition` (
  `id` int NOT NULL COMMENT '赛事ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `status_id` int NOT NULL COMMENT '状态，详见状态码',
  `logo` varchar(255) DEFAULT '' COMMENT '赛事logo',
  `cover` varchar(255) DEFAULT '' COMMENT '封面',
  `start_time` int NOT NULL COMMENT '开始时间（时间戳）',
  `end_time` int NOT NULL COMMENT '结束时间（时间戳）',
  `type` tinyint NOT NULL COMMENT '赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）',
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 中文名称',
  `city_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 英文名称',
  `price_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '奖金池',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='CSGO赛事信息表';

-- ----------------------------
-- Table structure for collect_dota_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_dota_match`;
CREATE TABLE `collect_dota_match` (
  `id` int NOT NULL COMMENT '比赛ID',
  `box` int NOT NULL COMMENT '总局数（BO1、BO3等）',
  `tournament_id` int NOT NULL COMMENT '赛事ID',
  `stage_id` int NOT NULL COMMENT '阶段ID',
  `home_team_id` int NOT NULL COMMENT '主队战队ID',
  `home_score` int DEFAULT '0' COMMENT '主队获胜局数',
  `away_team_id` int NOT NULL COMMENT '客队战队ID',
  `away_score` int DEFAULT '0' COMMENT '客队获胜局数',
  `status_id` int NOT NULL COMMENT '比赛状态（详见状态码）',
  `match_time` int NOT NULL COMMENT '比赛时间（时间戳）',
  `mlive` tinyint(1) DEFAULT '0' COMMENT '是否有动画（1是，0否）',
  `description` varchar(255) DEFAULT '' COMMENT '比赛说明/备注',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  PRIMARY KEY (`id`),
  KEY `idx_tournament_id` (`tournament_id`),
  KEY `idx_stage_id` (`stage_id`),
  KEY `idx_match_time` (`match_time`),
  KEY `idx_status_id` (`status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL 比赛信息表';

-- ----------------------------
-- Table structure for collect_dota_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_dota_player`;
CREATE TABLE `collect_dota_player` (
  `id` int NOT NULL COMMENT '选手ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '选手Logo',
  `team_id` int DEFAULT '0' COMMENT '所属战队ID',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '真实名称',
  `birthday` int DEFAULT '0' COMMENT '生日（时间戳）',
  `retired` tinyint(1) DEFAULT '0' COMMENT '是否退役（1-是，0-否）',
  `status` tinyint DEFAULT '1' COMMENT '选手状态（1-首发，2-替补）',
  `position` tinyint DEFAULT '0' COMMENT '位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_retired` (`retired`),
  KEY `idx_position` (`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL选手信息表';

-- ----------------------------
-- Table structure for collect_dota_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_dota_team`;
CREATE TABLE `collect_dota_team` (
  `id` int NOT NULL COMMENT '战队ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '战队Logo',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `region_id` int DEFAULT '0' COMMENT '所属赛区ID',
  `create_time` int DEFAULT '0' COMMENT '战队成立时间（时间戳）',
  `total_earnings` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '总奖金（字符串）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL战队信息表';

-- ----------------------------
-- Table structure for collect_extra_info
-- ----------------------------
DROP TABLE IF EXISTS `collect_extra_info`;
CREATE TABLE `collect_extra_info` (
  `category_id` int NOT NULL DEFAULT '0' COMMENT '本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ...',
  `belong_id` int NOT NULL DEFAULT '0' COMMENT '属于对应的category中的哪个id的数据。比如当category=1时，它表示足球赛事id',
  `view_count` int DEFAULT '0' COMMENT '浏览量',
  `release_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `order_weight` int DEFAULT '0' COMMENT '排序权重',
  `introduction` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '简介(联赛、球队、球员)',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否热门(1是 2否)',
  `is_top` tinyint DEFAULT '0' COMMENT '是否置顶(1是 2否)',
  PRIMARY KEY (`belong_id`,`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='采集数据的seo信息';

-- ----------------------------
-- Table structure for collect_football_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_competition`;
CREATE TABLE `collect_football_competition` (
  `id` int NOT NULL,
  `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '赛事类型： 赛事类型，0-未知、1-联赛、2-杯赛、3-友谊赛',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
  `upper_id` int NOT NULL DEFAULT '0' COMMENT '高一级赛事id, 没有就是0',
  `lower_id` int NOT NULL DEFAULT '0' COMMENT '低一级赛事id, 没有就是0',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `title_holder` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '卫冕冠军(json数组，详情见文档',
  `most_titles` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '夺冠最多球队 (json数组，详情见接口文档 ',
  `newcomers` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '晋级淘汰球队(json数组，见文档',
  `host_country` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '东道主国家',
  `host_city` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '东道主城市',
  `primary_color` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '主颜色',
  `secondary_color` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '次颜色',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球-赛事';

-- ----------------------------
-- Table structure for collect_football_competition_stats
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_competition_stats`;
CREATE TABLE `collect_football_competition_stats` (
  `id` int NOT NULL COMMENT '赛事id',
  `season_id` int NOT NULL,
  `promotions` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `shooters` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '射手榜',
  `teams_stats` mediumtext COLLATE utf8mb3_bin,
  `players_stats` mediumtext COLLATE utf8mb3_bin,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';

-- ----------------------------
-- Table structure for collect_football_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_honor`;
CREATE TABLE `collect_football_honor` (
  `id` int NOT NULL COMMENT '荣誉id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '中文名',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '粤语名',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '英文名',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '荣誉logo',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球员荣誉';

-- ----------------------------
-- Table structure for collect_football_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_match`;
CREATE TABLE `collect_football_match` (
  `id` bigint NOT NULL COMMENT '比赛id',
  `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
  `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
  `kind` tinyint NOT NULL DEFAULT '0' COMMENT '比赛类型  1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无',
  `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
  `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
  `home_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '主队比分,0:常规时间比赛；1半场比分；2红牌；3黄牌；4角球；5加时比分，包括常规时间 ； 6点球大战',
  `away_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '客队比分,0:常规时间比赛；1半场比分；2红牌；3黄牌；4角球；5加时比分，包括常规时间 ； 6点球大战',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
  PRIMARY KEY (`id`),
  KEY `away_id` (`away_team_id`),
  KEY `home_id` (`home_team_id`),
  KEY `match_ts_index` (`match_ts`),
  KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球比赛数据';

-- ----------------------------
-- Table structure for collect_football_match_analysis
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_match_analysis`;
CREATE TABLE `collect_football_match_analysis` (
  `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
  `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `goal_distribution` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '进球分布',
  `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩',
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15510732 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球比赛分析数据';

-- ----------------------------
-- Table structure for collect_football_match_edit
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_match_edit`;
CREATE TABLE `collect_football_match_edit` (
  `id` int NOT NULL AUTO_INCREMENT,
  `match_id` int NOT NULL,
  `home_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '主队比分',
  `away_scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '客队比分',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛比分修改';

-- ----------------------------
-- Table structure for collect_football_match_live
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_match_live`;
CREATE TABLE `collect_football_match_live` (
  `id` int NOT NULL COMMENT '赛事id',
  `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `status` int NOT NULL DEFAULT '0',
  `minutes` int NOT NULL COMMENT '比赛进行分钟数',
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';

-- ----------------------------
-- Table structure for collect_football_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_player`;
CREATE TABLE `collect_football_player` (
  `id` int NOT NULL,
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `country_id` int NOT NULL COMMENT '国家id',
  `nationality` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '国籍',
  `birthday` int NOT NULL DEFAULT '0' COMMENT '生日',
  `age` int NOT NULL DEFAULT '0' COMMENT '年龄',
  `height` int NOT NULL DEFAULT '0' COMMENT '身高',
  `weight` int NOT NULL DEFAULT '0' COMMENT '体重',
  `market_value` int NOT NULL DEFAULT '0' COMMENT '市值',
  `market_value_currency` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '市值单位',
  `contract_until` int NOT NULL DEFAULT '0' COMMENT '合同截止时间',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员logo',
  `national_logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员logo(国家队，',
  `position` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '擅长位置  F-前锋、M-中场、D-后卫、G-守门员、其他为未知',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '数据更新时间(纳米数据）',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '时间戳毫秒',
  `update_time` bigint NOT NULL COMMENT '时间戳毫秒',
  `preferred_foot` tinyint NOT NULL DEFAULT '0' COMMENT '惯用脚，0-未知、1-左脚、2-右脚、3-左右脚',
  `suffix` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '球员u系列?',
  `coach_id` int NOT NULL DEFAULT '0' COMMENT '教练id (球员转教练后的id',
  `uid` int NOT NULL DEFAULT '0' COMMENT '球员id(重复球员合并后的对应id)',
  `ability` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '能力评分 (json数组，详情见文档)',
  `characteristics` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '技术特点字段说明（json 数组 详情见接口文档',
  `positions` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '详细位置说明(json 数组， 详情见接口文档 example：["RW", ["ST"]]',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='数据采集：足球球员';

-- ----------------------------
-- Table structure for collect_football_player_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_player_honor`;
CREATE TABLE `collect_football_player_honor` (
  `id` int NOT NULL COMMENT '球员id',
  `honors` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员荣誉项: {honor_id int, season str, team_id int, competition_id int, season_id int, country_id int}',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球员荣誉列表';

-- ----------------------------
-- Table structure for collect_football_schedule_diary
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_schedule_diary`;
CREATE TABLE `collect_football_schedule_diary` (
  `id` int NOT NULL COMMENT '赛事id',
  `season_id` int NOT NULL COMMENT '赛季id',
  `competition_id` int NOT NULL COMMENT '联赛id',
  `match_time` int DEFAULT NULL COMMENT '比赛时间',
  `stage_id` int DEFAULT '0' COMMENT '阶段id',
  `group_num` int DEFAULT '0' COMMENT '第几组，1-A、2-B以此类推',
  `round_num` int DEFAULT '0' COMMENT '第几轮',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球-场馆';

-- ----------------------------
-- Table structure for collect_football_season
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_season`;
CREATE TABLE `collect_football_season` (
  `id` int NOT NULL,
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
  `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
  `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
  `has_player_stats` int NOT NULL COMMENT '是否有球员统计，1-是、0-否',
  `has_team_stats` int NOT NULL COMMENT '是否有球队统计，1-是、0-否',
  `has_table` int NOT NULL COMMENT '是否有积分榜，1-是、0-否',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';

-- ----------------------------
-- Table structure for collect_football_squad
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_squad`;
CREATE TABLE `collect_football_squad` (
  `team_id` int NOT NULL COMMENT '球队id',
  `player_id` int NOT NULL COMMENT '球员id',
  `position` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知',
  `has_shirt_number` tinyint NOT NULL DEFAULT '0' COMMENT '是否有球衣号，1-是、0-否',
  `shirt_number` int NOT NULL DEFAULT '0' COMMENT '球衣号',
  `update_at` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球-阵容';

-- ----------------------------
-- Table structure for collect_football_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_team`;
CREATE TABLE `collect_football_team` (
  `id` int NOT NULL,
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id（球队所属联赛，杯赛不关联）',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `coach_id` int NOT NULL DEFAULT '0' COMMENT '教练id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球队logo',
  `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
  `country_logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '国家队logo（为国家队时存在）',
  `foundation_time` int NOT NULL DEFAULT '0' COMMENT '成立时间',
  `website` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球队官网',
  `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
  `market_value` int NOT NULL DEFAULT '0' COMMENT '市值',
  `market_value_currency` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '市值单位',
  `total_players` int NOT NULL DEFAULT '0' COMMENT '总球员数，-1表示没有该字段数据',
  `foreign_players` int NOT NULL DEFAULT '0' COMMENT '非本土球员数，-1表示没有该字段数据',
  `national_players` int NOT NULL DEFAULT '0' COMMENT '国家队球员数，-1表示没有该字段数据',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息';

-- ----------------------------
-- Table structure for collect_football_team_honor
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_team_honor`;
CREATE TABLE `collect_football_team_honor` (
  `id` int NOT NULL COMMENT '球队id',
  `honors` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT ' {honor_id int, season str, competition_id int, season_id int}',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队荣誉id';

-- ----------------------------
-- Table structure for collect_football_transfer
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_transfer`;
CREATE TABLE `collect_football_transfer` (
  `id` int NOT NULL COMMENT '球员id',
  `transfer` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球员转会列表[{from_team_id int, from_team_name str, to_team_id int, to_team_name, transfer_type int, transfer_time int, transfer_fee int, transfer_desc}] => 转会类型，1-租借、2-租借结束、3-转会、4-退役、5-选秀、6-已解约、7-已签约、8-未知,   转会描述里含单位',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球员转会列表';

-- ----------------------------
-- Table structure for collect_football_venue
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_venue`;
CREATE TABLE `collect_football_venue` (
  `id` int NOT NULL COMMENT '场馆id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `capacity` int NOT NULL DEFAULT '0' COMMENT '球场容量',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球-场馆';

-- ----------------------------
-- Table structure for collect_football_video_collection
-- ----------------------------
DROP TABLE IF EXISTS `collect_football_video_collection`;
CREATE TABLE `collect_football_video_collection` (
  `id` int NOT NULL COMMENT '赛事id',
  `type` int NOT NULL COMMENT '类型，1-集锦、2-录像',
  `title` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '名称',
  `mobile_link` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'wap直播地址',
  `pc_link` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT 'web直播地址',
  `cover` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT '图片',
  `duration` int NOT NULL DEFAULT '0' COMMENT '时长-秒（s）',
  `competition_id` int NOT NULL COMMENT '联赛id',
  `match_ts` int NOT NULL COMMENT '赛事时间',
  PRIMARY KEY (`id`,`mobile_link`,`pc_link`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球集锦';

-- ----------------------------
-- Table structure for collect_kog_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_kog_competition`;
CREATE TABLE `collect_kog_competition` (
  `id` int NOT NULL COMMENT '赛事ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `status_id` int NOT NULL COMMENT '状态，详见状态码',
  `logo` varchar(255) DEFAULT '' COMMENT '赛事logo',
  `cover` varchar(255) DEFAULT '' COMMENT '封面',
  `start_time` int NOT NULL COMMENT '开始时间（时间戳）',
  `end_time` int NOT NULL COMMENT '结束时间（时间戳）',
  `type` tinyint NOT NULL COMMENT '赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）',
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 中文名称',
  `city_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 英文名称',
  `price_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '奖金池',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='CSGO赛事信息表';

-- ----------------------------
-- Table structure for collect_kog_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_kog_match`;
CREATE TABLE `collect_kog_match` (
  `id` int NOT NULL COMMENT '比赛ID',
  `box` int NOT NULL COMMENT '总局数（BO1、BO3等）',
  `tournament_id` int NOT NULL COMMENT '赛事ID',
  `stage_id` int NOT NULL COMMENT '阶段ID',
  `home_team_id` int NOT NULL COMMENT '主队战队ID',
  `home_score` int DEFAULT '0' COMMENT '主队获胜局数',
  `away_team_id` int NOT NULL COMMENT '客队战队ID',
  `away_score` int DEFAULT '0' COMMENT '客队获胜局数',
  `status_id` int NOT NULL COMMENT '比赛状态（详见状态码）',
  `match_time` int NOT NULL COMMENT '比赛时间（时间戳）',
  `mlive` tinyint(1) DEFAULT '0' COMMENT '是否有动画（1是，0否）',
  `description` varchar(255) DEFAULT '' COMMENT '比赛说明/备注',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  PRIMARY KEY (`id`),
  KEY `idx_tournament_id` (`tournament_id`),
  KEY `idx_stage_id` (`stage_id`),
  KEY `idx_match_time` (`match_time`),
  KEY `idx_status_id` (`status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL 比赛信息表';

-- ----------------------------
-- Table structure for collect_kog_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_kog_player`;
CREATE TABLE `collect_kog_player` (
  `id` int NOT NULL COMMENT '选手ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '选手Logo',
  `team_id` int DEFAULT '0' COMMENT '所属战队ID',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '真实名称',
  `birthday` int DEFAULT '0' COMMENT '生日（时间戳）',
  `retired` tinyint(1) DEFAULT '0' COMMENT '是否退役（1-是，0-否）',
  `status` tinyint DEFAULT '1' COMMENT '选手状态（1-首发，2-替补）',
  `position` tinyint DEFAULT '0' COMMENT '位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_retired` (`retired`),
  KEY `idx_position` (`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL选手信息表';

-- ----------------------------
-- Table structure for collect_kog_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_kog_team`;
CREATE TABLE `collect_kog_team` (
  `id` int NOT NULL COMMENT '战队ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '战队Logo',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `region_id` int DEFAULT '0' COMMENT '所属赛区ID',
  `create_time` int DEFAULT '0' COMMENT '战队成立时间（时间戳）',
  `total_earnings` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '总奖金（字符串）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL战队信息表';

-- ----------------------------
-- Table structure for collect_lol_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_lol_competition`;
CREATE TABLE `collect_lol_competition` (
  `id` int NOT NULL COMMENT '赛事ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `status_id` int NOT NULL COMMENT '状态，详见状态码',
  `logo` varchar(255) DEFAULT '' COMMENT '赛事logo',
  `cover` varchar(255) DEFAULT '' COMMENT '封面',
  `start_time` int NOT NULL COMMENT '开始时间（时间戳）',
  `end_time` int NOT NULL COMMENT '结束时间（时间戳）',
  `type` tinyint NOT NULL COMMENT '赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）',
  `city_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 中文名称',
  `city_name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '举办地 英文名称',
  `price_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '奖金池',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_status_id` (`status_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL赛事信息表';

-- ----------------------------
-- Table structure for collect_lol_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_lol_match`;
CREATE TABLE `collect_lol_match` (
  `id` int NOT NULL COMMENT '比赛ID',
  `box` int NOT NULL COMMENT '总局数（BO1、BO3等）',
  `tournament_id` int NOT NULL COMMENT '赛事ID',
  `stage_id` int NOT NULL COMMENT '阶段ID',
  `home_team_id` int NOT NULL COMMENT '主队战队ID',
  `home_score` int DEFAULT '0' COMMENT '主队获胜局数',
  `away_team_id` int NOT NULL COMMENT '客队战队ID',
  `away_score` int DEFAULT '0' COMMENT '客队获胜局数',
  `status_id` int NOT NULL COMMENT '比赛状态（详见状态码）',
  `match_time` int NOT NULL COMMENT '比赛时间（时间戳）',
  `mlive` tinyint(1) DEFAULT '0' COMMENT '是否有动画（1是，0否）',
  `description` varchar(255) DEFAULT '' COMMENT '比赛说明/备注',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  PRIMARY KEY (`id`),
  KEY `idx_tournament_id` (`tournament_id`),
  KEY `idx_stage_id` (`stage_id`),
  KEY `idx_match_time` (`match_time`),
  KEY `idx_status_id` (`status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL 比赛信息表';

-- ----------------------------
-- Table structure for collect_lol_match_live
-- ----------------------------
DROP TABLE IF EXISTS `collect_lol_match_live`;
CREATE TABLE `collect_lol_match_live` (
  `id` int NOT NULL COMMENT '比赛ID',
  `status_id` int NOT NULL COMMENT '比赛状态（详见状态码）',
  `box_num` int NOT NULL COMMENT '当前第几局',
  `single_status_id` int NOT NULL COMMENT '当前局比赛状态',
  `timer` json DEFAULT NULL COMMENT '比赛时间数组：[是否走表, 是否倒计时, 发布时间, 时间差, 忽略]',
  `tournament_id` int NOT NULL COMMENT '赛事ID',
  `home_id` int NOT NULL COMMENT '主队战队ID',
  `home_side` tinyint NOT NULL COMMENT '主队颜色方（1-蓝，2-红）',
  `home_score` int NOT NULL COMMENT '主队局数比分',
  `home_stats` json DEFAULT NULL COMMENT '主队统计字段（数组[15项]）',
  `home_stats2` json DEFAULT NULL COMMENT '主队统计字段2（数组[10项]）',
  `home_pick` json DEFAULT NULL COMMENT '主队英雄选择（数组）',
  `home_ban` json DEFAULT NULL COMMENT '主队禁选英雄（数组）',
  `home_players` json DEFAULT NULL COMMENT '主队选手数据（数组）',
  `away_id` int NOT NULL COMMENT '客队战队ID',
  `away_side` tinyint NOT NULL COMMENT '客队颜色方（1-蓝，2-红）',
  `away_score` int NOT NULL COMMENT '客队局数比分',
  `away_stats` json DEFAULT NULL COMMENT '客队统计字段（数组[15项]）',
  `away_stats2` json DEFAULT NULL COMMENT '客队统计字段2（数组[10项]）',
  `away_pick` json DEFAULT NULL COMMENT '客队英雄选择（数组）',
  `away_ban` json DEFAULT NULL COMMENT '客队禁选英雄（数组）',
  `away_players` json DEFAULT NULL COMMENT '客队选手数据（数组）',
  `economy_lines` json DEFAULT NULL COMMENT '经济曲线（数组字符串 例如["1:124"]）',
  `event` json DEFAULT NULL COMMENT '比赛事件列表（数组结构）',
  `updated_at` int NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status_id`),
  KEY `idx_tournament` (`tournament_id`),
  KEY `idx_box` (`box_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL 单局比赛详细信息';

-- ----------------------------
-- Table structure for collect_lol_player
-- ----------------------------
DROP TABLE IF EXISTS `collect_lol_player`;
CREATE TABLE `collect_lol_player` (
  `id` int NOT NULL COMMENT '选手ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '选手Logo',
  `team_id` int DEFAULT '0' COMMENT '所属战队ID',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '真实名称',
  `birthday` int DEFAULT '0' COMMENT '生日（时间戳）',
  `retired` tinyint(1) DEFAULT '0' COMMENT '是否退役（1-是，0-否）',
  `status` tinyint DEFAULT '1' COMMENT '选手状态（1-首发，2-替补）',
  `position` tinyint DEFAULT '0' COMMENT '位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_retired` (`retired`),
  KEY `idx_position` (`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL选手信息表';

-- ----------------------------
-- Table structure for collect_lol_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_lol_team`;
CREATE TABLE `collect_lol_team` (
  `id` int NOT NULL COMMENT '战队ID',
  `name_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `abbr_zh` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '中文简称',
  `abbr_en` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '英文简称',
  `logo` varchar(255) DEFAULT '' COMMENT '战队Logo',
  `country_id` int DEFAULT '0' COMMENT '所属国家ID',
  `region_id` int DEFAULT '0' COMMENT '所属赛区ID',
  `create_time` int DEFAULT '0' COMMENT '战队成立时间（时间戳）',
  `total_earnings` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '总奖金（字符串）',
  `updated_at` int NOT NULL COMMENT '更新时间（时间戳）',
  PRIMARY KEY (`id`),
  KEY `idx_country_id` (`country_id`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='LOL战队信息表';

-- ----------------------------
-- Table structure for collect_multi_language
-- ----------------------------
DROP TABLE IF EXISTS `collect_multi_language`;
CREATE TABLE `collect_multi_language` (
  `table` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '表名',
  `id` int NOT NULL COMMENT '表中的id',
  `column` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '表中字段名',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `name_id` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '印尼语',
  `short_name_id` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '印尼语，去掉()"''"',
  `name_vi` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '越南语',
  `short_name_vi` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '越南语，去掉()"''"',
  PRIMARY KEY (`id`,`table`,`column`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='多语言表(保存国家、联赛、球队、球员)';

-- ----------------------------
-- Table structure for collect_seo_info
-- ----------------------------
DROP TABLE IF EXISTS `collect_seo_info`;
CREATE TABLE `collect_seo_info` (
  `category_id` int NOT NULL DEFAULT '0' COMMENT '本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ...',
  `belong_id` int NOT NULL DEFAULT '0' COMMENT '属于对应的category中的哪个id的数据。比如当category=1时，它表示足球赛事id',
  `title` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '' COMMENT 'seo标题',
  `keyword` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `desc` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `tags` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT '',
  PRIMARY KEY (`category_id`,`belong_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='采集数据的seo信息';

-- ----------------------------
-- Table structure for collect_snooker_competition
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_competition`;
CREATE TABLE `collect_snooker_competition` (
  `id` int NOT NULL,
  `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `uid` int NOT NULL DEFAULT '0' COMMENT '赛事id(重复赛事合并后的对应id)',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事表';

-- ----------------------------
-- Table structure for collect_snooker_match
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_match`;
CREATE TABLE `collect_snooker_match` (
  `id` bigint NOT NULL COMMENT '比赛id',
  `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `tournament_id` int NOT NULL DEFAULT '0' COMMENT '阶段id',
  `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
  `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
  `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
  `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
  `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
  `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
  `neutral` tinyint NOT NULL DEFAULT '0' COMMENT '是否中立场，1-是、0-否',
  `bestof` tinyint NOT NULL DEFAULT '0' COMMENT '盘数，0-未知',
  `tbd` tinyint NOT NULL DEFAULT '0' COMMENT '比赛时间是否待定 1.是',
  `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
  `home_scores` int NOT NULL DEFAULT '0' COMMENT '客队id',
  `away_scores` int NOT NULL DEFAULT '0' COMMENT '客队id',
  `match_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '团体赛下关联的比赛',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
  PRIMARY KEY (`id`),
  KEY `away_id` (`away_team_id`),
  KEY `home_id` (`home_team_id`),
  KEY `match_ts_index` (`match_ts`),
  KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛数据';

-- ----------------------------
-- Table structure for collect_snooker_match_live
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_match_live`;
CREATE TABLE `collect_snooker_match_live` (
  `id` int NOT NULL COMMENT '赛事id',
  `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `status` int NOT NULL DEFAULT '0' COMMENT '赛事状态',
  `seconds` int NOT NULL COMMENT '小节剩余时间(秒)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='snooker实时统计';

-- ----------------------------
-- Table structure for collect_snooker_season
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_season`;
CREATE TABLE `collect_snooker_season` (
  `id` int NOT NULL,
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
  `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
  `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
  `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
  `updated_at` bigint NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';

-- ----------------------------
-- Table structure for collect_snooker_team
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_team`;
CREATE TABLE `collect_snooker_team` (
  `id` int NOT NULL,
  `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
  `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
  `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
  `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
  `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
  `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
  `abbr` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩写',
  `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'logo',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别，1-男、2-女、0-未知',
  `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
  `uid` tinyint NOT NULL DEFAULT '0' COMMENT '球队id(重复球队合并后的对应id)',
  `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
  `extra` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '个人统计数据（没有数据，字段不存在）',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';

-- ----------------------------
-- Table structure for collect_snooker_team_ranking
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_team_ranking`;
CREATE TABLE `collect_snooker_team_ranking` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '球队id',
  `type_id` int NOT NULL DEFAULT '0' COMMENT '榜单类型，1-世界排名、2-BETVICTOR 系列赛排名、3-CAZOO 系列赛排名、4-近1年排名',
  `ranking` int NOT NULL DEFAULT '0' COMMENT '名次',
  `price` int NOT NULL DEFAULT '0' COMMENT '奖金',
  `price_mode` int NOT NULL DEFAULT '0' COMMENT '奖金单位，1-英镑£',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_rank_pk2` (`team_id`,`type_id`,`ranking`)
) ENGINE=InnoDB AUTO_INCREMENT=2712965 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队排行榜';

-- ----------------------------
-- Table structure for collect_snooker_team_stats
-- ----------------------------
DROP TABLE IF EXISTS `collect_snooker_team_stats`;
CREATE TABLE `collect_snooker_team_stats` (
  `id` int NOT NULL,
  `team_id` int NOT NULL DEFAULT '0' COMMENT '球队id',
  `played` int NOT NULL DEFAULT '0' COMMENT '总局数',
  `won` int NOT NULL DEFAULT '0' COMMENT '胜局数',
  `lost` int NOT NULL DEFAULT '0' COMMENT '负局数',
  `won_rate` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '胜率',
  `scores` int NOT NULL DEFAULT '0' COMMENT '总得分',
  `scores_per` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '每局平均得分',
  `over_fifty` int NOT NULL DEFAULT '0' COMMENT '单杆超过50分次数',
  `over_fifty_rate` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '单杆超过50分基数',
  `over_hundred` int NOT NULL DEFAULT '0' COMMENT '单杆超过100分次数',
  `over_hundred_rate` float(10,2) NOT NULL DEFAULT '0.00' COMMENT '单杆超过100分基数',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_time` bigint NOT NULL,
  `update_time` bigint NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';

-- ----------------------------
-- Table structure for collect_task
-- ----------------------------
DROP TABLE IF EXISTS `collect_task`;
CREATE TABLE `collect_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '任务名，纯用来显示',
  `symbol` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '唯一标识，用来在代码中区分唯一的任务 ',
  `run_duration` int NOT NULL DEFAULT '0' COMMENT '任务执行间隔，单位秒， 0表示不可用',
  `status` int NOT NULL DEFAULT '1' COMMENT '任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住',
  `max_retry` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '失败后的最多重试次数 (0表示不进行重试',
  `updated_at` bigint NOT NULL COMMENT '更新时间，时间戳毫秒',
  `runStatus` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '这个任务的当前上下文状态，在代码中自定义，是一个json',
  `param` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '这个任务的一些参数，是一个json',
  `current_inst_id` int unsigned NOT NULL DEFAULT '0' COMMENT '当前正在执行的任务实例的id, 每次产生新任务前都要先检查一下当前任务是否已经完成',
  `rand_delay` int NOT NULL DEFAULT '0' COMMENT '任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='采集任务';

-- ----------------------------
-- Table structure for collect_task_inst
-- ----------------------------
DROP TABLE IF EXISTS `collect_task_inst`;
CREATE TABLE `collect_task_inst` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `task_id` int unsigned NOT NULL DEFAULT '0' COMMENT '关联 collect_task.id',
  `symbol` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '关联 collect_task.symbol',
  `inst_symbol` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '这个任务自己的标识',
  `retry_times` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '已经重试几次了',
  `status` int NOT NULL DEFAULT '0' COMMENT '1: 未完成， 2:已完成且成功， 3:执行失败， ',
  `create_time` bigint NOT NULL COMMENT '创建时间,时间戳毫秒',
  `update_time` bigint NOT NULL COMMENT '更新时间,时间戳毫秒',
  `run_status` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '本次任务执行的上下文信息，生成自collect_task.run_status',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59385 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='采集任务里具体要被执行的任务实例';

-- ----------------------------
-- Table structure for collect_video_live
-- ----------------------------
DROP TABLE IF EXISTS `collect_video_live`;
CREATE TABLE `collect_video_live` (
  `sport_id` tinyint NOT NULL DEFAULT '0' COMMENT '1足球，2篮球',
  `match_id` int NOT NULL COMMENT '比赛id',
  `source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源，用来标识是从哪套接口/用什么方法采集到的',
  `pushurl1` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '推流链接1，rtmp协议',
  `pushurl2` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '推流链接2，rtmp协议',
  `pushurl3` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '推流链接3 rtmp协议',
  PRIMARY KEY (`sport_id`,`match_id`,`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='视频回放列表';

-- ----------------------------
-- Table structure for collect_video_live_from_a
-- ----------------------------
DROP TABLE IF EXISTS `collect_video_live_from_a`;
CREATE TABLE `collect_video_live_from_a` (
  `match_id` int NOT NULL COMMENT '比赛id',
  `url` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '跳转url',
  `class_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '篮球/足球',
  `streamers` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '视频流',
  UNIQUE KEY `match_id_idx` (`match_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='视频直播跳转url，数据从甲方提供的接口获取';

-- ----------------------------
-- Table structure for collect_video_record
-- ----------------------------
DROP TABLE IF EXISTS `collect_video_record`;
CREATE TABLE `collect_video_record` (
  `type_id` tinyint NOT NULL DEFAULT '0' COMMENT '1足球，2篮球',
  `match_id` bigint NOT NULL COMMENT '比赛id',
  `source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源标识，用来识别是用哪套接口/方法采集到的',
  `url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '回放链接',
  `cover` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '封面图片',
  PRIMARY KEY (`type_id`,`match_id`,`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='视频回放列表';

-- ----------------------------
-- Table structure for collect_video_record_id
-- ----------------------------
DROP TABLE IF EXISTS `collect_video_record_id`;
CREATE TABLE `collect_video_record_id` (
  `type_id` tinyint NOT NULL DEFAULT '0' COMMENT '1足球，2篮球',
  `match_id` int NOT NULL COMMENT '比赛id',
  `source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源标识，用来识别是用哪套接口/方法采集到的',
  `url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '回放链接',
  `cover` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '封面图片',
  PRIMARY KEY (`type_id`,`match_id`,`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='视频回放列表-印尼';

-- ----------------------------
-- Table structure for columns
-- ----------------------------
DROP TABLE IF EXISTS `columns`;
CREATE TABLE `columns` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `pid` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `tid` int NOT NULL DEFAULT '0' COMMENT '顶级id(已经是顶级则为自己)',
  `children_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '所有子栏目id们(不包含自己)',
  `level` tinyint NOT NULL DEFAULT '0' COMMENT '分类层次',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `alias` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '别名(由英文字母数字和._/组成并以/开头)',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:显示 2:隐藏)',
  `link_property` tinyint NOT NULL DEFAULT '1' COMMENT '链接属性(1:新窗口打开 2:nofollow)',
  `desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '描述',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `banner` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'banner图',
  `model` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '模型(单页/链接/文章)',
  `detail_router` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '详情页路由',
  `module_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT '所属模块id(关联module id)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` bigint NOT NULL COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `tdk_tmpl_category` tinyint NOT NULL DEFAULT '1' COMMENT 'tdk模板页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目)',
  `language` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '语言(中/英/印尼......)',
  `curr_period` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '当前期',
  `next_period_time` bigint NOT NULL DEFAULT '0' COMMENT '下期开奖时间',
  `five_elements` json NOT NULL COMMENT '五行+生肖',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=46937 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='栏目';

-- ----------------------------
-- Table structure for columns_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `columns_group_relation`;
CREATE TABLE `columns_group_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `col_id` int unsigned NOT NULL COMMENT '栏目原id',
  `rel_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组ID',
  `pid` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `tid` int NOT NULL DEFAULT '0' COMMENT '顶级id(已经是顶级则为自己)',
  `children_ids` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '所有子栏目id们(不包含自己)',
  `level` tinyint NOT NULL DEFAULT '0' COMMENT '分类层次',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `alias` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '别名(由英文字母数字和._/组成并以/开头)',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态(1:显示 2:隐藏)',
  `link_property` tinyint NOT NULL DEFAULT '1' COMMENT '链接属性(1:新窗口打开 2:nofollow)',
  `desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '描述',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `banner` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'banner图',
  `model` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '模型(单页/链接/文章)',
  `detail_router` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '详情页路由',
  `module_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '所属模块id(关联collect_xxx_competition id)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` bigint NOT NULL COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `tdk_tmpl_category` tinyint NOT NULL DEFAULT '1' COMMENT 'tdk模板页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目)',
  `language` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '语言(中/英/印尼......)',
  `curr_period` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '当前期',
  `next_period_time` bigint NOT NULL DEFAULT '0' COMMENT '下期开奖时间',
  `five_elements` json NOT NULL COMMENT '五行+生肖',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42247046 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='分组栏目';

-- ----------------------------
-- Table structure for columns_site_relation
-- ----------------------------
DROP TABLE IF EXISTS `columns_site_relation`;
CREATE TABLE `columns_site_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `col_id` int unsigned NOT NULL COMMENT '栏目原id',
  `rel_id` int unsigned NOT NULL DEFAULT '0' COMMENT '站点ID',
  `pid` int NOT NULL DEFAULT '0' COMMENT '父级ID',
  `tid` int NOT NULL DEFAULT '0' COMMENT '顶级id(已经是顶级则为自己)',
  `children_ids` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '所有子栏目id们(不包含自己)',
  `level` tinyint NOT NULL DEFAULT '0' COMMENT '分类层次',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `alias` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '别名(由英文字母数字和._/组成并以/开头)',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态(1:显示 2:隐藏)',
  `link_property` tinyint NOT NULL DEFAULT '1' COMMENT '链接属性(1:新窗口打开 2:nofollow)',
  `desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '描述',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `banner` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'banner图',
  `model` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '模型(单页/链接/文章)',
  `detail_router` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '详情页路由',
  `module_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT '所属模块id(关联collect_xxx_competition id)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` bigint NOT NULL COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `tdk_tmpl_category` tinyint NOT NULL DEFAULT '1' COMMENT 'tdk模板页面类型(1:赛程栏目 2:录像栏目 3:新闻栏目)',
  `language` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '语言(中/英/印尼......)',
  `curr_period` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '当前期',
  `next_period_time` bigint NOT NULL DEFAULT '0' COMMENT '下期开奖时间',
  `five_elements` json NOT NULL COMMENT '五行+生肖',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=50920120 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='站点栏目';

-- ----------------------------
-- Table structure for competition_stats
-- ----------------------------
DROP TABLE IF EXISTS `competition_stats`;
CREATE TABLE `competition_stats` (
  `id` int NOT NULL COMMENT '赛事id',
  `shooters` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '射手榜 ，json, 详情见文档',
  `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '积分榜， json, 详情见接口文档',
  `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
  `promotions` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '升降级列表, json, 详情见文档',
  `player_stats` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '球员数据 - 只预留字段，因为这个数据非常大，先不拉取',
  `teams_stats` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球队统计 - 预留字段但先不拉取， 因为这个字段太大',
  `updated_at` int NOT NULL DEFAULT '0' COMMENT '统计数据（除积分榜外的数据)的更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事的统计数据+积分榜';

-- ----------------------------
-- Table structure for crawler_news_raw
-- ----------------------------
DROP TABLE IF EXISTS `crawler_news_raw`;
CREATE TABLE `crawler_news_raw` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源 （比如新浪体育',
  `docid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '在原始来源处的唯一id',
  `title` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '标题',
  `url` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '原始链接',
  `intro` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '摘要',
  `competition_id` int NOT NULL DEFAULT '0' COMMENT '关联的赛事id,  为0表示还未关联',
  `source_cate` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '在原始来源里的分类标识 （比如新浪就是media_name)',
  `create_time` bigint DEFAULT NULL,
  `update_time` bigint NOT NULL,
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '文章正文',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型。 1足球，2篮球',
  `is_video` tinyint NOT NULL DEFAULT '0' COMMENT '是否视频(1是 2否)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `crawler_news_raw_pk2` (`source`,`docid`)
) ENGINE=InnoDB AUTO_INCREMENT=17949669 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原始新闻';

-- ----------------------------
-- Table structure for crawler_news_raw_content
-- ----------------------------
DROP TABLE IF EXISTS `crawler_news_raw_content`;
CREATE TABLE `crawler_news_raw_content` (
  `id` bigint NOT NULL DEFAULT '0',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '文章正文',
  `content_filter` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '过滤标签后的内容',
  `is_filter` tinyint NOT NULL DEFAULT '0' COMMENT '是否过滤 0-否1-是'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for cron_job
-- ----------------------------
DROP TABLE IF EXISTS `cron_job`;
CREATE TABLE `cron_job` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '任务名，纯用来显示',
  `symbol` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '唯一标识，用来在代码中区分唯一的任务 ',
  `status` int NOT NULL DEFAULT '1' COMMENT '任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住',
  `max_retry` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '失败后的最多重试次数 (0表示不进行重试',
  `updated_at` bigint NOT NULL COMMENT '更新时间，时间戳毫秒',
  `param` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '这个任务的一些参数，是一个json',
  `rand_delay` int NOT NULL DEFAULT '0' COMMENT '任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。 ',
  `cron` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'crontab string',
  `progress` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '当前进度，自定义的json',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='定时任务';

-- ----------------------------
-- Table structure for cron_job_record
-- ----------------------------
DROP TABLE IF EXISTS `cron_job_record`;
CREATE TABLE `cron_job_record` (
  `id` int NOT NULL AUTO_INCREMENT,
  `symbol` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'cron_job表里的标识',
  `run_at` bigint NOT NULL DEFAULT '0' COMMENT '执行时间',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '成功还是失败。 1成功',
  `progress` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '当前执行的状态',
  `retry` int NOT NULL DEFAULT '0' COMMENT '这是第几次重试',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8201611 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='定时任务执行记录';

-- ----------------------------
-- Table structure for crypto_rates_gainers
-- ----------------------------
DROP TABLE IF EXISTS `crypto_rates_gainers`;
CREATE TABLE `crypto_rates_gainers` (
  `rank` int DEFAULT NULL,
  `crypto_symbol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `price` decimal(30,8) DEFAULT NULL,
  `change_24h` decimal(10,6) DEFAULT NULL,
  `change_1h` decimal(10,6) DEFAULT NULL,
  `change_7d` decimal(10,6) DEFAULT NULL,
  `market_cap` decimal(40,8) DEFAULT NULL,
  `volume_24h` decimal(40,8) DEFAULT NULL,
  `is_trending` tinyint(1) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  PRIMARY KEY (`crypto_symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for crypto_rates_header
-- ----------------------------
DROP TABLE IF EXISTS `crypto_rates_header`;
CREATE TABLE `crypto_rates_header` (
  `rank` int DEFAULT NULL,
  `crypto_symbol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `price` decimal(30,8) DEFAULT NULL,
  `change_24h` decimal(10,6) DEFAULT NULL,
  `change_1h` decimal(10,6) DEFAULT NULL,
  `change_7d` decimal(10,6) DEFAULT NULL,
  `market_cap` decimal(40,8) DEFAULT NULL,
  `volume_24h` decimal(40,8) DEFAULT NULL,
  `is_trending` tinyint(1) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  PRIMARY KEY (`crypto_symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for crypto_rates_index
-- ----------------------------
DROP TABLE IF EXISTS `crypto_rates_index`;
CREATE TABLE `crypto_rates_index` (
  `rank` int DEFAULT NULL,
  `crypto_symbol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `price` decimal(30,8) DEFAULT NULL,
  `change_24h` decimal(10,6) DEFAULT NULL,
  `change_1h` decimal(10,6) DEFAULT NULL,
  `change_7d` decimal(10,6) DEFAULT NULL,
  `market_cap` decimal(40,8) DEFAULT NULL,
  `volume_24h` decimal(40,8) DEFAULT NULL,
  `is_trending` tinyint(1) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  PRIMARY KEY (`crypto_symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for crypto_rates_loosers
-- ----------------------------
DROP TABLE IF EXISTS `crypto_rates_loosers`;
CREATE TABLE `crypto_rates_loosers` (
  `rank` int DEFAULT NULL,
  `crypto_symbol` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `price` decimal(30,8) DEFAULT NULL,
  `change_24h` decimal(10,6) DEFAULT NULL,
  `change_1h` decimal(10,6) DEFAULT NULL,
  `change_7d` decimal(10,6) DEFAULT NULL,
  `market_cap` decimal(40,8) DEFAULT NULL,
  `volume_24h` decimal(40,8) DEFAULT NULL,
  `is_trending` tinyint(1) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
  PRIMARY KEY (`crypto_symbol`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for domain
-- ----------------------------
DROP TABLE IF EXISTS `domain`;
CREATE TABLE `domain` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '域名',
  `belong_user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属用户',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(1:正常 2:过户中)',
  `used` int NOT NULL DEFAULT '2' COMMENT '使用情况(1:已用 2:待用)',
  `length` int NOT NULL DEFAULT '0' COMMENT '域名长度',
  `suffix` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '域名后缀',
  `registrar` int unsigned NOT NULL DEFAULT '0' COMMENT '注册商',
  `account` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '平台账号',
  `register_time` bigint NOT NULL DEFAULT '0' COMMENT '注册时间',
  `expire_time` bigint NOT NULL DEFAULT '0' COMMENT '到期时间',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `price` double NOT NULL DEFAULT '0' COMMENT '价格',
  `language` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '语言',
  `server_id` int NOT NULL DEFAULT '0' COMMENT '所属服务器',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35856 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='域名管理';

-- ----------------------------
-- Table structure for friendly_link
-- ----------------------------
DROP TABLE IF EXISTS `friendly_link`;
CREATE TABLE `friendly_link` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组/站点id',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `belong` tinyint NOT NULL DEFAULT '0' COMMENT '所属(0:自身 1:分组 2:站点)',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型(1:文字 2:图片)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '链接',
  `expired_time` bigint NOT NULL DEFAULT '0' COMMENT '链接有限期',
  `resource` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '链接来源',
  `web_type` bigint NOT NULL DEFAULT '0' COMMENT '网站类型(1: 企业 2:私人)',
  `baidu_weights` double NOT NULL DEFAULT '0' COMMENT '百度权重',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `property` bigint NOT NULL DEFAULT '0' COMMENT '链接属性(1:新窗口打开 2:nofollow)',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '备注',
  `image_size` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '图片尺寸',
  `image` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '图片',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `link_type` tinyint NOT NULL DEFAULT '1' COMMENT '友链类型(1:分组新增 2:手动新增)',
  `rule_id` bigint NOT NULL DEFAULT '0' COMMENT '规则id',
  PRIMARY KEY (`id`),
  KEY `friendly_link_belong_id_delete_time_index` (`belong_id`,`delete_time`)
) ENGINE=InnoDB AUTO_INCREMENT=34442 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='友链';

-- ----------------------------
-- Table structure for friendly_link_rule
-- ----------------------------
DROP TABLE IF EXISTS `friendly_link_rule`;
CREATE TABLE `friendly_link_rule` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` int unsigned NOT NULL DEFAULT '0' COMMENT '类型(1单链 2互链)',
  `source_id` int unsigned NOT NULL DEFAULT '0' COMMENT '源组id',
  `target_id` int unsigned NOT NULL DEFAULT '0' COMMENT '目标组id',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `expired_time` bigint NOT NULL DEFAULT '0' COMMENT '链接有限期',
  `friendly_links` int unsigned NOT NULL DEFAULT '0' COMMENT '友链数量',
  `link_type` tinyint NOT NULL DEFAULT '1' COMMENT '友链类型(1:分组新增 2:手动新增)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `friendly_link_belong_id_delete_time_index` (`type`,`delete_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='友链单链互链规则';

-- ----------------------------
-- Table structure for friendly_link_rule_list
-- ----------------------------
DROP TABLE IF EXISTS `friendly_link_rule_list`;
CREATE TABLE `friendly_link_rule_list` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` int unsigned NOT NULL DEFAULT '0' COMMENT '规则id，关联friendly_link_rule',
  `source_id` int unsigned NOT NULL DEFAULT '0' COMMENT '源站id',
  `target_id` int unsigned NOT NULL DEFAULT '0' COMMENT '目标站id',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `friendly_link_belong_id_delete_time_index` (`rule_id`,`delete_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=533 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin ROW_FORMAT=DYNAMIC COMMENT='友链单链互链列表，根据friendly_link_rule自动生成';

-- ----------------------------
-- Table structure for keyword
-- ----------------------------
DROP TABLE IF EXISTS `keyword`;
CREATE TABLE `keyword` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong` tinyint NOT NULL DEFAULT '0' COMMENT '所属(0:自身 1:分组 2:站点)',
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组/站点id',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '链接',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `link_property` tinyint NOT NULL DEFAULT '1' COMMENT '链接属性(1:新窗口打开 2:nofollow)',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='关键词';

-- ----------------------------
-- Table structure for label
-- ----------------------------
DROP TABLE IF EXISTS `label`;
CREATE TABLE `label` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组/站点id',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `belong` tinyint NOT NULL DEFAULT '0' COMMENT '所属(0:自身 1:分组 2:站点)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `clicks` int NOT NULL DEFAULT '0' COMMENT '点击数',
  `rel_news_count` int NOT NULL DEFAULT '0' COMMENT '文章数',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_used` tinyint NOT NULL DEFAULT '1' COMMENT '是否常用(1:是 2:否)',
  `is_recommend` tinyint NOT NULL DEFAULT '1' COMMENT '是否推荐(1:是 2:否)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:显示 2:隐藏)',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '路由(唯一)',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `label_belong_id_status_delete_time_index` (`belong_id`,`status`,`delete_time`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='标签';

-- ----------------------------
-- Table structure for label_other_relation
-- ----------------------------
DROP TABLE IF EXISTS `label_other_relation`;
CREATE TABLE `label_other_relation` (
  `label_id` int unsigned NOT NULL DEFAULT '0' COMMENT '标签id',
  `rel_id` int unsigned NOT NULL DEFAULT '0' COMMENT '关联者id',
  `rel_type` int NOT NULL DEFAULT '0' COMMENT '关联者类型(1:新闻资讯 2:足球比赛 3:篮球比赛 4:xxx......)',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '分组/站点id',
  `belong` tinyint NOT NULL DEFAULT '0' COMMENT '所属(0:自身 1:分组 2:站点)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='标签关联表(标签关联其它数据，如文章/球队/球员/比赛等)';

-- ----------------------------
-- Table structure for local_image
-- ----------------------------
DROP TABLE IF EXISTS `local_image`;
CREATE TABLE `local_image` (
  `table` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '表名',
  `id` int NOT NULL COMMENT '表中的id',
  `column` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '表中字段名',
  `create_time` bigint NOT NULL DEFAULT '0',
  `url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `old_url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  PRIMARY KEY (`id`,`table`,`column`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

-- ----------------------------
-- Table structure for module
-- ----------------------------
DROP TABLE IF EXISTS `module`;
CREATE TABLE `module` (
  `id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '对应collect_xxx_competition id',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

-- ----------------------------
-- Table structure for news
-- ----------------------------
DROP TABLE IF EXISTS `news`;
CREATE TABLE `news` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_col_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属栏目id',
  `belong_col_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '所属栏目名称',
  `belong_group_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属分组',
  `belong_site_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属站点',
  `title` varchar(510) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '标题',
  `desc` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '简介',
  `label` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '标签',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `file_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '自定义文件名',
  `author` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '作者',
  `resource` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源',
  `views` int NOT NULL DEFAULT '0' COMMENT '浏览量',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:显示 2:隐藏)',
  `attr` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '属性(1:视频)',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '发布时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `is_hot` tinyint NOT NULL DEFAULT '0' COMMENT '是否热门（1是 2否）',
  `is_auto` tinyint NOT NULL COMMENT '是否自动（1是0否）',
  `pdf` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'pdf查看链接',
  PRIMARY KEY (`id`),
  UNIQUE KEY `title_site_id` (`belong_site_id`,`title`) USING BTREE,
  KEY `create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=839426 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='新闻资讯';

-- ----------------------------
-- Table structure for news_ai_get
-- ----------------------------
DROP TABLE IF EXISTS `news_ai_get`;
CREATE TABLE `news_ai_get` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '标题',
  `content` longtext CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '文章正文',
  `create_time` bigint DEFAULT NULL,
  `update_time` bigint NOT NULL,
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型。 1足球，2篮球',
  `is_pass` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态(1通过 2不通过)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1701 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ai公司提供的文章';

-- ----------------------------
-- Table structure for news_body
-- ----------------------------
DROP TABLE IF EXISTS `news_body`;
CREATE TABLE `news_body` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `news_id` int unsigned NOT NULL COMMENT '新闻id',
  `body` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '内容',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `idx_news_id` (`news_id`)
) ENGINE=InnoDB AUTO_INCREMENT=784855 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='新闻资讯内容';

-- ----------------------------
-- Table structure for permission_attrs
-- ----------------------------
DROP TABLE IF EXISTS `permission_attrs`;
CREATE TABLE `permission_attrs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `permission_id` int unsigned NOT NULL COMMENT '权限id',
  `url_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api接口路径',
  `masked_fields` json DEFAULT NULL COMMENT '脱敏字段配置',
  `order_by` int NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_id` (`permission_id`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=623 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限表（存储每个菜单所有的操作权限）';

-- ----------------------------
-- Table structure for permission_node
-- ----------------------------
DROP TABLE IF EXISTS `permission_node`;
CREATE TABLE `permission_node` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `p_id` int unsigned NOT NULL COMMENT '父权限id（顶级为0）',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单编号（权限编号）',
  `label` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '显示名称（中），逗号分隔',
  `order_by` int NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=591 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限表（存储每个菜单所有的操作权限）';

-- ----------------------------
-- Table structure for risk_control_content
-- ----------------------------
DROP TABLE IF EXISTS `risk_control_content`;
CREATE TABLE `risk_control_content` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tab_type` int NOT NULL DEFAULT '0' COMMENT '分类 [1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单 ； 不包含 5 地区屏蔽,  6 CC防御]',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '（1） tab_type = 1时 对应 IP白名单地址； \n（2） tab_type = 2时 对应 IP黑名单地址； \n（3） tab_type = 3时 对应 UA白名单配置； \n（4） tab_type = 4时 对应 UA黑名单配置',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '状态 [ 1 开 2 关]',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=175 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='站群风控内容配置';

-- ----------------------------
-- Table structure for risk_control_tab
-- ----------------------------
DROP TABLE IF EXISTS `risk_control_tab`;
CREATE TABLE `risk_control_tab` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `tab_type` int NOT NULL DEFAULT '0' COMMENT '栏目分类[1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单,  5 地区屏蔽,  6 CC防御]',
  `tab_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '栏目名称',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '状态 [ 1 开 2 关]',
  `is_pass` int NOT NULL DEFAULT '0' COMMENT '类型[ 1 白名单命中通过 2 黑名单命中不通过]',
  `is_list` int NOT NULL DEFAULT '0' COMMENT '栏目内容 [ 1 列表  2 功能配置 ]',
  `metas` json DEFAULT NULL COMMENT '功能属性：tab_type = 5时 对应 地区屏蔽配置 格式：\n   {"BlockUserSwitch": 1, "BlockSpiderSwitch": 1}\n            屏蔽用户：1开 2关     屏蔽蜘蛛：1开 2关\n；  tab_type = 6时 对应 CC防御配置  格式要求如下： {"DefenseSwitch": 1, "TriggerFrequency": 1}\n   防御开关 ：1开 2关     触发频率：90-150左右',
  `priority` int NOT NULL DEFAULT '0' COMMENT '访问优先级',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_tab_type` (`tab_type`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='站群风控栏目配置';

-- ----------------------------
-- Table structure for robots
-- ----------------------------
DROP TABLE IF EXISTS `robots`;
CREATE TABLE `robots` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'name',
  `content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'content',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='robots';

-- ----------------------------
-- Table structure for role_permission_config
-- ----------------------------
DROP TABLE IF EXISTS `role_permission_config`;
CREATE TABLE `role_permission_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `role_level` tinyint NOT NULL COMMENT '角色层级：1管理员 2站长 3站员',
  `label` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称（中）',
  `permission_set` json DEFAULT NULL COMMENT '权限集合：id数组',
  `masked_fields` json DEFAULT NULL COMMENT '掩码字段 ：map结构{id: {k1:v1, k2:v2}}',
  `remark` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `order_by` int NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`,`delete_time`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表（存储所有角色）';

-- ----------------------------
-- Table structure for router
-- ----------------------------
DROP TABLE IF EXISTS `router`;
CREATE TABLE `router` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `site_id` int unsigned NOT NULL COMMENT '站点id(关联site表)',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
  `relation_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0' COMMENT '关联id(关联collect_xxx_competition表/label表等)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `site_url` (`site_id`,`url`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3953137 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='路由表(同一个站点下的url唯一)';

-- ----------------------------
-- Table structure for selector_config_content
-- ----------------------------
DROP TABLE IF EXISTS `selector_config_content`;
CREATE TABLE `selector_config_content` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `select_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '选择器类型对应的配置名称',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '状态 [ 1 启用  2 禁用]',
  `sort` int NOT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `extra` json NOT NULL COMMENT '额外数据：如注册商底下的平台账号[{"account":"xx"}]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2830 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='数据配置 内容配置';

-- ----------------------------
-- Table structure for selector_config_tab
-- ----------------------------
DROP TABLE IF EXISTS `selector_config_tab`;
CREATE TABLE `selector_config_tab` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `select_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]',
  `tab_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '栏目名称',
  `is_open` int NOT NULL DEFAULT '0' COMMENT '状态 [ 1 开 2 关]',
  `is_list` int NOT NULL DEFAULT '0' COMMENT '栏目内容 [ 1 列表  2 功能配置 ]',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_tab_type` (`select_type`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='数据配置 tab配置';

-- ----------------------------
-- Table structure for sensitive
-- ----------------------------
DROP TABLE IF EXISTS `sensitive`;
CREATE TABLE `sensitive` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `class_id` int NOT NULL COMMENT '分类ID',
  `word` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '敏感词',
  `is_open` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用：1开 2关',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `word` (`word`)
) ENGINE=InnoDB AUTO_INCREMENT=1821 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='敏感词表';

-- ----------------------------
-- Table structure for sensitive_category
-- ----------------------------
DROP TABLE IF EXISTS `sensitive_category`;
CREATE TABLE `sensitive_category` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int NOT NULL COMMENT '分类ID',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '敏感词',
  `is_open` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用：1开 2关',
  `desc` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '敏感词描述',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1831 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='敏感词类别表';

-- ----------------------------
-- Table structure for seo_index
-- ----------------------------
DROP TABLE IF EXISTS `seo_index`;
CREATE TABLE `seo_index` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `url` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
  `keywords` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '关键词，快照时为空',
  `type` int NOT NULL DEFAULT '0' COMMENT '0-快照，1-索引',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `has_index` int NOT NULL DEFAULT '0' COMMENT '0-未收录，1-已收录',
  `has_send` int NOT NULL DEFAULT '0' COMMENT '0-未查询，1-已查询',
  `uuid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'uuid',
  `created_at` bigint NOT NULL DEFAULT '0' COMMENT '插入时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `title` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT 'title',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=105932 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='排名查询';

-- ----------------------------
-- Table structure for seo_index_title
-- ----------------------------
DROP TABLE IF EXISTS `seo_index_title`;
CREATE TABLE `seo_index_title` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `url` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
  `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'title',
  `link` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'link',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `created_at` bigint NOT NULL DEFAULT '0' COMMENT '插入时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11849 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='index收录的title和对应的url';

-- ----------------------------
-- Table structure for seo_rank
-- ----------------------------
DROP TABLE IF EXISTS `seo_rank`;
CREATE TABLE `seo_rank` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
  `keywords` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '关键词',
  `taskid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '任务id',
  `json` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '返回json',
  `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `has_get` int NOT NULL DEFAULT '0' COMMENT '0-未获取结果，1-已获取结果',
  `type` int NOT NULL DEFAULT '0' COMMENT '0-pc，1-mobile',
  `uuid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'uuid',
  `send_at` bigint NOT NULL DEFAULT '0' COMMENT '发送查询时间',
  `created_at` bigint NOT NULL DEFAULT '0' COMMENT '插入时间',
  `has_send` int NOT NULL DEFAULT '0' COMMENT '0-未发送，1-已发送',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `has_retry` int unsigned NOT NULL DEFAULT '0' COMMENT '0-未重试，1-已重试',
  `old_taskid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'old任务id',
  `is_rank` int NOT NULL DEFAULT '0' COMMENT '查询类型 0-普通查询，1-关键词排名',
  `delete_at` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `has_rank` int NOT NULL DEFAULT '0' COMMENT '1-有排名/0-无排名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=198915 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='排名查询';

-- ----------------------------
-- Table structure for signal_click_record
-- ----------------------------
DROP TABLE IF EXISTS `signal_click_record`;
CREATE TABLE `signal_click_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL DEFAULT '0' COMMENT '直播信号源id',
  `clicks` int NOT NULL DEFAULT '0' COMMENT '当天点击量',
  `record_day` date NOT NULL COMMENT '当天日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_product_day` (`product_id`,`record_day`)
) ENGINE=InnoDB AUTO_INCREMENT=1378 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='直播信号源点击记录表';

-- ----------------------------
-- Table structure for single_page
-- ----------------------------
DROP TABLE IF EXISTS `single_page`;
CREATE TABLE `single_page` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_group_id` int unsigned NOT NULL COMMENT '所属分组id',
  `belong_site_id` int unsigned NOT NULL COMMENT '所属站点id',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '页面名称',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
  `desc` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '简介',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `seo_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo关键词',
  `seo_desc` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'seo描述',
  `thumb` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩略图',
  `banner` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'banner图',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型(1:关于我们 2:联系我们)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`),
  KEY `single_page_belong_group_id_belong_site_id_delete_time_index` (`belong_group_id`,`belong_site_id`,`delete_time`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='单页';

-- ----------------------------
-- Table structure for site
-- ----------------------------
DROP TABLE IF EXISTS `site`;
CREATE TABLE `site` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `language` varchar(255) NOT NULL DEFAULT '' COMMENT '语言',
  `group_id` int unsigned NOT NULL COMMENT '组id',
  `domain_id` int unsigned NOT NULL COMMENT '域名id',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:关闭)',
  `running_time` bigint NOT NULL DEFAULT '0' COMMENT '开始运营时间',
  `tmpl_id` int unsigned NOT NULL DEFAULT '0' COMMENT '模版id',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `create_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
  `icp` varchar(255) NOT NULL DEFAULT '' COMMENT '备案号',
  `picp` varchar(255) NOT NULL DEFAULT '' COMMENT '公安备案号',
  `seo_title` varchar(255) NOT NULL COMMENT '首页title',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '首页关键词',
  `seo_desc` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '首页描述',
  `statistics_code` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '统计代码',
  `baidu_push` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '百度推送',
  `shenma_push_account` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '神马推送-站长平台账号',
  `shenma_push_authkey` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '神马推送-域名authkey',
  `toutiao_push` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '今日头条推送',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT '站点logo',
  `favicon` varchar(255) NOT NULL DEFAULT '' COMMENT '网站图标',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `views` int NOT NULL DEFAULT '0' COMMENT '每日访问次数',
  `total_views` int NOT NULL DEFAULT '0' COMMENT '历史总访问次数',
  `tdk_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应tdk_tmpl表的id',
  `url_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应url_settting表的id',
  `seo_status` int unsigned NOT NULL COMMENT 'seo状态',
  `baidu_verify_code` varchar(255) NOT NULL COMMENT '百度站长平台验证码',
  `auto_publish_news` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示信号源',
  `google_verify_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '谷歌站长平台验证码',
  `key_updates_news` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示广告',
  `auto_publish_news_time` bigint NOT NULL DEFAULT '0' COMMENT '自动发布新闻时间',
  `has_auto_publish_news_num` int NOT NULL DEFAULT '0' COMMENT '今天已自动发布新闻篇数',
  `baidu` int unsigned NOT NULL DEFAULT '0' COMMENT '百度收录的数量',
  `sougou` int unsigned NOT NULL DEFAULT '0' COMMENT '搜狗收录的数量',
  `domain_jump` tinyint NOT NULL DEFAULT '0' COMMENT '域名跳转(1-www跳@，2-@跳www)',
  `jump301` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '301站点',
  `video_products_show_pc` tinyint NOT NULL DEFAULT '1' COMMENT '直播产品是否显示pc端(1:是 2:否)',
  `video_products_show_mobile` tinyint NOT NULL DEFAULT '1' COMMENT '直播产品是否显示移动端(1:是 2:否)',
  `open_referer` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)',
  `open_video_products_ctrl` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)',
  `video_products_ctrl_sec` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `video_products_ctrl_min` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `public_welfare_show` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示公益信号',
  `video_products_ctrl_min_end` int NOT NULL COMMENT '开赛后多少分钟',
  `open_signal_domain` tinyint NOT NULL DEFAULT '0' COMMENT '开启信号域名跳转(1是 2否)',
  `video_products_show_setting` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播产品显示设置',
  `open_referer_mobile` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)',
  `video_products_ctrl_sec_mobile` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `open_video_products_ctrl_mobile` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)',
  `video_products_ctrl_min_mobile` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `public_welfare_show_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示公益信号',
  `video_products_ctrl_min_end_mobile` int NOT NULL COMMENT '开赛后多少分钟',
  `open_signal_domain_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '开启信号域名跳转(1是 2否)',
  `video_products_show_setting_mobile` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播产品显示设置',
  `video_products` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播源产品列表',
  `use_products` tinyint NOT NULL COMMENT '是否使用直播产品（1是 0否）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25365 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='站点';

-- ----------------------------
-- Table structure for site_child
-- ----------------------------
DROP TABLE IF EXISTS `site_child`;
CREATE TABLE `site_child` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `mother_id` int unsigned NOT NULL COMMENT '母站id',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `language` varchar(255) NOT NULL DEFAULT '' COMMENT '语言',
  `tmpl_id` int unsigned NOT NULL DEFAULT '0' COMMENT '模版id',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id',
  `create_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
  `icp` varchar(255) NOT NULL DEFAULT '' COMMENT '备案号',
  `picp` varchar(255) NOT NULL DEFAULT '' COMMENT '公安备案号',
  `seo_title` varchar(255) NOT NULL COMMENT '首页title',
  `seo_keyword` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '首页关键词',
  `seo_desc` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '首页描述',
  `statistics_code` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '统计代码',
  `baidu_push` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '百度推送',
  `shenma_push_account` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '神马推送-站长平台账号',
  `shenma_push_authkey` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '神马推送-域名authkey',
  `toutiao_push` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '今日头条推送',
  `logo` varchar(255) NOT NULL DEFAULT '' COMMENT '站点logo',
  `favicon` varchar(255) NOT NULL DEFAULT '' COMMENT '网站图标',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `tdk_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应tdk_tmpl表的id',
  `url_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应url_settting表的id',
  `baidu_verify_code` varchar(255) NOT NULL COMMENT '百度站长平台验证码',
  `google_verify_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '谷歌站长平台验证码',
  `group_id` int unsigned NOT NULL DEFAULT '0' COMMENT '信号源分组id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37548 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='子站点';

-- ----------------------------
-- Table structure for site_group
-- ----------------------------
DROP TABLE IF EXISTS `site_group`;
CREATE TABLE `site_group` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '组名',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(1:开启2:关闭)',
  `tmpl_id` int unsigned NOT NULL DEFAULT '0' COMMENT '模版id',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `video_products` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播源产品列表',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `tdk_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应tdk_tmpl表的id',
  `url_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应url_settting表的id',
  `video_products_show_pc` tinyint NOT NULL DEFAULT '1' COMMENT '直播产品是否显示pc端(1:是 2:否)',
  `video_products_show_mobile` tinyint NOT NULL DEFAULT '1' COMMENT '直播产品是否显示移动端(1:是 2:否)',
  `open_referer` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)',
  `open_video_products_ctrl` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)',
  `video_products_ctrl_sec` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `video_products_ctrl_min` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `language` varchar(255) NOT NULL DEFAULT '' COMMENT '语言',
  `public_welfare_show` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示公益信号',
  `video_products_ctrl_min_end` int NOT NULL COMMENT '开赛后多少分钟',
  `open_signal_domain` tinyint NOT NULL DEFAULT '0' COMMENT '开启信号域名跳转(1-二级域名跳转，2-iFrame框架跳转，3-二级导航页跳转，4-直连)',
  `video_products_show_setting` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播产品显示设置',
  `news_updates_num` int NOT NULL DEFAULT '0' COMMENT '新闻更新量',
  `open_referer_mobile` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)',
  `video_products_ctrl_sec_mobile` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `open_video_products_ctrl_mobile` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)',
  `video_products_ctrl_min_mobile` int NOT NULL DEFAULT '0' COMMENT '开赛前多少分钟',
  `public_welfare_show_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '是否展示公益信号',
  `video_products_ctrl_min_end_mobile` int NOT NULL COMMENT '开赛后多少分钟',
  `open_signal_domain_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '开启信号域名跳转(1是 2否)',
  `video_products_show_setting_mobile` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '直播产品显示设置',
  `model_fake` tinyint NOT NULL DEFAULT '0' COMMENT '模版伪原创(1-纯数字，2-纯字母，3-数字+字母，4-字母+数字)',
  `model_fake_bit` int NOT NULL DEFAULT '0' COMMENT '模版伪原创的位数',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `site_group_chk_1` CHECK (json_valid(`video_products`)),
  CONSTRAINT `site_group_chk_2` CHECK (json_valid(`video_products_show_setting`)),
  CONSTRAINT `site_group_chk_3` CHECK (json_valid(`video_products_show_setting_mobile`))
) ENGINE=InnoDB AUTO_INCREMENT=430 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='组';

-- ----------------------------
-- Table structure for sitemap
-- ----------------------------
DROP TABLE IF EXISTS `sitemap`;
CREATE TABLE `sitemap` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong` tinyint NOT NULL COMMENT '所属(1:分组 2:站点)',
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属分组/站点id',
  `format` tinyint NOT NULL DEFAULT '0' COMMENT '格式(1:xml地图 2:txt地图 3:html地图)',
  `create_type` tinyint NOT NULL DEFAULT '0' COMMENT '生成方式(1:手动 2:自动)',
  `main_refresh_rate` tinyint NOT NULL DEFAULT '0' COMMENT '首页更新频率(1:每天 2:每星期 3:每月)',
  `list_refresh_rate` tinyint NOT NULL DEFAULT '0' COMMENT '列表页更新频率(1:每天 2:每星期 3:每月)',
  `content_refresh_rate` tinyint NOT NULL DEFAULT '0' COMMENT '内容页更新频率(1:每天 2:每星期 3:每月)',
  `main_level` decimal(2,1) NOT NULL DEFAULT '0.0' COMMENT '首页优先级别',
  `list_level` decimal(2,1) NOT NULL DEFAULT '0.0' COMMENT '列表页优先级别',
  `content_level` decimal(2,1) NOT NULL DEFAULT '0.0' COMMENT '内容页优先级别',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `link_num` int NOT NULL DEFAULT '0' COMMENT '每个地图文件内链最大数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='站点地图';

-- ----------------------------
-- Table structure for spider
-- ----------------------------
DROP TABLE IF EXISTS `spider`;
CREATE TABLE `spider` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `logo` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '标志',
  `identification_criteria` tinyint NOT NULL DEFAULT '0' COMMENT '识别依据(1:仅UA识别 2:仅IP识别 3:UA+IP双识别 4:UA或IP识别)',
  `block_ip` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '屏蔽ip',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='蜘蛛管理';

-- ----------------------------
-- Table structure for spider_firewall
-- ----------------------------
DROP TABLE IF EXISTS `spider_firewall`;
CREATE TABLE `spider_firewall` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_group_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属分组id',
  `config` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '蜘蛛id列表',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='蜘蛛管理';

-- ----------------------------
-- Table structure for spider_log
-- ----------------------------
DROP TABLE IF EXISTS `spider_log`;
CREATE TABLE `spider_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `ip` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'IP',
  `city` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市',
  `access_url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '访问地址',
  `access_time` bigint NOT NULL DEFAULT '0' COMMENT '访问时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `status_code` int NOT NULL DEFAULT '0',
  `spider_id` int unsigned NOT NULL COMMENT '蜘蛛id',
  `domain_id` int unsigned NOT NULL COMMENT '访问的域名id',
  PRIMARY KEY (`id`),
  KEY `idx_access_time` (`access_time` DESC) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=220 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='蜘蛛管理，只保留今天的';

-- ----------------------------
-- Table structure for spider_log_black_ip
-- ----------------------------
DROP TABLE IF EXISTS `spider_log_black_ip`;
CREATE TABLE `spider_log_black_ip` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'IP',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ip` (`ip`) USING BTREE /*!80000 INVISIBLE */
) ENGINE=InnoDB AUTO_INCREMENT=145 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='蜘蛛管理';

-- ----------------------------
-- Table structure for store_base_config
-- ----------------------------
DROP TABLE IF EXISTS `store_base_config`;
CREATE TABLE `store_base_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储类型[local 本地存储, aws AWS亚马逊云存储]',
  `thumbnail_large_width` int NOT NULL DEFAULT '0' COMMENT '缩略大图宽',
  `thumbnail_large_height` int NOT NULL DEFAULT '0' COMMENT '缩略大图高',
  `thumbnail_medium_width` int NOT NULL DEFAULT '0' COMMENT '缩略中图宽',
  `thumbnail_medium_height` int NOT NULL DEFAULT '0' COMMENT '缩略中图高',
  `thumbnail_small_width` int NOT NULL DEFAULT '0' COMMENT '缩略小图宽',
  `thumbnail_small_height` int NOT NULL DEFAULT '0' COMMENT '缩略小图高',
  `is_open_watermark` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启水印： 1:开 2:关',
  `watermark_type` tinyint NOT NULL DEFAULT '0' COMMENT '水印类型： 1:图片 2:文字',
  `watermark_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '水印图片或水印文字',
  `watermark_location` int NOT NULL DEFAULT '0' COMMENT '水印位置',
  `watermark_opacity` int NOT NULL DEFAULT '0' COMMENT '水印透明度',
  `watermark_rotation` int NOT NULL DEFAULT '0' COMMENT '水印倾斜度',
  `watermark_horizontal` int NOT NULL DEFAULT '0' COMMENT '水印横坐标偏移量',
  `watermark_vertical` int NOT NULL DEFAULT '0' COMMENT '水印纵坐标偏移量',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置-存储基础配置';

-- ----------------------------
-- Table structure for store_cloud_config
-- ----------------------------
DROP TABLE IF EXISTS `store_cloud_config`;
CREATE TABLE `store_cloud_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `type` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储类型[local 本地存储, aws AWS亚马逊云存储]',
  `config` json NOT NULL COMMENT '配置',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统配置-存储云配置';

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `start_time` bigint NOT NULL DEFAULT '0' COMMENT '系统开始运行时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

-- ----------------------------
-- Table structure for tdk_tmpl
-- ----------------------------
DROP TABLE IF EXISTS `tdk_tmpl`;
CREATE TABLE `tdk_tmpl` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '规则名称',
  `belong` tinyint NOT NULL COMMENT '所属(1:分组 2:站点)',
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属分组/站点id',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `t_remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '标题模版说明',
  `t_config` json NOT NULL COMMENT '标题模版内容',
  `kw_remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '关键词模版说明',
  `kw_config` json NOT NULL COMMENT '关键词模版内容',
  `desc_remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '描述模版说明',
  `desc_config` json NOT NULL COMMENT '描述模版内容',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认(1:是 2:否)',
  `demo` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '演示例子',
  `language` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '语言',
  `other_language_ids` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '其它语言的tdk id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='tdk模版';

-- ----------------------------
-- Table structure for template
-- ----------------------------
DROP TABLE IF EXISTS `template`;
CREATE TABLE `template` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型(1:自适应 2:pc 3:h5)',
  `is_default` tinyint NOT NULL DEFAULT '2' COMMENT '是否默认模板(1:是 2:否)',
  `path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '模版所在文件夹',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:关闭)',
  `cover` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '封面',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `language` json DEFAULT NULL,
  `other_language_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '其它语言的模板id',
  `design_id` int NOT NULL DEFAULT '0' COMMENT '模板设计表id',
  `is_single` tinyint NOT NULL DEFAULT '2' COMMENT '是否单页模板(1:是 2:否)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=327 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='模版';

-- ----------------------------
-- Table structure for template_design
-- ----------------------------
DROP TABLE IF EXISTS `template_design`;
CREATE TABLE `template_design` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `setting_name` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '配置名称',
  `remark` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '说明',
  `setting_type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `config` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `image` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '图片',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='模板设计表';

-- ----------------------------
-- Table structure for tools
-- ----------------------------
DROP TABLE IF EXISTS `tools`;
CREATE TABLE `tools` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工具名称',
  `type` int NOT NULL DEFAULT '0' COMMENT '工具类别(关联数据配置)',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工具标签',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `seo_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'seo标题',
  `seo_keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'seo关键字',
  `seo_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'seo描述',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义url',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `creator` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态(1启用，2禁用)',
  `seo_list` json DEFAULT NULL COMMENT 'seo集合',
  `ui` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '界面代码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工具表';

-- ----------------------------
-- Table structure for url_setting
-- ----------------------------
DROP TABLE IF EXISTS `url_setting`;
CREATE TABLE `url_setting` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong` tinyint NOT NULL COMMENT '所属(1:分组 2:站点)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '名称',
  `self_id` int unsigned NOT NULL DEFAULT '0' COMMENT '自身id(如果belong非0,那这个指向的是id)',
  `belong_id` int unsigned NOT NULL DEFAULT '0' COMMENT '所属分组/站点id',
  `mode` tinyint NOT NULL DEFAULT '0' COMMENT '模式(1:动态url 2:伪静态 3:静态页面)',
  `static_suffix` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '静态后缀',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '说明',
  `config` json NOT NULL COMMENT '内容',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:启用 2:禁用)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `is_default` tinyint NOT NULL DEFAULT '0' COMMENT '是否默认(1:是 2:否)',
  `demo` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '演示例子',
  `model_value` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'model_value',
  `schedule_title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'schedule_title',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='url设置';

-- ----------------------------
-- Table structure for video_product
-- ----------------------------
DROP TABLE IF EXISTS `video_product`;
CREATE TABLE `video_product` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `belong_user_id` int unsigned NOT NULL COMMENT '所属用户id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:显示 2:隐藏)',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '产品名称',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '跳转url',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `allow_wildcard_domain` tinyint NOT NULL DEFAULT '1' COMMENT '是否支持泛域名(1:是 2:否)',
  `wildcard_domain_length` int NOT NULL DEFAULT '0' COMMENT '泛域名长度(字母+数字)',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  `is_video_record` tinyint NOT NULL DEFAULT '0' COMMENT '录像跳转域名(1是 2否)',
  `is_join_suff` tinyint NOT NULL DEFAULT '0' COMMENT '是否拼接直播间后缀',
  `type` int NOT NULL DEFAULT '0' COMMENT '类型(1乐球/2雨燕)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='直播源产品';

-- ----------------------------
-- Table structure for video_product_child
-- ----------------------------
DROP TABLE IF EXISTS `video_product_child`;
CREATE TABLE `video_product_child` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `pid` bigint NOT NULL DEFAULT '0' COMMENT '直播源产品id',
  `cid` bigint NOT NULL DEFAULT '0' COMMENT '新生成的子id',
  `name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '子名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='直播源产品关联表（方便统计查找源）';

-- ----------------------------
-- Procedure structure for proc_spider_log
-- ----------------------------
DROP PROCEDURE IF EXISTS `proc_spider_log`;
delimiter ;;
CREATE PROCEDURE `proc_spider_log`()
BEGIN
    -- 获取昨天日期，并格式化为适合表名的格式
    SET @yesterday = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%Y%m%d');
    
    -- 创建新的表以昨天日期命名
    SET @create_table_query = CONCAT('CREATE TABLE IF NOT EXISTS spider_log_', @yesterday, ' LIKE spider_log');
    PREPARE stmt FROM @create_table_query;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 将spider_log表的数据复制到新表
    SET @insert_data_query = CONCAT('INSERT INTO spider_log_', @yesterday, ' SELECT * FROM spider_log');
    PREPARE stmt FROM @insert_data_query;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 清空spider_log表
    TRUNCATE TABLE spider_log;
    
END
;;
delimiter ;

-- ----------------------------
-- Event structure for event_spider_log
-- ----------------------------
DROP EVENT IF EXISTS `event_spider_log`;
delimiter ;;
CREATE EVENT `event_spider_log`
ON SCHEDULE
EVERY '1' DAY STARTS '2025-01-14 00:01:00'
DO BEGIN
    -- 获取昨天日期
    SET @yesterday = DATE_SUB(CURDATE(), INTERVAL 1 DAY);
    
    -- 创建新的表以昨天日期命名
    SET @create_table_query = CONCAT('CREATE TABLE IF NOT EXISTS spider_log_', @yesterday, ' LIKE spider_log');
    PREPARE stmt FROM @create_table_query;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 将spider_log表的数据复制到新表
    SET @insert_data_query = CONCAT('INSERT INTO spider_log_', @yesterday, ' SELECT * FROM spider_log');
    PREPARE stmt FROM @insert_data_query;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 清空spider_log表
    TRUNCATE TABLE spider_log;
    
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
