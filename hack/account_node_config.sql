/*
 Navicat MySQL Dump SQL

 Source Server         : gtcms-test
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : gtcms-test.cluster-c9k28m0w8151.ap-east-1.rds.amazonaws.com:3306
 Source Schema         : gtcms

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 22/07/2025 18:57:23
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for account_node_config
-- ----------------------------
DROP TABLE IF EXISTS `account_node_config`;
CREATE TABLE `account_node_config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点名称',
  `level` tinyint NOT NULL COMMENT '层级',
  `parent_id` int unsigned NOT NULL COMMENT '父id 第一层级为0',
  `api_node` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应的api接口名称',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
  `update_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4074 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台管理节点配置表';

-- ----------------------------
-- Records of account_node_config
-- ----------------------------
BEGIN;
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1, '登录页', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (2, '后台首页', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (3, '站群管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (4, '内容管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (5, '体育数据', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (6, 'SEO管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (7, '模板管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (8, '蜘蛛管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (9, '系统管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (10, '系统设置', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (11, '采集管理', 1, 0, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (301, '站点管理', 2, 3, 'site', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (302, '分组管理', 2, 3, 'siteGroup', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (303, '域名管理', 2, 3, 'domain', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (401, '栏目管理', 2, 4, 'column', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (402, '新闻资讯', 2, 4, 'news', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (403, '标签管理', 2, 4, 'label', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (404, '广告管理', 2, 4, 'ad', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (405, '单页管理', 2, 4, 'singlePage', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (406, '素材中心', 2, 4, 'attachment', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (501, '赛事直播', 2, 5, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (502, '赛事录播', 2, 5, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (503, '赛事管理', 2, 5, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (504, '球队管理', 2, 5, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (505, '球员管理', 2, 5, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (601, '友链管理', 2, 6, 'friendlyLink', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (602, '关键字管理', 2, 6, 'keyword', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (603, 'TKD模板库', 2, 6, 'tdkTmpl', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (604, 'URL配置', 2, 6, 'urlSetting', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (605, 'Sitemap配置', 2, 6, 'sitemap', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (701, '模板管理', 2, 7, 'template', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (702, '模板调用', 2, 7, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (703, '开发文档', 2, 7, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (901, '账户管理', 2, 9, 'accountMgr', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (902, '角色管理', 2, 9, 'roleMgr', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (903, '权限管理', 2, 9, 'permissionMgr', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (904, '操作日志', 2, 9, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (905, '登录日志', 2, 9, '', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (906, '修改密码', 2, 9, 'loginMgr', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1001, '后台设置', 2, 10, 'cmsLoginConfig', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1002, '站群风控', 2, 10, 'riskControlTab', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1003, '存储配置', 2, 10, 'storeConfig', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1004, '第三方配置', 2, 10, 'articleTransformationConfig', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1005, '敏感词库', 2, 10, 'sensitive', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1006, '数据配置', 2, 10, 'selectorConfig', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1011, '后台设置', 2, 10, 'cmsLoginRisk', 0, 'admin', 0, 'admin', 0, 0);
INSERT INTO `account_node_config` (`id`, `name`, `level`, `parent_id`, `api_node`, `create_time`, `create_account`, `update_time`, `update_account`, `delete_time`, `creater`) VALUES (1012, '站群风控', 2, 10, 'riskControlContent', 0, 'admin', 0, 'admin', 0, 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
