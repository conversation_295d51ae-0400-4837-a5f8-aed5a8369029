#!/bin/bash

#################################### 更新亚马逊 ##################################


REMOTE_USER="ec2-user"
REMOTE_HOST="************"
REMOTE_PORT=2542
REMOTE_PATH="/home/<USER>/halalplus/html/admin/admin"
REMOTE_PATH_UI="/home/<USER>/halalplus/html/admin/admin"
PRIVATE_KEY="/home/<USER>/.ssh/id_rsa"
#程序
LOCAL_FILE="/home/<USER>/下载/code/halal-admin/linux_amd64/halal-admin"
#前端包
LOCAL_FILE_UI="/home/<USER>/下载/dist5.zip"


# 编译二进制文件
gf build -o linux_amd64/halal-admin main.go
#gf build
# 更新halal-admin

ssh -i "$PRIVATE_KEY" "$REMOTE_USER@$REMOTE_HOST" -p "$REMOTE_PORT" <<EOF
cd $REMOTE_PATH
PID=\$(pgrep -f "halal-admin")
sudo kill -9 \$PID
cp -ap halal-admin halal-admin.bak
EOF

sudo scp -i "$PRIVATE_KEY" -P "$REMOTE_PORT" "$LOCAL_FILE" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH}"
#sudo scp -i "$PRIVATE_KEY" -P "$REMOTE_PORT" "$LOCAL_FILE_UI" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH_UI}"

# 更新halal-admin
ssh -i "$PRIVATE_KEY" "$REMOTE_USER@$REMOTE_HOST" -p "$REMOTE_PORT" <<EOF
cd $REMOTE_PATH
nohup ./halal-admin &
EOF

#sudo scp -i "$PRIVATE_KEY" -P "$REMOTE_PORT" "$LOCAL_FILE_UI" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_PATH_UI}"

# 更新halal-admin-h5
#ssh -i "$private_key" "$user@$server" -p "$REMOTE_PORT" <<EOF
#cd $REMOTE_PATH_UI
#unzip -q -o dist.zip
#EOF


# 判断执行是否成功
if [ $? -eq 0 ]; then
    echo "远程脚本执行成功"
else
    echo "远程脚本执行失败"
fi
