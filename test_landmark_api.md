# 地标管理API测试文档

## 地标类型管理接口

### 1. 新增地标类型
```bash
POST /landmarkType/add
Content-Type: application/json
Authorization: Bearer {token}

{
  "icon_url": "https://example.com/icon.png",
  "content": [
    {
      "language_type": 0,
      "type_name": "清真寺"
    },
    {
      "language_type": 1,
      "type_name": "Mosque"
    },
    {
      "language_type": 2,
      "type_name": "Masjid"
    }
  ]
}
```

### 2. 地标类型列表
```bash
POST /landmarkType/list
Content-Type: application/json
Authorization: Bearer {token}

{
  "current": 1,
  "page_size": 10
}
```

### 3. 编辑地标类型
```bash
POST /landmarkType/edit
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1,
  "icon_url": "https://example.com/new-icon.png",
  "content": [
    {
      "language_type": 0,
      "type_name": "清真寺（更新）"
    },
    {
      "language_type": 1,
      "type_name": "Mosque (Updated)"
    },
    {
      "language_type": 2,
      "type_name": "Masjid (Updated)"
    }
  ]
}
```

### 4. 获取单个地标类型
```bash
POST /landmarkType/one
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1
}
```

### 5. 删除地标类型
```bash
POST /landmarkType/delete
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1
}
```

### 6. 地标类型选项
```bash
POST /landmarkType/options
Content-Type: application/json
Authorization: Bearer {token}

{}
```

## 地标管理接口

### 1. 新增地标
```bash
POST /landmark/add
Content-Type: application/json
Authorization: Bearer {token}

{
  "type_id": 1,
  "latitude": "21.4225",
  "longitude": "39.8262",
  "image_url": "https://example.com/landmark.jpg",
  "sort_order": 1,
  "content": [
    {
      "language_type": 0,
      "landmark_name": "麦加大清真寺",
      "short_description": "伊斯兰教最神圣的清真寺",
      "address": "沙特阿拉伯麦加",
      "information_text": "<p>麦加大清真寺的详细介绍...</p>"
    },
    {
      "language_type": 1,
      "landmark_name": "Masjid al-Haram",
      "short_description": "The holiest mosque in Islam",
      "address": "Mecca, Saudi Arabia",
      "information_text": "<p>Detailed information about Masjid al-Haram...</p>"
    },
    {
      "language_type": 2,
      "landmark_name": "Masjidil Haram",
      "short_description": "Masjid paling suci dalam Islam",
      "address": "Mekah, Arab Saudi",
      "information_text": "<p>Informasi detail tentang Masjidil Haram...</p>"
    }
  ]
}
```

### 2. 地标列表
```bash
POST /landmark/list
Content-Type: application/json
Authorization: Bearer {token}

{
  "type_id": 1,
  "current": 1,
  "page_size": 10
}
```

### 3. 编辑地标
```bash
POST /landmark/edit
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1,
  "type_id": 1,
  "latitude": "21.4225",
  "longitude": "39.8262",
  "image_url": "https://example.com/new-landmark.jpg",
  "sort_order": 1,
  "content": [
    {
      "language_type": 0,
      "landmark_name": "麦加大清真寺（更新）",
      "short_description": "伊斯兰教最神圣的清真寺（更新）",
      "address": "沙特阿拉伯麦加（更新）",
      "information_text": "<p>麦加大清真寺的详细介绍（更新）...</p>"
    }
  ]
}
```

### 4. 获取单个地标
```bash
POST /landmark/one
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1
}
```

### 5. 删除地标
```bash
POST /landmark/delete
Content-Type: application/json
Authorization: Bearer {token}

{
  "id": 1
}
```

## 测试场景

### 功能测试
1. **地标类型CRUD**：创建、查询、更新、删除地标类型
2. **地标CRUD**：创建、查询、更新、删除地标
3. **多语言支持**：验证中文、英文、印尼语的正确显示
4. **关联检查**：验证删除有关联地标的类型时的错误提示
5. **数据验证**：验证必填字段、字段长度限制等

### 边界测试
1. **坐标格式验证**：测试无效的经纬度格式
2. **类型名称重复**：测试相同语言下的重复类型名称
3. **不存在的ID**：测试查询、编辑、删除不存在的记录
4. **分页边界**：测试分页参数的边界值

### 多语言测试
1. **语言切换**：通过Accept-Language头测试不同语言的显示
2. **语言支持标识**：验证LanguageArr字段的正确性
3. **缺失语言**：测试某种语言缺失时的处理

## 预期结果

所有接口应该：
1. 返回正确的HTTP状态码
2. 返回符合API规范的JSON格式
3. 正确处理多语言内容
4. 正确验证输入参数
5. 正确处理业务逻辑（如关联检查）
