package consts

const (
	ModLiveFootball   = 90001 // 足球
	ModLiveBasketball = 90002 // 篮球

	ModLiveNBA              = "b1"   // NBA直播
	ModLiveCBA              = "b3"   // CBA直播
	ModLiveENGPremierLeague = "f82"  // 英超
	ModLiveITASerieA        = "f108" // 意甲
	ModLiveSPALaLiga        = "f120" // 西甲
	ModLiveGERBundesliga    = "f129" // 德甲
	ModLiveFRALigue1        = "f142" // 法甲
	ModLiveCHNSuperLeague   = "f542" // 中超
)

const (
	SportIdFootball   = 1 // 足球
	SportIdBasketball = 2 // 篮球
)

const (
	ModMatchLive         = 90004 // 赛事直播
	ModMatchRecord       = 90005 // 赛事录播
	ModLabel             = 90006 // 标签
	ModTeam              = 90007 // 球队
	ModPlayer            = 90008 // 球员
	ModMatchFbRecord     = 90009 // 足球录像
	ModMatchBbRecord     = 90010 // 篮球录像
	ModFbVideoCollection = 90011 // 足球集锦
	ModBbVideoCollection = 90012 // 篮球集锦
	ModStandings         = 90013 // 积分榜
	ModScorer            = 90014 // 射手榜
	ModTopic             = 90015 // 专题
	ModDatabase          = 90016 // 资料库
	ModChannel           = 90017 // 电视频道

	ModRecordNBA                 = 900051  // NBA录播
	ModRecordCBA                 = 900052  // CBA录播
	ModRecordCHNSuperLeague      = 900053  // 中超录播
	ModRecordENGPremierLeague    = 900054  // 英超录播
	ModRecordITASerieA           = 900055  // 意甲录播
	ModRecordSPALaLiga           = 900056  // 西甲录播
	ModRecordGERBundesliga       = 900057  // 德甲录播
	ModRecordFRALigue1           = 900058  // 法甲录播
	ModRecordUEFAEURO            = 900059  // 欧洲杯录播
	ModRecordUEFAUCL             = 9000510 // 欧冠杯录播
	ModRecordCONMEBOLCopaAmerica = 9000511 // 美洲杯录播
	ModRecordJPNJ1               = 9000512 // 日职联录播
	ModRecordKORK1               = 9000513 // 韩K联录播
	ModRecordAUSALeague          = 9000514 // 澳超录播

	ModNewsNBA                 = 900031  // NBA新闻
	ModNewsCBA                 = 900032  // CBA新闻
	ModNewsCHNSuperLeague      = 900033  // 中超新闻
	ModNewsENGPremierLeague    = 900034  // 英超新闻
	ModNewsITASerieA           = 900035  // 意甲新闻
	ModNewsSPALaLiga           = 900036  // 西甲新闻
	ModNewsGERBundesliga       = 900037  // 德甲新闻
	ModNewsFRALigue1           = 900038  // 法甲新闻
	ModNewsUEFAEURO            = 900039  // 欧洲杯新闻
	ModNewsUEFAUCL             = 9000310 // 欧冠杯新闻
	ModNewsCONMEBOLCopaAmerica = 9000311 // 美洲杯新闻
	ModNewsJPNJ1               = 9000312 // 日职联新闻
	ModNewsKORK1               = 9000313 // 韩K联新闻
	ModNewsAUSALeague          = 9000314 // 澳超新闻

	ModFbVcENGPremierLeague = 900111 // 英超集锦
	ModFbVcITASerieA        = 900112 // 意甲集锦
	ModFbVcSPALaLiga        = 900113 // 西甲集锦
	ModFbVcGERBundesliga    = 900114 // 德甲集锦
	ModFbVcFRALigue1        = 900115 // 法甲集锦

	ModStandingsENGPremierLeague = 900131 // 英超积分榜
	ModStandingsITASerieA        = 900132 // 意甲积分榜
	ModStandingsSPALaLiga        = 900133 // 西甲积分榜
	ModStandingsGERBundesliga    = 900134 // 德甲积分榜
	ModStandingsFRALigue1        = 900135 // 法甲积分榜

	ModScorerENGPremierLeague = 900141 // 英超射手榜
	ModScorerITASerieA        = 900142 // 意甲射手榜
	ModScorerSPALaLiga        = 900143 // 西甲射手榜
	ModScorerGERBundesliga    = 900144 // 德甲射手榜
	ModScorerFRALigue1        = 900145 // 法甲射手榜

	ModLiveJPNJ1League         = "f567" // 日职联
	ModLiveKORK1League         = "f581" // 韩K联
	ModLiveUEFAEURO            = "f45"  // 欧洲杯
	ModLiveUEFAUCL             = "f46"  // 欧冠杯
	ModLiveAFCAFCAsianCup      = "f490" // 亚洲杯
	ModLiveAFCAFCACL           = "f491" // 亚冠杯
	ModLiveAFCAFCQualification = "f3"   // 世亚预
)

var ModNews = []string{"0", ModLiveNBA, ModLiveCBA, ModLiveENGPremierLeague, ModLiveITASerieA,
	ModLiveSPALaLiga, ModLiveGERBundesliga, ModLiveFRALigue1, ModLiveCHNSuperLeague}

var Mod2SportId = map[int]int{
	ModLiveFootball:      SportIdFootball,
	ModLiveBasketball:    SportIdBasketball,
	ModMatchRecord:       SportIdFootball,
	ModMatchFbRecord:     SportIdFootball,
	ModMatchBbRecord:     SportIdBasketball,
	ModFbVideoCollection: SportIdFootball,
	ModBbVideoCollection: SportIdBasketball,

	ModNewsNBA:                 SportIdBasketball,
	ModNewsCBA:                 SportIdBasketball,
	ModNewsCHNSuperLeague:      SportIdFootball,
	ModNewsENGPremierLeague:    SportIdFootball,
	ModNewsITASerieA:           SportIdFootball,
	ModNewsSPALaLiga:           SportIdFootball,
	ModNewsGERBundesliga:       SportIdFootball,
	ModNewsFRALigue1:           SportIdFootball,
	ModNewsUEFAUCL:             SportIdFootball,
	ModNewsJPNJ1:               SportIdFootball,
	ModNewsKORK1:               SportIdFootball,
	ModNewsAUSALeague:          SportIdFootball,
	ModNewsUEFAEURO:            SportIdFootball,
	ModNewsCONMEBOLCopaAmerica: SportIdFootball,
}
