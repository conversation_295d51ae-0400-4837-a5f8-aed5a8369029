package consts

const (
	FootballPlayerBaseTaskId       = 1
	FootballTeamBaseTaskId         = 2
	FootballCountryTaskId          = 3
	FootballCompetitionBaseTaskId  = 4
	FootballHonorTaskId            = 5
	FootballPlayerHonorTaskId      = 6
	FootballTeamHonorTaskId        = 7
	FootballTransferTaskId         = 8
	FootballVenueTaskId            = 9
	FootballSeasonTaskId           = 10
	FootballCompetitionStatsTaskId = 11
	FootballMatchTaskId            = 23
	FootballSquadTaskId            = 25
)

const (
	BasketballCompetitionBaseTaskId  = 12
	BasketballHonorTaskId            = 13
	BasketballPlayerBaseTaskId       = 14
	BasketballPlayerHonorTaskId      = 15
	BasketballSeasonTaskId           = 16
	BasketballTeamBaseTaskId         = 17
	BasketballTeamHonorTaskId        = 18
	BasketballVenueTaskId            = 19
	BasketballCompetitionStatsTaskId = 20
	BasketballMatchTaskId            = 24
	BasketballSquadTaskId            = 26
)

const (
	VideoRecordTaskId = 21
	VideoLiveTaskId   = 22
)

const (
	SinaNewsNBA       = 27
	SinaNewsCBA       = 28
	SinaNewsYingChao  = 29
	SinaNewsXiJia     = 30
	SinaNewsYiJia     = 31
	SinaNewsZhongChao = 32
	SinaNewsDeJia     = 33
	SinaNewsFaJia     = 34
)

const (
	SEOFootballCompetitionId = iota + 1
	SEOFootballTeamId
	SEOFootballPlayerId
	SEOBasketballCompetitionId
	SEOBasketballTeamId
	SEOBasketballPlayerId
	SEOFootballMatchId
	SEOBasketballMatchId
	SEOSnookerCompetitionId
	SEOSnookerTeamId
	SEOSnookerPlayerId
	SEOSnookerMatchId
	//本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ...
)

const (
	MultiLanguageType = iota + 1 // 1多语言类型 2多语言国家 3多语言赛事 4多语言球队 5多语言球员
	MultiLanguageTypeCountry
	MultiLanguageTypeCompetition
	MultiLanguageTypeTeam
	MultiLanguageTypePlayer
)

// 足球比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+
const (
	FootballMatchStatus0 = iota
	FootballMatchStatus1
	FootballMatchStatus2
	FootballMatchStatus3
	FootballMatchStatus4
	FootballMatchStatus5
	FootballMatchStatus6
	FootballMatchStatus7
	FootballMatchStatus8
)
