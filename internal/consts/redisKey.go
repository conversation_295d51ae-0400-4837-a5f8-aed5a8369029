package consts

import (
	"github.com/gogf/gf/v2/util/gconv"
)

func KeyAuthToken[T comparable](id T) string {
	return "admin.token." + gconv.String(id)
}

func KeyAccountInfo[T comparable](id T) string {
	return "admin." + gconv.String(id)
}

const KeyHttpContext string = "ctx.account"
const KeyContextRole string = "ctx.role"

const (
	InitModule    = "init_module"
	ModuleList    = "module_list"
	ModuleOptions = "module_options"

	FootballCompetitionList   = "football_competition_list"
	BasketballCompetitionList = "basketball_competition_list"

	AdSiteList  = "ad_site_list:"
	AdGroupList = "ad_group_list:"
	AdUpdate    = "ad_update"

	ColumnOneByModuleIdWithLanguage = "column_one_by_module_id_with_language:"
	ColumnSiteGroupListByLanguage   = "column_site_group_list_by_language:"
	ColumnListByLanguage            = "column_list_by_language:"
	ColumnOneById                   = "column_one_by_id:"
	ColumnUpdate                    = "column_update"

	FriendlyLinkSiteList   = "friendly_link_site_list:"
	FriendlyLinkGroupList  = "friendly_link_group_list:"
	FriendlyLinkRuleUpdate = "friendly_link_rule_update"

	TdkTmplSiteList  = "tdk_tmpl_site_list:"
	TdkTmplGroupList = "tdk_tmpl_group_list:"

	UrlSettingSiteList  = "url_setting_site_list:"
	UrlSettingGroupList = "url_setting_group_list:"

	CollectSeoInfo       = "collect_seo_info:"
	CollectExtraInfoList = "collect_extra_info_list:"

	SiteByDomainName = "site_by_domain_name:"
	DomainOneByName  = "domain_one_by_name:"
	DomainUpdate     = "domain_update:"

	TemplateOne          = "template_one:"
	TemplateOneDefault   = "template_one_default"
	TemplateUpdate       = "template_update"
	SpiderList           = "spider_list"
	SpiderBlackIpList    = "spider_black_ip_list"
	SpiderFirewallList   = "spider_firewall_list"
	SpiderFirewallUpdate = "spider_firewall_update"

	LabelSiteList  = "label_site_list:"
	LabelGroupList = "label_group_list:"
	LabelList      = "label_list"
	LabelOne       = "label_one:"

	SinglePageList           = "single_page_list:"
	NewsListPage             = "news_list_page:"
	NewsListPageCount        = "news_list_page_count:"
	NewsListByMod            = "news_list_by_mod_page:"
	NewsListByModCount       = "news_list_by_mod_page_count:"
	NewsUpdate               = "news_update"
	NewsUpdateNotAuto        = "news_update_not_auto"
	SiteGroupOne             = "site_group_one:"
	SiteGroupUpdate          = "site_group_update"
	SiteUpdate               = "site_update"
	SiteChildUpdate          = "site_child_update"
	SelectorConfigUpdate     = "selector_config_update"
	VideoProductUpdate       = "video_product_update"
	SelectorConfigListByType = "selector_config_list_by_type:"
	TopicUpdate              = "topic_update"
	ChannelUpdate            = "channel_update"
	TopicListPage            = "topic_list_page:"
	ChannelListPage          = "channel_list_page:"

	TdkTmplOne       = "tdk_tmpl_one:"
	UrlSettingOne    = "url_setting_one:"
	UrlSettingUpdate = "url_setting_update"
	TdkTmplUpdate    = "tdk_tmpl_update"
	SitemapOneById   = "sitemap_one_by_id:"
	SitemapUpdate    = "sitemap_update"
	RobotsUpdate     = "robots_update"

	RiskRiskTabList     = "RiskTabList"
	RiskRiskContentList = "RiskContentList"
	RiskUpdate          = "RiskUpdate"

	LocalImageOne             = "local_image_one:"
	StoreConfig               = "store_config"
	StoreCloudConfigByType    = "store_cloud_config_by_type:"
	SitemapBySiteId           = "sitemap_by_site_id:"
	SitemapBySiteIdWithMaxIdx = "sitemap_by_site_id_with_max_idx:"
	StoreUpdate               = "store_update"

	MultiLanguageOne   = "multi_language_one:"
	FootballTeamUpdate = "football_team_update:"

	BasketballPlayer                  = "basket_ball_player:"
	BasketballTeam                    = "basket_ball_team:"
	BasketballTeamListByCompetitionId = "basket_ball_team_list_by_comp_id:"
	BasketballCompetition             = "basket_ball_competition:"
	BasketballCompetitionStats        = "basket_ball_competition_stats:"
	BasketballMatchListHotLogo        = "basket_ball_match_list_hot_logo"
	BasketballMatchListByRanking      = "basket_ball_match_list_ranking"
	BasketballMatchListHot            = "basket_ball_match_list_hot"
	BasketballMatchListNonHot         = "basket_ball_match_list_non_hot"
	BasketballMatchOne                = "basket_ball_match_One:"
	BasketballMatchOneWitchRecord     = "basket_ball_match_one_witch_record:"
	BasketballHistoryListFromTeam     = "basket_ball_his_list_from_team:"
	BasketballHistoryListBetTeam      = "basket_ball_his_list_bet_team:"

	FootballPlayer                  = "foot_ball_player:"
	FootballTeam                    = "foot_ball_team:"
	FootballTeamListByCompetitionId = "foot_ball_team_list_by_comp_id:"
	FootballCompetition             = "foot_ball_competition:"
	FootballCompetitionStats        = "foot_ball_competition_stats:"
	FootballMatchListHotLogo        = "foot_ball_match_list_hot_logo"
	FootballMatchListByRanking      = "foot_ball_match_list_ranking"
	FootballMatchListHot            = "foot_ball_match_list_hot"
	FootballMatchListNonHot         = "foot_ball_match_list_non_hot"
	FootballMatchOne                = "foot_ball_match_one:"
	FootballMatchOneWitchRecord     = "foot_ball_match_one_witch_record:"
	FootballHistoryListFromTeam     = "foot_ball_his_list_from_team:"
	FootballHistoryListBetTeam      = "foot_ball_his_list_bet_team:"

	MatchLiveListBySportIdComId       = "match_live_list:"
	MatchRecordListBySportIdComIdPage = "match_record_list:"
	MatchRecordOnePre                 = "match_record_one_pre:"
	MatchRecordOneNext                = "match_record_one_next:"

	FootballMatchUpdate   = "foot_ball_match_update"
	SnookerMatchUpdate    = "snooker_match_update"
	BasketballMatchUpdate = "basket_ball_match_update"
	ExtraInfoUpdate       = "extra_info_update"
)
