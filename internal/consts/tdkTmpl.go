package consts

const (
	// 1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:球队 9:球员 10:集锦栏目 12:集锦详情页 13:积分榜 14:射手榜 15:专题 16:资料库 17:资料库详情 18:电视频道 19:电视频道详情
	TdkCategoryMatchColumn = iota + 1
	TdkCategoryRecordColumn
	TdkCategoryNewsColumns
	TdkCategoryLabel
	TdkCategoryMatchDetail
	TdkCategoryRecordDetail
	TdkCategoryNewsDetail
	TdkCategoryTeam
	TdkCategoryPlayer
	TdkVideoCollection
	TdkVideoCollectionDetail = iota + 2
	TdkStanding
	TdkScorer
	TdkTopic
	TdkDatabase
	TdkDatabaseDetail
	TdkChannel
	TdkChannelDetail
	TdkMatchResult
	TdkMatchResultDetail
)

var Tdk2url = map[int]int{
	TdkCategoryMatchColumn:  UrlCategoryMatchColumn,
	TdkCategoryRecordColumn: UrlCategoryRecordColumn,
	TdkCategoryNewsColumns:  UrlCategoryNewsColumns,
	TdkCategoryLabel:        UrlCategoryLabel,
	TdkCategoryTeam:         UrlCategoryTeam,
	TdkCategoryPlayer:       UrlCategoryPlayer,
	TdkVideoCollection:      UrlCategoryVideoCollection,
	TdkStanding:             UrlCategoryStandings,
	TdkScorer:               UrlCategoryScorer,
	TdkTopic:                UrlCategoryTopics,
	TdkDatabase:             UrlCategoryDatabase,
	TdkChannel:              UrlCategoryChannel,
	TdkMatchResult:          UrlCategoryMatchResult,
}

var Url2tdk = map[int]int{
	UrlCategoryMatchColumn:     TdkCategoryMatchColumn,
	UrlCategoryRecordColumn:    TdkCategoryRecordColumn,
	UrlCategoryNewsColumns:     TdkCategoryNewsColumns,
	UrlCategoryLabel:           TdkCategoryLabel,
	UrlCategoryTeam:            TdkCategoryTeam,
	UrlCategoryPlayer:          TdkCategoryPlayer,
	UrlCategoryVideoCollection: TdkVideoCollection,
	UrlCategoryStandings:       TdkStanding,
	UrlCategoryScorer:          TdkScorer,
	UrlCategoryTopics:          TdkTopic,
	UrlCategoryDatabase:        TdkDatabase,
	UrlCategoryChannel:         TdkChannel,
	UrlCategoryMatchResult:     TdkMatchResult,
}
