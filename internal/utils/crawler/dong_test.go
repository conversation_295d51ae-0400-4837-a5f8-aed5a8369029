package crawler

import (
	"github.com/gogf/gf/v2/os/gctx"
	"strings"
	"testing"
)

var ctx = gctx.New()

func TestDongApi(t *testing.T) {
	// 一个样例
	url := ""
	for i := 0; i < 3; i++ {
		res, err := DongQiuDiApi(ctx, 3, url)
		url = res.Next
		t.Log(err)
		if err != nil {
			return
		}
		for _, e := range res.Articles {
			status, article, err := DongQiuDiArticle(e.Id)
			if err != nil {
				t.Log(err)
				continue
			}
			t.Log("article:", status, strings.TrimSpace(article))
		}
	}
}
