package crawler

import (
	"bytes"
	"context"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"regexp"
	"strings"
)

const espnUrlTpl = "https://global.espn.com/football/league/_/name/%s"

func EspnListApi(ctx context.Context, competitionName string) (res *SkysportResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	u := fmt.Sprintf(espnUrlTpl, competitionName)
	_, b, err := Get(u)
	if err != nil {
		return
	}
	b = matcherComment.ReplaceAll(b, []byte{}) // 清除单行注释
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(b))
	if err != nil {
		return
	}
	title := make([]string, 0)
	intro := make([]string, 0)
	url := make([]string, 0)
	doc.Find(".news-feed-item").Each(func(i int, selection *goquery.Selection) {
		titleAnchor := selection.Find("h1")
		if titleAnchor.Length() > 0 {
			title = append(title, strings.TrimSpace(titleAnchor.Find("a").Text()))
		} else {
			title = append(title, "")
		}

		introAnchor := selection.Find("p")
		if introAnchor.Length() > 0 {
			intro = append(intro, strings.TrimSpace(introAnchor.Text()))
		} else {
			intro = append(intro, "")
		}

		urlAnchor := selection.Find("a")
		if urlAnchor.Length() > 0 {
			url = append(url, urlAnchor.First().AttrOr("data-popup-href", ""))
		} else {
			url = append(url, "")
		}
	})
	rt := new(SkysportResp)
	for i := 0; i < len(title); i++ {
		each := new(SkyData)
		each = &SkyData{
			Title: title[i],
			Url:   url[i],
			Intro: intro[i],
		}
		rt.Result = append(rt.Result, *each)
	}
	if len(rt.Result) == 0 {
		return nil, fmt.Errorf("no data")
	}
	return rt, nil
}

func EspnInfoApi(ctx context.Context, url string) (out string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	_, b, err := Get(url)
	if err != nil {
		return
	}
	b = matcherComment.ReplaceAll(b, []byte{}) // 清除单行注释
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(b))
	if err != nil {
		return
	}

	arti := doc.Find(".article-body")
	arti.Find(".article-meta").Remove()
	arti.Find(".content-reactions.reactions-allowed").Remove()
	arti.Find(".inline.editorial.float-r").Remove()
	arti.Find("iframe").Remove()
	arti.Find(".ad-300").Remove()

	out, err = arti.Html()
	if err != nil {
		return
	}

	// 定义正则表达式，匹配 <a> 标签并提取标签内部的文本
	re := regexp.MustCompile(`<a[^>]*>(.*?)</a>`)
	// 替换所有的 <a> 标签，只保留标签中的文本内容
	out = re.ReplaceAllString(out, "$1")
	return out, nil
}

type EspnResp struct {
	Result []SkyData `json:"result"`
}

type EspnData struct {
	Intro string `json:"intro"`
	Title string `json:"title"`
	Url   string `json:"url"` // str of ts second
}
