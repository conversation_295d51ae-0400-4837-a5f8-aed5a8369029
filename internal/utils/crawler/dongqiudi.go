package crawler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

// DongQiuDiAPI: 示例：https://www.dongqiudi.com/api/app/tabs/web/5.json?after=1722388787&page=2&child_tab_id=0
// 使用5.json的5 用来区分栏目

func DongQiuDiApi(ctx context.Context, id int, url string) (res *DongQiuDiResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()

	u := url
	if url == "" {
		u = fmt.Sprintf(dongQiuDiTpl, id)
	}
	_, body, err := Get(u)
	res = new(DongQiuDiResp)
	err = json.Unmarshal(body, res)
	if err != nil {
		return nil, err
	}
	return
}

// id
const dongQiuDiTpl = "https://www.dongqiudi.com/api/app/tabs/web/%d.json"
const dongQiuDiDetailTpl = "https://api.dongqiudi.com/v2/article/detail/%d"

type DongQiuDiResp struct {
	Next     string              `json:"next"` // 下一页数据
	Articles []DongQiuDiRespData `json:"articles"`
}

type DongQiuDiRespData struct {
	Id         int    `json:"id"`
	Scheme     string `json:"scheme"`
	Url        string `json:"url"`
	Title      string `json:"title"`
	ShareTitle string `json:"share_title"`
	Thumb      string `json:"thumb"`     // 封面
	ShowTime   int    `json:"show_time"` // 发布时间
}

type DongQiuDiArticleResp struct {
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    DongQiuDiArticleData `json:"data"`
}

type DongQiuDiArticleData struct {
	Body string `json:"body"`
}

// 示例：https://api.dongqiudi.com/v2/article/detail/4437051
// 取： id: artibody, 整个返回
func DongQiuDiArticle(id int) (status int, body string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	u := fmt.Sprintf(dongQiuDiDetailTpl, id)
	status, b, err := Get(u)
	if err != nil {
		return
	}
	var res DongQiuDiArticleResp
	err = json.Unmarshal(b, &res)
	if err != nil {
		return 0, "", err
	}
	if res.Code != 0 {
		return 0, "", fmt.Errorf("code:%d, message:%s", res.Code, res.Message)
	}
	body = res.Data.Body
	body = strings.ReplaceAll(body, "<img data-src", "<img src")
	return
}
