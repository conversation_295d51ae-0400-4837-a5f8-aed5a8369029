package crawler

import (
	"github.com/valyala/fasthttp"
	"math/rand"
	"time"
)

var client *fasthttp.Client

func init() {
	client = &fasthttp.Client{
		Name:                     "crawler",
		NoDefaultUserAgentHeader: true,
		MaxConnsPerHost:          1000,
		ReadTimeout:              30 * time.Second,
		WriteTimeout:             30 * time.Second,
	}

}

func randUA() string {
	return ualist[rand.Intn(len(ualist))]
}

func Get(url string) (status int, body []byte, err error) {
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)
	req.Header.Set("User-Agent", randUA())
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2")
	req.Header.Set("DNT", "1")
	req.SetRequestURI(url)
	res := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(res)
	err = client.Do(req, res)
	if err != nil {
		return 0, nil, err
	}

	status = res.StatusCode()
	body = res.Body()
	return status, body, nil
}
