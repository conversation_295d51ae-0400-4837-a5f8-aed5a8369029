package crawler

import (
	"strings"
	"testing"
	"time"
)

func TestSina<PERSON>pi(t *testing.T) {
	// 一个样例
	res, err := SinaApi(ctx, [2]int{45, 489}, 10, 1)
	t.Log(err)
	if err != nil {
		return
	}
	if res.Result.Status.Code != 0 {
		t.Log(res.Result.Status.Msg)
		return
	}
	for _, e := range res.Result.Data {
		if e.MediaName == "新浪彩票" { // 一定要过滤掉
			continue
		}
		status, article, err := SinaArticle(e.Url)
		if err != nil {
			t.Log(err)
			continue
		}
		t.Log("article:", status, strings.TrimSpace(string(article)))
		break                   // test
		time.Sleep(time.Second) // 保留适当的间隔
	}
}
