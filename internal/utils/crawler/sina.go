package crawler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"regexp"
	"strings"
	"time"
)

// SinaAPI: 示例：http://feed.mix.sina.com.cn/api/roll/get?pageid=45&lid=489&num=30&versionNumber=1.2.4&page=1&encode=utf-8&callback=feedCardJsonpCallback&_=1715593455646
// 使用pageid+lid 用来区分栏目

func SinaApi(ctx context.Context, param [2]int, num, page int) (res *SinaResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	u := fmt.Sprintf(sinaTpl, param[0], param[1], num, page, time.Now().UnixMilli())
	_, body, err := Get(u)
	//g.Log().Info(ctx, "SinaAPI:", status, u)
	//fmt.Println(status, err)
	//fmt.Println(string(body))
	bs := string(body)
	istart := strings.Index(bs, prefix)
	iend := strings.Index(bs, suffix)
	if istart == -1 || iend == -1 {
		return nil, fmt.Errorf("SinaAPI: %s", bs)
	}
	body = body[istart+len(prefix) : iend]
	res = new(SinaResp)
	err = json.Unmarshal(body, res)
	if err != nil {
		return nil, err
	}
	return
}

// pageid, lid, num, page, ts_ms
const sinaTpl = "http://feed.mix.sina.com.cn/api/roll/get?pageid=%d&lid=%d&num=%d&versionNumber=1.2.4&page=%d&encode=utf-8&callback=feedCardJsonpCallback&_=%d"
const prefix = "try{feedCardJsonpCallback("
const suffix = ");}catch"

type SinaResp struct {
	Result SinaResult
}

type SinaResult struct {
	Status SinaStatus     `json:"status"`
	Total  int            `json:"total"`
	Start  int            `json:"start"`
	End    int            `json:"end"`
	Data   []SinaRespData `json:"data"`
}

type SinaRespData struct {
	Docid      string `json:"docid"`
	Intime     string `json:"intime"` // str of ts second
	Channelid  string `json:"channelid"`
	Categoryid string `json:"categoryid"`
	Url        string `json:"url"`
	Title      string `json:"title"`
	Intro      string `json:"intro"`
	//Keywords   string        `json:"keywords"`
	MediaName string        `json:"media_name"` // eg:"新浪彩票"
	Images    []SinaRespImg `json:"images"`
	Ctime     string        `json:"ctime"`
}
type SinaStatus struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type SinaRespImg struct {
	U string `json:"u"`
	//W string `json:"w"` // w h 实际是int
	//H string `json:"h"`
}

// 示例：https://sports.sina.com.cn//g//pl//2024-05-14//doc-inavczvs1574629.shtml
// 取： id: artibody, 整个返回
func SinaArticle(url string) (status int, body []byte, err error) {
	status, b, err := Get(url)
	if err != nil {
		return
	}
	b = matcherComment.ReplaceAll(b, []byte{}) // 清除单行注释
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(b))
	if err != nil {
		return
	}
	arti := doc.Find("div#artibody")
	arti.Find("#left_hzh_ad").Remove()
	arti.Find(".show_statement").Remove()
	arti.Find("p").Each(func(i int, selection *goquery.Selection) {
		text := strings.TrimSpace(selection.Text())
		if len(text) == 0 {
			selection.Remove()
		}
	})
	// 判断是否是作者信息
	lastP := arti.Find("p:last-child")
	lastPText := strings.TrimSpace(lastP.Text())
	if strings.HasPrefix(lastPText, "（") && strings.HasSuffix(lastPText, "）") {
		lastP.Remove()
	}
	html, err := arti.Html()
	if err != nil {
		return
	}
	return status, []byte(strings.TrimSpace(html)), err

}

const articleTpl = "http://feed.mix.sina.com.cn/api/roll/get?pageid=%d&lid=%d&num=%d&versionNumber=1.2.4&page=%d"

// 获取新浪文章列表
func SinaArticleList(ctx context.Context, param [2]int, num, page int) (res *SinaResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	u := fmt.Sprintf(articleTpl, param[0], param[1], num, page)
	_, body, err := Get(u)

	res = new(SinaResp)
	err = json.Unmarshal(body, res)
	if err != nil {
		return nil, err
	}
	return

}

type SinaCommentResp struct {
	Result SinaCommentResult
}

type SinaCommentResult struct {
	Cmntlist []DataList `json:"cmntlist"`
}

type DataList struct {
	Newsid  string `json:"newsid"`
	Content string `json:"content"`
	Mid     string `json:"mid"`
}

const commentTpl = "https://comment.sina.com.cn/page/info?version=1&format=json&channel=ty&ie=utf-8&oe=utf-8&page=1&page_size=50&newsid="

// 获取新浪评论列表
func SinaCommentList(ctx context.Context, url string) (res *SinaCommentResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	_, body, err := Get(commentTpl + url)

	res = new(SinaCommentResp)
	err = json.Unmarshal(body, res)
	if err != nil {
		return nil, err
	}
	return

}

// 先只做单行。 多行： <!--(.|[\r\n])*?-->
var matcherComment = regexp.MustCompile("<!--.*?-->")
