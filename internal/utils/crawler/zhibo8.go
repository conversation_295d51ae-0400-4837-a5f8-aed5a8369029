package crawler

//
//import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"github.com/gogf/gf/v2/database/gdb"
//	"github.com/gogf/gf/v2/frame/g"
//	"github.com/gogf/gf/v2/os/gtime"
//	"github.com/gogf/gf/v2/util/gconv"
//	"gtcms/internal/dao"
//	"gtcms/internal/model/entity"
//	"gtcms/internal/service"
//	"io"
//	"log"
//	"net/http"
//	"strings"
//)
//
//const zhibo8Tpl = "https://s.qiumibao.com/json/hot/24hours.htm"
//const zhibo8DetailTpl = "https://vodhl.duoduocdn.com/vod-player/1251542705/%s/tcplayer/console/vod-player.html?autoplay=true&width=960&height=540"
//
//type News struct {
//	Title      string `json:"title"`
//	Type       string `json:"type"`
//	CreateTime string `json:"createTime"`
//	FromURL    string `json:"from_url"`
//	FromName   string `json:"from_name"`
//}
//
//type Video struct {
//	FileId     string `json:"fileid"`
//	Title      string `json:"title"`
//	Label      string `json:"label"`
//	CreateTime string `json:"createTime"`
//}
//
//func Zhibo8Api(ctx context.Context) (response *News, err error) {
//	defer func() {
//		if r := recover(); r != nil {
//			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
//		}
//	}()
//
//	body, err := http.Get(zhibo8Tpl)
//	// 读取响应体
//	b, err := io.ReadAll(body.Body)
//	if err != nil {
//		g.Log().Line().Error(ctx, "Error reading response body:", err)
//		return
//	}
//
//	var result map[string]interface{}
//	err = json.Unmarshal(b, &result)
//	if err != nil {
//		log.Fatal(err)
//	}
//	videos := result["video"]
//	if videos == nil {
//		return
//	}
//
//	for _, v := range videos.([]interface{}) {
//		var one *Video
//		err = gconv.Scan(v, &one)
//		if err != nil {
//			return
//		}
//
//		competitionName := strings.Split(one.Label, ",")[0]
//		compId, _ := service.Collect().GetCompetitionIdByName(ctx, competitionName)
//
//		news := &entity.CrawlerNewsRaw{
//			Source:        "zhibo8",
//			Docid:         one.FileId,
//			Title:         one.Title,
//			CompetitionId: compId,
//			CreateTime:    gtime.New(one.CreateTime).UnixMilli(),
//			Content:       fmt.Sprintf(zhibo8DetailTpl, one.FileId),
//		}
//
//		err = dao.CrawlerNewsRaw.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
//			_, err = dao.CrawlerNewsRaw.Ctx(ctx).Insert(news)
//			if err != nil {
//				return err
//			}
//			return nil
//		})
//		if err != nil {
//			g.Log().Line().Error(ctx, "zhibo8 article save fail")
//			continue
//		}
//	}
//	return
//}
