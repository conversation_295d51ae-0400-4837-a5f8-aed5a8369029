package crawler

import (
	"context"
	"testing"
)

func TestSkysportInfoApi(t *testing.T) {
	ctx := context.Background()
	url := "https://www.skysports.com/football/news/11835/13244166/kylian-mbappe-real-madrid-superstar-still-adapting-to-life-at-santiago-bernabeu-after-disastrous-el-clasico-debut"
	_, err := SkysportInfoApi(ctx, url)
	if err != nil {
		t.Errorf("SkysportInfoApi returned an error: %v", err)
	}
}

func TestSkysportInfoApi_1(t *testing.T) {
	ctx := context.Background()
	url := "http://www.skysports.com/football/news/11827/13244166/kylian-mbappe-real-madrid-superstar-still-adapting-to-life-at-santiago-bernabeu-after-disastrous-el-clasico-debut"
	_, err := SkysportInfoApi(ctx, url)
	if err != nil {
		t.Errorf("SkysportInfoApi returned an error: %v", err)
	}
}

func TestSkysportInfoApi_2(t *testing.T) {
	ctx := context.Background()
	url := "https://www.skysports.com/football/news/11835/13244166/kylian-mbappe-real-madrid-superstar-still-adapting-to-life-at-santiago-bernabeu-after-disastrous-el-clasico-debut"
	_, err := SkysportInfoApi(ctx, url)
	if err != nil {
		t.Errorf("SkysportInfoApi returned an error: %v", err)
	}
}
