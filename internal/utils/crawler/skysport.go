package crawler

import (
	"bytes"
	"context"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"strings"
)

const urlTpl = "https://www.skysports.com/football/ajax/digrevMoreNewsByBasketId/%d/20/1"

func SkysportListApi(ctx context.Context, siteId int) (res *SkysportResp, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	u := fmt.Sprintf(urlTpl, siteId)
	_, b, err := Get(u)
	if err != nil {
		return
	}
	b = matcherComment.ReplaceAll(b, []byte{}) // 清除单行注释
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(b))
	if err != nil {
		return
	}
	title := make([]string, 0)
	intro := make([]string, 0)
	url := make([]string, 0)
	doc.Find(".news-list__headline-link").Each(func(i int, selection *goquery.Selection) {
		title = append(title, strings.TrimSpace(selection.Text()))
	})
	doc.Find(".news-list__snippet").Each(func(i int, selection *goquery.Selection) {
		intro = append(intro, strings.TrimSpace(selection.Text()))
	})
	doc.Find(".news-list__headline-link").Each(func(i int, selection *goquery.Selection) {
		href, exists := selection.Attr("href")
		if exists {
			url = append(url, strings.ReplaceAll(strings.TrimSpace(href), "http://", "https://"))
		} else {
			url = append(url, "")
		}
	})
	rt := new(SkysportResp)
	for i := 0; i < len(title); i++ {
		each := new(SkyData)
		each = &SkyData{
			Title: title[i],
			Url:   url[i],
			Intro: intro[i],
		}
		rt.Result = append(rt.Result, *each)
	}
	if len(rt.Result) == 0 {
		return nil, fmt.Errorf("no data")
	}
	return rt, nil
}

func SkysportInfoApi(ctx context.Context, url string) (out string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	_, b, err := Get(url)
	if err != nil {
		return
	}
	b = matcherComment.ReplaceAll(b, []byte{}) // 清除单行注释
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(b))
	if err != nil {
		return
	}

	arti := doc.Find(".sdc-article-body.sdc-article-body--lead")
	arti.Find(".sdc-site-outbrain.sdc-site-outbrain--AR_5").Remove()
	arti.Find(".sdc-site-layout-sticky-region").Remove()
	arti.Find(".sdc-article-widget.sdc-article-related-stories").Remove()
	arti.Find(".sdc-article-widget.sdc-site-oddschecker").Remove()
	out, err = arti.Html()
	if err != nil {
		return
	}
	return out, nil
}

type SkysportResp struct {
	Result []SkyData `json:"result"`
}

type SkyData struct {
	Intro string `json:"intro"`
	Title string `json:"title"`
	Url   string `json:"url"` // str of ts second
}
