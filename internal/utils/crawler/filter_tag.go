package crawler

import (
	"regexp"
	"strings"
)

func RemoveHTMLTags(input string) string {
	// 使用正则表达式匹配HTML标签并去除
	re := regexp.MustCompile(`<.*?>`)
	return re.ReplaceAllString(input, "")
}

func removeAngleBrackets(text string) string {
	// 替换掉 < 和 > 字符
	text = strings.ReplaceAll(text, "<", "")
	text = strings.ReplaceAll(text, ">", "")
	return text
}

// 中英文双引号混用 导致解析错误
// "我练习普拉提、健身、热身，我锻炼背部以便手臂能更快地反应，我想踢到身体允许的程度，我一年一年地变老，但我的身体可以让我踢到41或42岁，毫无问题，等到我脑子爆炸的时候，就再也无法修复了，哈哈。"</strong></p><h2 class="dqd-Title-t2-s1">斯卡洛尼是一个什么样的人？</h2><p>“斯卡洛尼是一个很忠诚的人，如果必须让某人离开，
func replaceQuotes(input string) string {
	// 替换英文双引号为中文双引号
	input = strings.ReplaceAll(input, `"`, `“`)
	input = strings.ReplaceAll(input, `“`, `”`)
	// 替换英文单引号为中文单引号
	input = strings.ReplaceAll(input, `'`, `‘`)
	input = strings.ReplaceAll(input, `‘`, `’`)
	return input
}

// 提取HTML标签内的文本，并进行引号替换
func extractAndReplaceQuotes(input string) string {
	// 正则表达式：匹配所有标签内的内容
	re := regexp.MustCompile(`<([^>]+)>(.*?)</[^>]+>`)
	// 使用替换函数对匹配的标签内容进行处理
	return re.ReplaceAllStringFunc(input, func(match string) string {
		// 提取标签内的文本部分
		// 提取标签内的文本
		reText := regexp.MustCompile(`>(.*?)<`)
		textMatch := reText.FindStringSubmatch(match)
		if len(textMatch) > 1 {
			// 替换文本中的引号
			updatedText := replaceQuotes(textMatch[1])
			// 将替换后的文本替换回原标签中
			return strings.Replace(match, textMatch[1], updatedText, 1)
		}
		return match
	})
}

// 提取双引号内的内容并过滤掉 < 和 > 字符
// target="_self" title="你认为球员本场表现是什么水平，快来为他们打分吧>>" content="">你认为球员本场表现是什么水平，快来为他们打分吧>></a><br/></p>
func extractAndCleanQuotes(input string) string {
	// 匹配双引号内的内容
	re := regexp.MustCompile(`"([^"]*)"`) // 匹配所有双引号内的内容
	// 替换双引号内的内容，去除 < 和 > 字符
	result := re.ReplaceAllStringFunc(input, func(match string) string {
		// match[1:len(match)-1] 去掉匹配到的双引号
		return `"` + removeAngleBrackets(match[1:len(match)-1]) + `"`
	})
	return result
}

func FilerTags(htmlContent string) string {
	// 调用处理函数
	htmlContent = extractAndReplaceQuotes(htmlContent)
	htmlContent = extractAndCleanQuotes(htmlContent)
	htmlContent = RemoveHTMLTags(htmlContent)
	return htmlContent
}
