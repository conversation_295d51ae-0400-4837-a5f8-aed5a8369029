package collect_api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/valyala/fasthttp"
	"net/http"
)

func DoReqFull(ctx context.Context, reqUrl string, req *ReqFull) (res *ResFull, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	reqUrl += "?user=" + req.User + "&secret=" + req.Secret
	if req.Id > 0 {
		reqUrl += "&id=" + gconv.String(req.Id)
	}
	if req.Time > 0 {
		reqUrl += "&time=" + gconv.String(req.Time)
	}
	if req.Id == 0 && req.Time == 0 { // 两者都为0，默认使用id
		reqUrl += "&id=0"
	}
	if req.Limit > 0 {
		reqUrl += "&limit=" + gconv.String(req.Limit)
	}
	if req.Type > 0 {
		reqUrl += "&type=" + gconv.String(req.Type)
	}
	if req.Date != "" {
		reqUrl += "&date=" + req.Date
	}

	//g.Log().Info(ctx, "reqUrl:", reqUrl)
	respBody, statusCode, err := DoReqRaw(reqUrl)
	res = new(ResFull)
	err = json.Unmarshal(respBody, res)
	if err != nil {
		return nil, statusCode, err
	}
	if len(res.Err) > 0 {
		return res, statusCode, errors.New(res.Err)
	}
	return
}

func DoReqById(ctx context.Context, reqUrl string, req *ReqBase, id int) (res *ResBase, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()

	reqUrl += "?user=" + req.User + "&secret=" + req.Secret + "&id=" + gconv.String(id)
	respBody, statusCode, err := DoReqRaw(reqUrl)
	if err != nil {
		return nil, statusCode, err
	}

	// 检查 respBody 的长度
	if len(respBody) == 0 {
		return nil, statusCode, errors.New("response body is empty")
	}

	// 检查 respBody 是否为有效的 JSON
	if !json.Valid(respBody) {
		return nil, statusCode, errors.New("invalid JSON response body")
	}

	res = new(ResBase)
	err = json.Unmarshal(respBody, res)
	if err != nil {
		return nil, statusCode, err
	}
	if len(res.Err) > 0 {
		return res, statusCode, errors.New(res.Err)
	}
	return res, statusCode, nil
}

func DoReqBase(ctx context.Context, reqUrl string, req *ReqBase) (res *ResBase, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	reqUrl += "?user=" + req.User + "&secret=" + req.Secret
	respBody, statusCode, err := DoReqRaw(reqUrl)
	res = new(ResBase)
	err = json.Unmarshal(respBody, res)
	if err != nil {
		return nil, statusCode, err
	}
	if len(res.Err) > 0 {
		return res, statusCode, errors.New(res.Err)
	}
	return
}

func DoReqBaseRaw(ctx context.Context, reqUrl string) (res *ResBase, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	respBody, statusCode, err := DoReqRaw(reqUrl)
	g.Log().Line().Debug(ctx, "DoReqBaseReq", reqUrl, statusCode, err)
	res = new(ResBase)
	err = json.Unmarshal(respBody, res)
	if err != nil {
		return nil, statusCode, err
	}
	if len(res.Err) > 0 {
		return res, statusCode, errors.New(res.Err)
	}
	return
}

// TODO 读配置
func MakReqBase() *ReqBase {
	return &ReqBase{
		Secret: secret,
		User:   user,
	}
}

func DoReqRaw(reqUrl string) (respBody []byte, statusCode int, err error) {
	httpReq := fasthttp.AcquireRequest()
	httpReq.SetRequestURI(reqUrl)
	httpReq.Header.SetMethod(http.MethodGet)
	httpRes := fasthttp.AcquireResponse()
	err = client.Do(httpReq, httpRes)
	if err != nil {
		return nil, 0, err
	}
	fasthttp.ReleaseRequest(httpReq)
	defer fasthttp.ReleaseResponse(httpRes)
	statusCode = httpRes.StatusCode()
	respBody = httpRes.Body()
	g.Log().Line().Debug(context.TODO(), "respStatus:", statusCode)
	if statusCode != http.StatusOK {
		return nil, statusCode, errors.New("Http status code:" + gconv.String(statusCode))
	}
	return
}

var client *fasthttp.Client = &fasthttp.Client{
	NoDefaultUserAgentHeader: true,
}
