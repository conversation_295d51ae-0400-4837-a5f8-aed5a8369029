package video

import (
	"context"
	"encoding/json"
	"fmt"
	"gtcms/internal/utils/collect_api"
)

func GetLive(ctx context.Context) (results []VideoLiveResult, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	reqBase := collect_api.MakReqBase()
	res, statusCode, err := collect_api.DoReqBaseRaw(ctx, collect_api.VideoServer+"pushurl_v4?user="+reqBase.User+"&secret="+reqBase.Secret)
	if err != nil {
		return nil, statusCode, err
	}
	results = make([]VideoLiveResult, 0)
	err = json.Unmarshal(res.Data, &results)
	if err != nil {
		return nil, statusCode, err
	}
	return
}

type VideoLiveResult struct {
	SportId     int    `json:"sport_id"`     // 1足球，2篮球 (即回放的type_id)
	MatchId     int    `json:"match_id"`     // 纳米的比赛id
	MatchTime   int64  `json:"match_time"`   // 比赛的时间戳秒
	MatchStatus int    `json:"match_status"` // 比赛状态。1未开赛，2上半，3中，4下半，8完场
	CompId      int    `json:"comp_id"`      // 赛事id
	Comp        string `json:"comp"`         // 赛事名，简称
	Home        string `json:"home"`         // 主队名
	Away        string `json:"away"`         // 客队名
	Pushurl1    string `json:"pushurl1"`     // 推流链接， rtmp://
	Pushurl2    string `json:"pushurl2"`
	Pushurl3    string `json:"pushurl3"`
}
