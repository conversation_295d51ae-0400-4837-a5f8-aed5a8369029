package video

import (
	"context"
	"encoding/json"
	"fmt"
	"gtcms/internal/utils/collect_api"
)

// 获取印尼赛事录像
func GetIdRecords(ctx context.Context) (results []VideoRecordResult, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	res, statusCode, err := collect_api.DoReqBaseRaw(ctx, "https://jk.77livejk.cc/app/TGLiveRecords?check_type=50&url_type=1")
	if err != nil {
		return nil, statusCode, err
	}
	results = make([]VideoRecordResult, 0)
	err = json.Unmarshal(res.List, &results)
	if err != nil {
		return nil, statusCode, err
	}
	return

}
