package video

import (
	"context"
	"encoding/json"
	"fmt"
	"gtcms/internal/utils/collect_api"
)

func GetRecords(ctx context.Context) (results []VideoRecordResult, statusCode int, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("panic occurred during unmarshal: %v", r)
		}
	}()
	res, statusCode, err := collect_api.DoReqBaseRaw(ctx, "https://jk.tgrfst.cc/app/TGLiveRecords?url_type=1")
	if err != nil {
		return nil, statusCode, err
	}
	results = make([]VideoRecordResult, 0)
	err = json.Unmarshal(res.List, &results)
	if err != nil {
		return nil, statusCode, err
	}
	return

}

type VideoRecordResult struct {
	TypeId      int    `json:"type_id"`       // 1足球，2篮球 (即直播的sport_id)
	MatchId     int    `json:"match_id"`      // 纳米的比赛id
	MatchTime   string `json:"match_time"`    //2024-04-22 09:30:00
	ShortNameZh string `json:"short_name_zh"` // 赛事的中文简称， eg: NBA  亚冠杯
	Url         string `json:"url"`           // 回放链接
	MatchName   string `json:"match_name"`    // 标题，eg:俄克拉荷马城雷霆 VS 新奥尔良鹈鹕
	ClassName   string `json:"class_name"`    // eg: 篮球/足球
	Cover       string `json:"cover"`         // 封面图url

}
