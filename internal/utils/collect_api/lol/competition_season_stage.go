package lol

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type CompetitionResult struct {
	collect_api.ResResultBase
	ID         int    `json:"id"`           // 赛事ID
	NameZh     string `json:"name_zh"`      // 中文名称
	NameEn     string `json:"name_en"`      // 英文名称
	AbbrZh     string `json:"abbr_zh"`      // 中文简称
	AbbrEn     string `json:"abbr_en"`      // 英文简称
	StatusID   int    `json:"status_id"`    // 状态（详见状态码）
	Logo       string `json:"logo"`         // 赛事Logo
	Cover      string `json:"cover"`        // 封面图
	StartTime  int    `json:"start_time"`   // 开始时间（时间戳）
	EndTime    int    `json:"end_time"`     // 结束时间（时间戳）
	Type       int    `json:"type"`         // 赛事类型
	CityName   string `json:"city_name"`    // 举办地 中文
	CityNameEn string `json:"city_name_en"` // 举办地 英文
	PricePool  string `json:"price_pool"`   // 奖金池
	UpdatedAt  int    `json:"updated_at"`   // 更新时间
}

func GetCompetitionList(ctx context.Context, req collect_api.ReqList) (results []CompetitionResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CompetitionResult](ctx, Prefix, "/tournament", req)
}
