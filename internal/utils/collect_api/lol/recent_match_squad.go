package lol

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type MatchResult struct {
	ID          int           `json:"id"`          // 比赛ID
	Box         int           `json:"box"`         // 总局数
	Tournament  NestedID      `json:"tournament"`  // 赛事项（只包含ID）
	Stage       NestedID      `json:"stage"`       // 阶段项（只包含ID）
	Home        TeamScore     `json:"home"`        // 主队项
	Away        TeamScore     `json:"away"`        // 客队项
	MatchTime   int           `json:"match_time"`  // 比赛时间（时间戳）
	StatusID    int           `json:"status_id"`   // 比赛状态
	Coverage    MatchCoverage `json:"coverage"`    // 覆盖信息
	Description string        `json:"description"` // 备注
	UpdatedAt   int           `json:"updated_at"`  // 更新时间
}

type NestedID struct {
	ID int `json:"id"`
}

type TeamScore struct {
	ID    int `json:"id"`
	Score int `json:"score"`
}

type MatchCoverage struct {
	Mlive int `json:"mlive"` // 是否有动画
}

type MatchLive struct {
	ID             int          `json:"id"`               // 比赛ID
	StatusID       int          `json:"status_id"`        // 比赛状态
	BoxNum         int          `json:"box_num"`          // 当前第几局
	SingleStatusID int          `json:"single_status_id"` // 当前局比赛状态
	Timer          []int        `json:"timer"`            // 比赛时间信息 [是否走表, 是否倒计时, 发布时间, 时间差, 忽略]
	Tournament     NestedID     `json:"tournament"`       // 赛事对象
	Home           TeamDetail   `json:"home"`             // 主队信息
	Away           TeamDetail   `json:"away"`             // 客队信息
	EconomyLines   []string     `json:"economy_lines"`    // 经济曲线，如 "1:124"
	Event          []MatchEvent `json:"event"`            // 比赛事件
	UpdatedAt      int          `json:"updated_at"`       // 更新时间
}

type TeamDetail struct {
	ID      int            `json:"id"`      // 战队ID
	Side    int            `json:"side"`    // 队伍方（1.蓝色方、2.红色方）
	Score   int            `json:"score"`   // 当前局比分
	Stats   []int          `json:"stats"`   // 统计数据（共15项）
	Stats2  []int          `json:"stats2"`  // 附加统计（共10项）
	Pick    []int          `json:"pick"`    // 选择的英雄ID列表
	Ban     []int          `json:"ban"`     // 禁用的英雄ID列表
	Players []PlayerDetail `json:"players"` // 所有选手数据
}

type PlayerDetail struct {
	ID         int      `json:"id"`         // 选手ID
	Hero       NestedID `json:"hero"`       // 使用英雄ID
	Stats      []int    `json:"stats"`      // 选手统计数据（击杀、死亡、补刀等）
	Equipments []int    `json:"equipments"` // 装备ID（长度固定为7）
	Level      int      `json:"level"`      // 英雄等级
	Spells     []int    `json:"spells"`     // 召唤师技能ID（2个）
	Position   int      `json:"position"`   // 位置（1-adc、2-中单等）
}

type MatchEvent struct {
	Type     int        `json:"type"`     // 类型（1-击杀英雄、2-推塔等）
	SubType  int        `json:"sub_type"` // 怪物子类型（仅当type为4时有值）
	Side     int        `json:"side"`     // 所属方（1-蓝色方、2-红色方）
	Time     int        `json:"time"`     // 发生时间（单位秒）
	Killer   EventActor `json:"killer"`   // 击杀方
	Killered EventActor `json:"killered"` // 被击杀方
}

type EventActor struct {
	Type int      `json:"type"`           // 类型（1-英雄、0-其他）
	Hero NestedID `json:"hero,omitempty"` // 如果是英雄，包含英雄ID
}

func GetMatchList(ctx context.Context, req collect_api.ReqList) (results []MatchResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchResult](ctx, Prefix, "/match", req)
}

func GetMatchLive(ctx context.Context, req collect_api.ReqList) (results []MatchLive, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchLive](ctx, Prefix, "/match/live", req)
}
