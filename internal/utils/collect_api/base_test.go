package collect_api

import (
	"encoding/json"
	"testing"
)

func Test_Res(t *testing.T) {
	s := `{"code":99,"query":{"total":1,"limit":2},"results":[{"name":"aaa"},{"name":"bbb"}]}`
	res := new(ResFull)
	err := json.Unmarshal([]byte(s), res)
	if res.Code != 99 {
		t.Fail()
		return
	}

	type result struct {
		Name string
	}
	results := make([]result, 0)
	err = json.Unmarshal(res.Results, &results)
	if err != nil {
		t.Fail()
		return
	}
	if res.Query.Limit != 2 || len(results) != 2 {
		t.Fail()
		return
	}
	t.Log(results)
}

func Test_Comparable(t *testing.T) {
	type TN struct {
		Name string `json:"name"`
	}
	s := `[{"name":"aaa"},{"name":"bbb"},{"name":"ccc"}]`
	r, st, e := testComp[TN](s)
	t.Log(r, st, e)

}
func testComp[K comparable](s string) (results []K, statusCode int, err error) {
	results = make([]K, 0)
	err = json.Unmarshal([]byte(s), &results)
	return
}
