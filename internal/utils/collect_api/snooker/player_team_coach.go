package snooker

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type TeamResult struct {
	collect_api.ResResultBase
	CountryId int `json:"country_id"`

	collect_api.ResResultName
	collect_api.ResResultShortName
	Abbr     string    `json:"abbr"`
	Logo     string    `json:"logo"`
	Gender   int       `json:"gender" dc:"是否国家队"`
	National int       `json:"national" dc:"国家id"`
	Uid      int       `json:"uid" dc:"球队id(重复球队合并后的对应id)"`
	Extra    ExtraItem `json:"extra" dc:"个人信息（没有数据，为空）"`
}

type ExtraItem struct {
	Birthday        interface{} `json:"birthday" dc:"生日"`
	LocationZh      string      `json:"location_zh" dc:"现居住地(中)"`
	LocationEn      string      `json:"location_en" dc:"现居住地(英)"`
	Turned          int         `json:"turned" dc:"转入职业年份"`
	BestPrize       string      `json:"best_prize" dc:"职业生涯最高级别奖项"`
	HighestScore    int         `json:"highest_score" dc:"最高单局分数"`
	HighestScoreNum int         `json:"highest_score_num" dc:"最高单局分数次数"`
	MoneyEarnings   int         `json:"money_earnings" dc:"最高单局分数次数"`
}
type TeamStatsResult struct {
	collect_api.ResResultBase
	TeamId          int     `json:"team_id" dc:"球队id"`
	Played          int     `json:"played" dc:"总局数"`
	Won             int     `json:"won" dc:"胜局数"`
	Lost            int     `json:"lost" dc:"负局数"`
	WonRate         float64 `json:"won_rate" dc:"胜率"`
	Scores          int     `json:"scores" dc:"总得分"`
	ScoresPer       float64 `json:"scores_per" dc:"每局平均得分"`
	OverFifty       int     `json:"over_fifty" dc:"单杆超过50分次数"`
	OverFiftyRate   float64 `json:"over_fifty_rate" dc:"单杆超过50分基数"`
	OverHundred     int     `json:"over_hundred" dc:"单杆超过100分次数"`
	OverHundredRate float64 `json:"over_hundred_rate" dc:"单杆超过100分基数"`
}

type TeamRankingResult struct {
	//collect_api.ResResultBase
	TeamId    int `json:"team_id" dc:"球队id"`
	Ranking   int `json:"ranking" dc:"名次"`
	Price     int `json:"price" dc:"奖金"`
	PriceMode int `json:"price_mode" dc:"奖金单位，1-英镑£"`
}

func GetTeamList(ctx context.Context, req collect_api.ReqList) (results []TeamResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamResult](ctx, Prefix, "/team/list", req)
}
func GetTeamStatsList(ctx context.Context, req collect_api.ReqList) (results []TeamStatsResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamStatsResult](ctx, Prefix, "/team/stats/list", req)
}

func GetTeamRankingList(ctx context.Context, req collect_api.ReqList) (results []TeamRankingResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamRankingResult](ctx, Prefix, "/team/ranking", req)
}

type PlayerResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo         string `json:"logo"`
	CountryId    int    `json:"country_id"`
	Nationality  string `json:"nationality" dc:"国籍"`
	NationalLogo string `json:"national_logo" dc:"国家队球员logo, 判断球队是国家队时用"`
	Birthday     int    `json:"birthday"`
	Age          int    `json:"age"`
	Height       int    `json:"height" dc:"身高"`
	Weight       int    `json:"weight" dc:"体重"`
	collect_api.ResResultMarket
	ContractUntil   int       `json:"contract_until" dc:"合同截止时间"`
	PreferredFoot   int       `json:"preferred_foot" dc:"惯用脚。0-未知、1-左脚、2-右脚、3-左右脚"`
	Suffix          string    `json:"suffix" dc:"球员u系列(??)"`
	CoachId         int       `json:"coach_id" dc:"球员转为教练后的对应id"`
	Uid             int       `json:"uid" dc:"重复球员合并后的对应球员id"`
	Ability         [][3]int  `json:"ability" dc:"能力评分。[类型，评分，平均分] 满分100. 能力评分字段说明：1-扑救 2-预判3-处理球 4-空中 5-战术 6-进攻 7-防守 8-创造力 9-技术。eg:[6,99,67]"`
	Characteristics [][][]int `json:"characteristics" dc:"技术特点说明, TODO:等拿到接口再研究"`
	Position        string    `json:"position" dc:"擅长位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	Positions       []any     `json:"positions" dc:"详细位置。eg:[\"RW\", [\"ST\"]] index 0:主要位置，1:次要位置列表。 详细位置字段说明（没有字段不存在'LW'-左边锋 'RW'-右边锋 'ST'-前锋 'AM'-攻击型中场 'ML'-左中场 'MC'-中路中场 'MR'-右中场 'DM'-防守型中场 'DL'-左后卫 'DC'-中后卫 'DR'-右后卫 'GK'-守门员" `
}

func GetPlayerList(ctx context.Context, req collect_api.ReqList) (results []PlayerResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[PlayerResult](ctx, Prefix, "/player/list", req)
}

type CoachResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Logo               string `json:"logo"`
	Birthday           int    `json:"birthday"`
	Age                int    `json:"age"`
	CountryId          int    `json:"country_id"`
	Nationality        string `json:"nationality" dc:"国籍"`
	PreferredFormation string `json:"preferred_formation" dc:"习惯阵形"`
	TeamId             int    `json:"team_id" dc:"执教球队id"`
	Joined             int    `json:"joined" dc:"加盟时间"`
	ContractUntil      int    `json:"contract_until" dc:"合同截止时间"`
}

func GetCoachList(ctx context.Context, req collect_api.ReqList) (results []CoachResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CoachResult](ctx, Prefix, "/coach/list", req)
}
