package dota

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type TeamResult struct {
	collect_api.ResResultBase
	ID            int      `json:"id"`             // 战队ID
	NameZh        string   `json:"name_zh"`        // 中文名称
	NameEn        string   `json:"name_en"`        // 英文名称
	AbbrZh        string   `json:"abbr_zh"`        // 中文简称
	AbbrEn        string   `json:"abbr_en"`        // 英文简称
	Logo          string   `json:"logo"`           // 战队Logo
	Country       NestedID `json:"country"`        // 国家对象
	Region        NestedID `json:"region"`         // 赛区对象
	CreateTime    int      `json:"create_time"`    // 成立时间（时间戳）
	TotalEarnings string   `json:"total_earnings"` // 总奖金（字符串）
	UpdatedAt     int      `json:"updated_at"`     // 更新时间
}

func GetTeamList(ctx context.Context, req collect_api.ReqList) (results []TeamResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamResult](ctx, Prefix, "/team", req)
}

type PlayerResult struct {
	collect_api.ResResultBase
	ID        int      `json:"id"`         // 选手ID
	NameZh    string   `json:"name_zh"`    // 中文名称
	NameEn    string   `json:"name_en"`    // 英文名称
	AbbrZh    string   `json:"abbr_zh"`    // 中文简称
	AbbrEn    string   `json:"abbr_en"`    // 英文简称
	Logo      string   `json:"logo"`       // 选手Logo
	Team      NestedID `json:"team"`       // 战队对象
	Country   NestedID `json:"country"`    // 国家对象
	RealName  string   `json:"real_name"`  // 真实名称
	Birthday  int      `json:"birthday"`   // 生日（时间戳）
	Retired   int      `json:"retired"`    // 是否退役（1 是，0 否）
	Status    int      `json:"status"`     // 1 首发，2 替补
	Position  int      `json:"position"`   // 位置（1-adc，2-中单等）
	UpdatedAt int      `json:"updated_at"` // 更新时间
}

func GetPlayerList(ctx context.Context, req collect_api.ReqList) (results []PlayerResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[PlayerResult](ctx, Prefix, "/player", req)
}
