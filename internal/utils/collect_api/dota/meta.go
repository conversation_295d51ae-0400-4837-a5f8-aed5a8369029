package dota

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type CategoryResult struct {
	Id        int    `json:"id"`
	NameZh    string `json:"name_zh"`
	NameZht   string `json:"name_zht"`
	NameEn    string `json:"name_en"`
	UpdatedAt int    `json:"updated_at"`
}

func GetCategoryList(ctx context.Context) (results []CategoryResult, statusCode int, err error) {
	return collect_api.GetBaseList[CategoryResult](ctx, Prefix, "/category/list")
}

type CountryResult struct {
	Id         int    `json:"id"`
	CategoryId int    `json:"category_id"`
	NameZh     string `json:"name_zh"`
	NameZht    string `json:"name_zht"`
	NameEn     string `json:"name_en"`
	Logo       string `json:"logo"`
	UpdatedAt  int    `json:"updated_at"`
}

func GetCountryList(ctx context.Context) (results []CountryResult, statusCode int, err error) {
	return collect_api.GetBaseList[CountryResult](ctx, Prefix, "/country/list")
}
