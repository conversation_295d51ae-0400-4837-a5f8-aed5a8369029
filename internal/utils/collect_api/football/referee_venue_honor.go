package football

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

// 裁判信息
type RefereeResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Birthday int `json:"birthday"`
	Age      int `json:"age"`
}

func GetRefereeList(ctx context.Context, req collect_api.ReqList) (results []RefereeResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[RefereeResult](ctx, Prefix, "/referee/list", req)
}

// 场馆
type VenueResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Capacity  int `json:"capacity" dc:"球场容量"`
	CountryId int `json:"country_id" dc:"国家id"`
}

func GetVenueList(ctx context.Context, req collect_api.ReqList) (results []VenueResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[VenueResult](ctx, Prefix, "/venue/list", req)
}

type HonorResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Logo string `json:"logo" dc:"荣誉logo"`
}

func GetHonorList(ctx context.Context, req collect_api.ReqList) (results []HonorResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[HonorResult](ctx, Prefix, "/honor/list", req)
}
