package football

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

// 球员的荣誉，球员转会， 球队荣誉

type PlayerHonorResult struct {
	Id        int               `json:"id"`
	Honors    []PlayerHonorInfo `json:"honors"`
	UpdatedAt int               `json:"updated_at"`
}

type PlayerHonorInfo struct {
	HonorId       int    `json:"honor_id"`
	Season        string `json:"season"`
	TeamId        int    `json:"team_id"`
	CompetitionId int    `json:"competition_id"`
	SeasonId      int    `json:"season_id"`
	CountryId     int    `json:"country_id"`
}

func GetPlayerHonorList(ctx context.Context, req collect_api.ReqList) (results []PlayerHonorResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[PlayerHonorResult](ctx, Prefix, "/player/honor/list", req)
}

type TeamHonorResult struct {
	Id        int             `json:"id"`
	Honors    []TeamHonorInfo `json:"honors"`
	UpdatedAt int             `json:"updated_at"`
}

type TeamHonorInfo struct {
	HonorId       int    `json:"honor_id"`
	Season        string `json:"season"`
	CompetitionId int    `json:"competition_id"`
	SeasonId      int    `json:"season_id"`
}

func GetTeamHonorList(ctx context.Context, req collect_api.ReqList) (results []TeamHonorResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamHonorResult](ctx, Prefix, "/team/honor/list", req)
}

type TransferResult struct {
	Id        int            `json:"id"`
	Transfer  []TransferInfo `json:"transfer"`
	UpdatedAt int            `json:"updated_at"`
}

type TransferInfo struct {
	FromTeamId   int    `json:"from_team_id"` // 转出球队id
	FromTeamName string `json:"from_team_name"`
	ToTeamId     int    `json:"to_team_id"`
	ToTeamName   string `json:"to_team_name"`
	TransferType int    `json:"transfer_type"` //转会类型，1-租借、2-租借结束、3-转会、4-退役、5-选秀、6-已解约、7-已签约、8-未知
	TransferTime int    `json:"transfer_time"` // 转会时间
	TransferFee  int    `json:"transfer_fee"`  // 转会费用
	TransferDesc string `json:"transfer_desc"` // 转会描述（含单位
}

func GetTransferList(ctx context.Context, req collect_api.ReqList) (results []TransferResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TransferResult](ctx, Prefix, "/player/transfer/list", req)
}
