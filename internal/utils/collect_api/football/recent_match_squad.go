package football

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type MatchResult struct {
	collect_api.ResResultBase
	SeasonId      int    `json:"season_id"`      // 赛季id
	CompetitionId int    `json:"competition_id"` // 赛事id
	HomeTeamId    int    `json:"home_team_id"`   // 主队id
	AwayTeamId    int    `json:"away_team_id"`   // 客队id
	Kind          int    `json:"kind"`           // 类型id，1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无
	StatusId      int    `json:"status_id"`      // 比赛状态，详见状态码->比赛状态
	MatchTime     int64  `json:"match_time"`     // 比赛时间
	VenueId       int    `json:"venue_id"`       // 场馆id
	HomeScores    [7]int `json:"home_scores"`    // 主队比分: [常规时间比分，半场比分，红牌，黄牌，角球，加时比分(包括常规时间)，点球大战比分]
	AwayScores    [7]int `json:"away_scores"`    // 客队比分，
}

type MatchLive struct {
	Id    int   `json:"id"` // 比赛id
	Score []any `json:"score"`
}

type MatchAnalysisResult struct {
	Info             InfoParam             `json:"info"`              // 比赛信息
	GoalDistribution GoalDistributionParam `json:"goal_distribution"` // 进球分布
	History          HistoryParam          `json:"history"`           // 进球分布
}
type InfoParam struct {
	Id            int   `json:"id"`
	SeasonId      int   `json:"season_id"`      // 赛季id
	CompetitionId int   `json:"competition_id"` // 赛事id
	HomeTeamId    int   `json:"home_team_id"`   // 主队id
	AwayTeamId    int   `json:"away_team_id"`   // 客队id
	MatchTime     int64 `json:"match_time"`     // 比赛时间
}
type HistoryParam struct {
	Home []HistoryHomeParam `json:"home"` // 主队进球分布
	Away []HistoryHomeParam `json:"away"` // 客队进球分布
}
type HistoryHomeParam struct {
	Id            int   `json:"id"`             // 比赛id
	SeasonId      int   `json:"season_id"`      // 赛季id
	CompetitionId int   `json:"competition_id"` // 赛事id
	HomeTeamId    int   `json:"home_team_id"`   // 主队id
	AwayTeamId    int   `json:"away_team_id"`   // 客队id
	MatchTime     int64 `json:"match_time"`     // 比赛时间
	HomeScores    []int `json:"home_scores"`    // 比分字段说明
	AwayScores    []int `json:"away_scores"`    // 比分字段说明
}

type GoalDistributionParam struct {
	Home HomeParam `json:"home"` // 主队进球分布
	Away AwayParam `json:"away"` // 客队进球分布
}

type HomeParam struct {
	All AllParam `json:"all"` // 主队进球分布数据
}
type AwayParam struct {
	All AllParam `json:"all"` // 客队进球分布数据
}
type AllParam struct {
	Matches  int     `json:"matches"`  // 比赛场次
	Scored   [][]int `json:"scored"`   // 客队进球分布
	Conceded [][]int `json:"conceded"` // 主队失球分布
}

func GetMatchList(ctx context.Context, req collect_api.ReqList) (results []MatchResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchResult](ctx, Prefix, "/match/list", req)
}

func GetMatchLive(ctx context.Context, req collect_api.ReqList) (results []MatchLive, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchLive](ctx, Prefix, "/match/live", req)
}

func GetMatchAnalysis(ctx context.Context, req collect_api.ReqList) (results MatchAnalysisResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetNotSlice[MatchAnalysisResult](ctx, Prefix, "/match/analysis", req)
}

type SquadResult struct {
	collect_api.ResResultBase
	Squad []Squad `json:"squad"`
}

type Squad struct {
	PlayerId       int    `json:"player_id" dc:"球员id"`
	Position       string `json:"position" dc:"球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	HasShirtNumber int    `json:"has_shirt_number" dc:"是否有球衣号码, 1是"`
	ShirtNumber    int    `json:"shirt_number" dc:"球衣号码"`
}

type VideoCollection struct {
	Type       int    `json:"type" dc:"类型，1-集锦、2-录像"`
	Title      string `json:"title" dc:"标题"`
	MobileLink string `json:"mobile_link" dc:"wap直播地址"`
	PcLink     string `json:"pc_link" dc:"web直播地址"`
	Cover      string `json:"cover" dc:"图片"`
	Duration   int    `json:"duration" dc:"时长-秒（s）"`
}

func GetSquadList(ctx context.Context, req collect_api.ReqList) (results []SquadResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[SquadResult](ctx, Prefix, "/team/squad/list", req)
}

func GetVideoCollectionList(ctx context.Context, req collect_api.ReqList) (results []VideoCollection, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[VideoCollection](ctx, Prefix, "/match/stream/video_collection", req)
}
