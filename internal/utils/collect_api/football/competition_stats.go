package football

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

// 赛事积分榜
// https://open.sportnanoapi.com/api/v5/football/competition/table/detail
type CompetitionTableResult struct {
	Promotions []CompetitionPromotion `json:"promotions"`
	Tables     []CompetitionTable     `json:"tables"`
	SeasonId   int                    `json:"season_id"`
}

// 赛事统计数据
// https://open.sportnanoapi.com/api/v5/football/competition/stats/detail
// 其实还有其它字段， 用不到，先不采集
type CompetitionStatsResult struct {
	Shooters     []CompetitionShooter `json:"shooters"`      // 射手榜
	PlayersStats []Players            `json:"players_stats"` // 球员数据字段说明
	TeamsStats   []Teams              `json:"teams_stats"`   // 球队数据字段说明
	SeasonId     int                  `json:"season_id"`
}

type CompetitionPromotion struct {
	Id int `json:"id"`
	collect_api.ResResultName
	Color string `json:"color"`
}

func GetCompetitionTableDetail(ctx context.Context, id int) (results *CompetitionTableResult, statusCode int, err error) {
	return collect_api.GetById[CompetitionTableResult](ctx, Prefix, "/competition/table/detail", id)
}

func GetCompetitionStatsDetail(ctx context.Context, id int) (results *CompetitionStatsResult, statusCode int, err error) {
	return collect_api.GetById[CompetitionStatsResult](ctx, Prefix, "/competition/stats/detail", id)
}

type CompetitionTable struct {
	Id         int                   `json:"id"`         // 积分榜表id
	Conference string                `json:"conference"` //分区信息，少数有
	Group      int                   `json:"group"`      // 不为0表示分组赛的第几组，1-A、2-B以此类推
	StageId    int                   `json:"stage_id"`   // 阶段id
	Rows       []CompetitionTableRow `json:"rows"`       // 球队积分项
}

type CompetitionTableRow struct {
	TeamId       int    `json:"team_id"`       // 球队id
	PromotionId  int    `json:"promotion_id"`  // 升降级
	DeductPoints int    `json:"deduct_points"` // 扣除积分
	Note         string `json:"note"`          // 说明

	Points       int `json:"points"`        // 积分
	Position     int `json:"position"`      //排名
	Total        int `json:"total"`         // 总场数
	Won          int `json:"won"`           // 胜场
	Draw         int `json:"draw"`          // 平场
	Loss         int `json:"loss"`          // 负场
	Goals        int `json:"goals"`         // 进球数
	GoalsAgainst int `json:"goals_against"` // 失球
	GoalsDiff    int `json:"goals_diff"`    // 净胜球

	HomePoints       int `json:"home_points"`        // 积分
	HomePosition     int `json:"home_position"`      //排名
	HomeTotal        int `json:"home_total"`         // 总场数
	HomeWon          int `json:"home_won"`           // 胜场
	HomeDraw         int `json:"home_draw"`          // 平场
	HomeLoss         int `json:"home_loss"`          // 负场
	HomeGoals        int `json:"home_goals"`         // 进球数
	HomeGoalsAgainst int `json:"home_goals_against"` // 失球
	HomeGoalsDiff    int `json:"home_goals_diff"`    // 净胜球

	AwayPoints       int `json:"away_points"`        // 积分
	AwayPosition     int `json:"away_position"`      //排名
	AwayTotal        int `json:"away_total"`         // 总场数
	AwayWon          int `json:"away_won"`           // 胜场
	AwayDraw         int `json:"away_draw"`          // 平场
	AwayLoss         int `json:"away_loss"`          // 负场
	AwayGoals        int `json:"away_goals"`         // 进球数
	AwayGoalsAgainst int `json:"away_goals_against"` // 失球
	AwayGoalsDiff    int `json:"away_goals_diff"`    // 净胜球
}

type CompetitionShooter struct {
	Position      int `json:"position"` // 排名
	PlayerId      int `json:"player_id"`
	TeamId        int `json:"team_id"`
	Goals         int `json:"goals"`          // 进球
	Penalty       int `json:"penalty"`        // 点球
	Assists       int `json:"assists"`        // 助攻
	MinutesPlayed int `json:"minutes_played"` // 出场时间
}

type Players struct {
	PlayerId          int `json:"player_id"`           // 球员id
	TeamId            int `json:"team_id"`             //球队id
	Matches           int `json:"matches"`             //比赛场次
	Court             int `json:"court"`               // 上场场次
	First             int `json:"first"`               // 首发
	Goals             int `json:"goals"`               // 进球
	Penalty           int `json:"penalty"`             // 点球
	Assists           int `json:"assists"`             // 助攻
	MinutesPlayed     int `json:"minutes_played"`      //出场时间(分钟)
	RedCards          int `json:"red_cards"`           //红牌
	YellowCards       int `json:"yellow_cards"`        // 黄牌
	Shots             int `json:"shots"`               // 射门
	ShotsOnTarget     int `json:"shots_on_target"`     // 射正
	Dribble           int `json:"dribble"`             // 过人
	DribbleSucc       int `json:"dribble_succ"`        // 过人成功
	Clearances        int `json:"clearances"`          //解围
	BlockedShots      int `json:"blocked_shots"`       //有效阻挡
	Interceptions     int `json:"interceptions"`       // 拦截
	Tackles           int `json:"tackles"`             // 抢断
	Passes            int `json:"passes"`              // 传球
	PassesAccuracy    int `json:"passes_accuracy"`     // 传球成功
	KeyPasses         int `json:"key_passes"`          // 关键传球
	Crosses           int `json:"crosses"`             //传中球
	CrossesAccuracy   int `json:"crosses_accuracy"`    //传中球成功
	LongBalls         int `json:"long_balls"`          // 长传
	LongBallsAccuracy int `json:"long_balls_accuracy"` // 成功长传
	Duels             int `json:"duels"`               // 1对1拼抢
	DuelsWon          int `json:"duels_won"`           // 1对1拼抢成功
	Dispossessed      int `json:"dispossessed"`        // 传球被断
	Fouls             int `json:"fouls"`               //犯规
	WasFouled         int `json:"was_fouled"`          //被侵犯
	Offsides          int `json:"offsides"`            // 越位
	Yellow2RedCards   int `json:"yellow2red_cards"`    // 两黄变红
	Saves             int `json:"saves"`               // 扑救
	Punches           int `json:"punches"`             // 拳击球
	RunsOut           int `json:"runs_out"`            // 守门员出击
	RunsOutSucc       int `json:"runs_out_succ"`       //守门员出击成功
	GoodHighClaim     int `json:"good_high_claim"`     //高空出击
	Freekicks         int `json:"freekicks"`           // 任意球
	FreekickGoals     int `json:"freekick_goals"`      // 任意球得分
	HitWoodwork       int `json:"hit_woodwork"`        // 击中门框
	Fastbreaks        int `json:"fastbreaks"`          // 快攻
	FastbreakShots    int `json:"fastbreak_shots"`     // 快攻射门
	FastbreakGoals    int `json:"fastbreak_goals"`     // 快攻进球
	PossLosts         int `json:"poss_losts"`          // 丢失球权
	Rating            int `json:"rating"`              // 评分，10为满分

}
type Teams struct {
	TeamId            int `json:"team_id"`             //球队id
	Matches           int `json:"matches"`             //比赛场次
	Goals             int `json:"goals"`               //进球
	Penalty           int `json:"penalty"`             //点球
	Assists           int `json:"assists"`             //助攻
	RedCards          int `json:"red_cards"`           //红牌
	YellowCards       int `json:"yellow_cards"`        //黄牌
	Shots             int `json:"shots"`               //射门
	ShotsOnTarget     int `json:"shots_on_target"`     //射正
	Dribble           int `json:"dribble"`             //过人
	DribbleSucc       int `json:"dribble_succ"`        //过人成功
	Clearances        int `json:"clearances"`          //解围
	BlockedShots      int `json:"blocked_shots"`       //有效阻挡
	Tackles           int `json:"tackles"`             //抢断
	Passes            int `json:"passes"`              //传球
	PassesAccuracy    int `json:"passes_accuracy"`     //传球成功
	KeyPasses         int `json:"key_passes"`          //关键传球
	Crosses           int `json:"crosses"`             //传中球
	CrossesAccuracy   int `json:"crosses_accuracy"`    //传中球成功
	LongBalls         int `json:"long_balls"`          //长传
	LongBallsAccuracy int `json:"long_balls_accuracy"` //成功长传
	Duels             int `json:"duels"`               //1对1拼抢
	DuelsWon          int `json:"duels_won"`           //1对1拼抢成功
	Fouls             int `json:"fouls"`               //犯规
	WasFouled         int `json:"was_fouled"`          //被侵犯
	GoalsAgainst      int `json:"goals_against"`       //失球
	Interceptions     int `json:"interceptions"`       //拦截
	Offsides          int `json:"offsides"`            //越位
	Yellow2RedCards   int `json:"yellow2red_cards"`    //两黄变红
	CornerKicks       int `json:"corner_kicks"`        //角球
	BallPossession    int `json:"ball_possession"`     //控球率
	Freekicks         int `json:"freekicks"`           //任意球
	FreekickGoals     int `json:"freekick_goals"`      //任意球得分
	HitWoodwork       int `json:"hit_woodwork"`        //击中门框
	Fastbreaks        int `json:"fastbreaks"`          //快攻
	FastbreakShots    int `json:"fastbreak_shots"`     //快攻射门
	FastbreakGoals    int `json:"fastbreak_goals"`     //快攻进球
	PossLosts         int `json:"poss_losts"`          //丢失球权
}
