package collect_api

import (
	"context"
	"encoding/json"
	"net/http"
)

type ReqBase struct {
	User   string `json:"user"`
	Secret string `json:"secret"`
}

type ReqList struct {
	Id    int    `json:"id" dc:"查询>=id的记录，并按id排序"`
	Time  int    `json:"time" dc:"查询更新时间>=time的记录，按time排序 （时间戳）"`
	Limit int    `json:"limit" dc:"返回数据最大值，默认1000，最大1000"`
	Type  int    `json:"type" dc:"查询类型，1-分类、2-国家 3-赛事 4-球队 5-球员"`
	Date  string `json:"date" dc:"查询日期，格式为yyyymmdd（20200101）"`
}

type ReqId struct {
	ReqBase
	Id int `json:"id"`
}

type ReqFull struct {
	ReqBase
	ReqList
}

type ResQuery struct {
	Total   int    `json:"total" dc:"返回数据总量"`
	Type    string `json:"type" dc:"查询类型，id查询：sequence、time查询：time，默认sequence"`
	Id      int    `json:"id" dc:"查询id值，默认为0（id查询，字段返回）"`
	MinId   int    `json:"min_id" dc:"返回数据最小id（id查询，字段返回）"`
	MaxId   int    `json:"max_id" dc:"返回数据最大id（id查询，字段返回）"`
	Limit   int    `json:"limit" dc:"可返回数据最大数（id查询，字段返回）"`
	Time    int    `json:"time" dc:"查询time值（time查询，字段返回）"`
	MinTime int    `json:"min_time" dc:"返回数据最小time(更新时间戳)（time查询，字段返回）"`
	MaxTime int    `json:"max_time" dc:"返回数据最大time(更新时间戳)（time查询，字段返回）"`
}

type ResFull struct {
	ResBase
	Query *ResQuery `json:"query"`
}

type ResBase struct {
	Code    int             `json:"code" dc:"状态码"`
	Results json.RawMessage `json:"results"`
	List    json.RawMessage `json:"list"` //  目前只有回放列表接口才用得到
	Data    json.RawMessage `json:"data"` //  目前只有直播列表接口才用得到
	Err     string          `json:"err" dc:"如果报错，这是报错信息"`
}

type ResResultBase struct {
	Id        int `json:"id"`
	UpdatedAt int `json:"updatedAt"`
}

type ResResultName struct {
	NameZh  string `json:"name_zh" dc:"中文名"`
	NameZht string `json:"name_zht" dc:"粤语名"`
	NameEn  string `json:"name_en"`
}
type ResResultShortName struct {
	ShortNameZh  string `json:"short_name_zh" dc:"中文简称"`
	ShortNameZht string `json:"short_name_zht" dc:"粤语简称"`
	ShortNameEn  string `json:"short_name_En"`
}

type ResResultMarket struct {
	MarketValue         int    `json:"market_value" dc:"市值"`
	MarketValueCurrency string `json:"market_value_currency" dc:"市值单位"`
}

type DeleteResult struct {
	Match       []int `json:"match"`
	Team        []int `json:"team"`
	Player      []int `json:"player"`
	Competition []int `json:"competition"`
	Season      []int `json:"season"`
	Stage       []int `json:"stage"`
}

const (
	// curl 'http://43.199.128.68/api/v5/basketball/team/squad/list?user=sptyliw&secret=f5de88930f02f347233ddf0425070605&id=10149&limit=1'
	//Server = "https://open.sportnanoapi.com/"
	Server = "http://43.199.128.68/"
	//VideoServer = "https://open.sportnanoapi.com/"
	VideoServer = "http://43.199.128.68/"
	user        = "sptyliw"
	secret      = "f5de88930f02f347233ddf0425070605"
)

func GetBaseList[K any](ctx context.Context, prefix, suffix string) (results []K, statusCode int, err error) {
	results = make([]K, 0)
	var res *ResBase
	res, statusCode, err = DoReqBase(ctx, Server+prefix+suffix, MakReqBase())
	if err != nil || statusCode != http.StatusOK {
		return
	}
	err = json.Unmarshal(res.Results, &results)
	return
}

func GetNotSlice[K any](ctx context.Context, prefix, suffix string, reqList ReqList) (results K, q *ResQuery, statusCode int, err error) {
	req := ReqFull{
		ReqBase: *MakReqBase(),
		ReqList: reqList,
	}
	var res *ResFull
	res, statusCode, err = DoReqFull(ctx, Server+prefix+suffix, &req)
	if err != nil || statusCode != http.StatusOK {
		return
	}
	err = json.Unmarshal(res.Results, &results)
	q = res.Query
	return
}

func GetFullList[K any](ctx context.Context, prefix, suffix string, reqList ReqList) (results []K, q *ResQuery, statusCode int, err error) {
	results = make([]K, 0)
	req := ReqFull{
		ReqBase: *MakReqBase(),
		ReqList: reqList,
	}
	var res *ResFull
	res, statusCode, err = DoReqFull(ctx, Server+prefix+suffix, &req)
	if err != nil || statusCode != http.StatusOK {
		return
	}
	err = json.Unmarshal(res.Results, &results)
	q = res.Query
	return
}

func GetById[K any](ctx context.Context, prefix, suffix string, id int) (results *K, statusCode int, err error) {
	results = new(K)
	var res *ResBase
	res, statusCode, err = DoReqById(ctx, Server+prefix+suffix, MakReqBase(), id)
	if err != nil || statusCode != http.StatusOK {
		return
	}
	err = json.Unmarshal(res.Results, &results)
	return
}
