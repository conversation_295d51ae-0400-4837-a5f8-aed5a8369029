package footballRecent

// 变动比赛
type MatchResult struct {
	Id            int            `json:"id"`
	SeasonId      int            `json:"season_id"`
	CompetitionId int            `json:"competition_id"`
	HomeTeamId    int            `json:"home_team_id" dc:"主队id"`
	AwayTeamId    int            `json:"away_team_id" dc:"客队id"`
	StatusId      int            `json:"status_id" dc:"比赛状态（详情需要枚举"`
	MatchTime     int            `json:"match_time" dc:"比赛时间"`
	Neutral       int            `json:"neutral" dc:"是否中立场 1是"`
	Note          string         `json:"note" dc:"备注"`
	HomeScores    [7]int         `json:"home_scores" dc:"主队比分字段 0常规时间比分，1半场比分，2红牌，3黄牌,4角球（-1表示没有数据），5加时比分(包括常规时间比分),6点球大战比分(不包含常规和加时赛比分)"`
	AwayScores    [7]int         `json:"away_scores" dc:"客队比分字段 0常规时间比分，1半场比分，2红牌，3黄牌,4角球（-1表示没有数据），5加时比分(包括常规时间比分),6点球大战比分(不包含常规和加时赛比分)"`
	HomePosition  string         `json:"home_position" dc:"主队排名"`
	AwayPosition  string         `json:"away_position" dc:"客队排名 "`
	Coverage      *MatchCoverage `json:"coverage" dc:"动画/情报/阵容 (???)"`
	VenueId       int            `json:"venue_id" dc:"场馆id"`
	RefereeId     int            `json:"referee_id" dc:"裁判"`
	RelatedId     int            `json:"related_id" dc:"双回合中另一回合比赛id"`
}

type MatchCoverage struct {
	Mlive        int `json:"mlive" dc:"是否有动画"`
	Intelligence int `json:"intelligence" dc:"是否有情报"`
	Lineup       int `json:"lineup" dc:"是否有阵容"`
}
