package basketball

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

// 赛事积分榜
// https://open.sportnanoapi.com/api/v5/basketball/competition/table/detail
type CompetitionTableResult struct {
	Promotions []CompetitionPromotion `json:"promotions"`
	Tables     []CompetitionTable     `json:"tables"`

	SeasonId int `json:"season_id"`
}

// 赛事统计数据
// https://open.sportnanoapi.com/api/v5/football/competition/stats/detail
// 其实还有其它字段， 用不到，先不采集
type CompetitionStatsResult struct {
	Shooters     []CompetitionShooter `json:"shooters"` // 射手榜
	SeasonId     int                  `json:"season_id"`
	PlayersStats []Players            `json:"players_stats"` // 球员数据字段说明
	TeamsStats   []Teams              `json:"teams_stats"`   // 球队数据字段说明
}

type CompetitionPromotion struct {
	Id int `json:"id"`
	collect_api.ResResultName
	Color string `json:"color"`
}

func GetCompetitionTableDetail(ctx context.Context, id int) (results *CompetitionTableResult, statusCode int, err error) {
	return collect_api.GetById[CompetitionTableResult](ctx, Prefix, "/competition/table/detail", id)
}

func GetCompetitionStatsDetail(ctx context.Context, id int) (results *CompetitionStatsResult, statusCode int, err error) {
	return collect_api.GetById[CompetitionStatsResult](ctx, Prefix, "/competition/stats/detail", id)
}

type CompetitionTable struct {
	Id      int                   `json:"id"`       // 积分榜表id
	Name    string                `json:"name"`     //  积分榜名称
	Scope   int                   `json:"scope"`    // 统计范围，1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、0-无
	StageId int                   `json:"stage_id"` // 阶段id
	Rows    []CompetitionTableRow `json:"rows"`     // 球队积分项
}

type CompetitionTableRow struct {
	TeamId      int `json:"team_id"`      // 球队id
	PromotionId int `json:"promotion_id"` // 升降级
	//DeductPoints int    `json:"deduct_points"` // 扣除积分
	//Points       int `json:"points"`        // 积分
	Position int    `json:"position"` //排名
	Note     string `json:"note"`     // 说明
	//Total        int    `json:"total"`         // 总场数
	Won int `json:"won"` // 胜场
	//Draw         int `json:"draw"`          // 平场
	Loss             int     `json:"loss"`               // 负场
	WonRate          float64 `json:"won_rate"`           // 胜率
	GameBack         string  `json:"game_back"`          // 胜场差 可能不存在
	PointsAvg        float64 `json:"points_avg"`         // 场均得分
	PointsAgainstAvg float64 `json:"points_against_avg"` // 场均失分
	DiffAvg          float64 `json:"diff_avg"`           // 场均净胜
	Streaks          int     `json:"streaks"`            // 近期连胜连败
	Home             string  `json:"home"`               // 主场： 主场胜-主场负
	Away             string  `json:"away"`               // 客场： 客场胜-客场负
	Division         string  `json:"division"`           // 赛区胜-负
	Conference       string  `json:"conference"`         // 东（西）部胜-负，该球队在东或西部的胜负数据，可能不存在
	Last10           string  `json:"last_10"`            // 近10场胜-负（在该赛季只打了5场比赛：4-1），可能不存在
	Points           float64 `json:"points"`             // 杯赛积分，可能不存在
	PointsFor        float64 `json:"points_for"`         // 杯赛总得分，可能不存在
	PointsAgt        float64 `json:"points_agt"`         // 杯赛总失分，可能不存在

	//Goals        int `json:"goals"`         // 进球数
	//GoalsAgainst int `json:"goals_against"` // 失球
	//GoalsDiff    int `json:"goals_diff"`    // 净胜球
	//
}

type CompetitionShooter struct {
	Position      int `json:"position"` // 排名
	PlayerId      int `json:"player_id"`
	TeamId        int `json:"team_id"`
	Goals         int `json:"goals"`          // 进球
	Penalty       int `json:"penalty"`        // 点球
	Assists       int `json:"assists"`        // 助攻
	MinutesPlayed int `json:"minutes_played"` // 出场时间
}

type Players struct {
	PlayerId            int     `json:"player_id"`             // 球员id
	TeamId              int     `json:"team_id"`               //球队id
	Scope               int     `json:"scope"`                 //统计范围，1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、7-附加赛
	Matches             int     `json:"matches"`               //比赛场次
	Court               int     `json:"court"`                 // 上场场次
	First               int     `json:"first"`                 // 首发
	MinutesPlayed       int     `json:"minutes_played"`        //出场时间(分钟)
	Points              int     `json:"points"`                // 得分
	FreeThrowsScored    int     `json:"free_throws_scored"`    // 罚球命中数
	FreeThrowsTotal     int     `json:"free_throws_total"`     // 罚球总数
	FreeThrowsAccuracy  string  `json:"free_throws_accuracy"`  // 罚球命中率
	TwoPointsScored     int     `json:"two_points_scored"`     // 两分球命中数
	TwoPointsTotal      int     `json:"two_points_total"`      // 两分球总数
	TwoPointsAccuracy   string  `json:"two_points_accuracy"`   // 两分球命中率
	ThreePointsScored   int     `json:"three_points_scored"`   // 三分球命中数
	ThreePointsTotal    int     `json:"three_points_total"`    // 三分球总数
	ThreePointsAccuracy string  `json:"three_points_accuracy"` // 三分球命中率
	FieldGoalsScored    int     `json:"field_goals_scored"`    // 投篮命中数
	FieldGoalsTotal     int     `json:"field_goals_total"`     // 投篮总数
	FieldGoalsAccuracy  string  `json:"field_goals_accuracy"`  // 投篮命中率
	Rebounds            int     `json:"rebounds"`              // 篮板数
	DefensiveRebounds   int     `json:"defensive_rebounds"`    // 防守篮板数
	OffensiveRebounds   int     `json:"offensive_rebounds"`    // 进攻篮板数
	Assists             int     `json:"assists"`               // 助攻
	Turnovers           int     `json:"turnovers"`             // 失误
	Steals              int     `json:"steals"`                // 抢断
	Blocks              int     `json:"blocks"`                // 盖帽
	PersonalFouls       int     `json:"personal_fouls"`        // 个人犯规
	Pace                float32 `json:"pace"`                  // 回合数(场均) (没有数据字段不存在)
	NetRating           float32 `json:"net_rating"`            //净效率(场均) (没有数据字段不存在)
	AssistRatio         float32 `json:"assist_ratio"`          //助攻比率(场均) (没有数据字段不存在)
	DoubleDoubles       int     `json:"double_doubles"`        //两双(总) (没有数据字段不存在)
	TripleDoubles       int     `json:"triple_doubles"`        //三双(总) (没有数据字段不存在)

	DefensiveRating  float32 `json:"defensive_rating"`  // 防守效率(场均) (没有数据字段不存在)
	OffensiveRating  float32 `json:"offensive_rating"`  // 进攻效率(场均) (没有数据字段不存在)
	UsagePercentage  float32 `json:"usage_percentage"`  // 回合占有率(%) (没有数据字段不存在)
	AssistPercentage float32 `json:"assist_percentage"` // 助攻率(%) (没有数据字段不存在)
	FastBreakPoints  float32 `json:"fast_break_points"` // 快攻得分(场均) (没有数据字段不存在)

	PointsInThePaint              float32 `json:"points_in_the_paint"`             //内线得分(场均) (没有数据字段不存在)
	PointsOffTurnovers            float32 `json:"points_off_turnovers"`            //利用失误得分(场均) (没有数据字段不存在)
	SecondChancePoints            float32 `json:"second_chance_points"`            //二次进攻得分(场均) (没有数据字段不存在)
	ReboundingPercentage          float32 `json:"rebounding_percentage"`           //篮板率(%) (没有数据字段不存在)
	PlayerImpactEstimate          float32 `json:"player_impact_estimate"`          //比赛贡献值(场均) (没有数据字段不存在)
	AssistToTurnoverRatio         float32 `json:"assist_to_turnover_ratio"`        //内线得分(场均) (没有数据字段不存在)
	TrueShootingPercentage        float32 `json:"true_shooting_percentage"`        //真实命中率(%) (没有数据字段不存在)
	EffectiveFieldGoalPercentage  float32 `json:"effective_field_goal_percentage"` //有效命中率(%) (没有数据字段不存在)
	DefensiveReboundingPercentage float32 `json:"defensive_rebounding_percentage"` //防守篮板率(%) (没有数据字段不存在)
	OffensiveReboundingPercentage float32 `json:"offensive_rebounding_percentage"` //进攻篮板率(%) (没有数据字段不存在)

}
type Teams struct {
	TeamId                        int     `json:"team_id"`                         // 球队ID
	Scope                         int     `json:"scope"`                           // 统计范围，1-赛季、2-预选赛、3-小组赛、4-季前赛、5-常规赛、6-淘汰赛(季后赛)、7-附加赛
	Matches                       int     `json:"matches"`                         // 比赛场次
	Points                        int     `json:"points"`                          // 得分
	PointsAgainst                 int     `json:"points_against"`                  // 失分
	FreeThrowsScored              int     `json:"free_throws_scored"`              // 罚球命中数
	FreeThrowsTotal               int     `json:"free_throws_total"`               // 罚球总数
	FreeThrowsAccuracy            string  `json:"free_throws_accuracy"`            // 罚球命中率
	TwoPointsScored               int     `json:"two_points_scored"`               // 两分球命中数
	TwoPointsTotal                int     `json:"two_points_total"`                // 两分球总数
	TwoPointsAccuracy             string  `json:"two_points_accuracy"`             // 两分球命中率
	ThreePointsScored             int     `json:"three_points_scored"`             // 三分球命中数
	ThreePointsTotal              int     `json:"three_points_total"`              // 三分球总数
	ThreePointsAccuracy           string  `json:"three_points_accuracy"`           // 三分球命中率
	FieldGoalsScored              int     `json:"field_goals_scored"`              // 投篮命中数
	FieldGoalsTotal               int     `json:"field_goals_total"`               // 投篮总数
	FieldGoalsAccuracy            string  `json:"field_goals_accuracy"`            // 投篮命中率
	TotalFouls                    int     `json:"total_fouls"`                     // 犯规
	Rebounds                      int     `json:"rebounds"`                        // 篮板数
	DefensiveRebounds             int     `json:"defensive_rebounds"`              // 防守篮板数
	OffensiveRebounds             int     `json:"offensive_rebounds"`              // 进攻篮板数
	Assists                       int     `json:"assists"`                         // 助攻
	Turnovers                     int     `json:"turnovers"`                       // 失误
	Steals                        int     `json:"steals"`                          // 抢断
	Blocks                        int     `json:"blocks"`                          // 盖帽
	NetRating                     float32 `json:"net_rating"`                      // 净效率(场均) (没有数据字段不存在)
	DefensiveRating               float32 `json:"defensive_rating"`                // 防守效率(场均) (没有数据字段不存在)
	OffensiveRating               float32 `json:"offensive_rating"`                // 进攻效率(场均) (没有数据字段不存在)
	AssistPercentage              float32 `json:"assist_percentage"`               // 助攻率(%) (没有数据字段不存在)
	FastBreakPoints               float32 `json:"fast_break_points"`               // 快攻得分(场均) (没有数据字段不存在)
	PointsInThePaint              float32 `json:"points_in_the_paint"`             // 内线得分(场均) (没有数据字段不存在)
	PointsOffTurnovers            float32 `json:"points_off_turnovers"`            // 利用失误得分(场均) (没有数据字段不存在)
	SecondChancePoints            float32 `json:"second_chance_points"`            // 二次进攻得分(场均) (没有数据字段不存在)
	ReboundingPercentage          float32 `json:"rebounding_percentage"`           // 篮板率(%) (没有数据字段不存在)
	DefensiveReboundingPercentage float32 `json:"defensive_rebounding_percentage"` // 防守篮板率(%) (没有数据字段不存在)
	OffensiveReboundingPercentage float32 `json:"offensive_rebounding_percentage"` // 进攻篮板率(%) (没有数据字段不存在)
}
