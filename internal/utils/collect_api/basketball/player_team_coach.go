package basketball

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type TeamResult struct {
	collect_api.ResResultBase
	CompetitionId int `json:"competition_id"`
	CountryId     int `json:"country_id"`
	CoachId       int `json:"coach_id" dc:"教练id"`
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo     string `json:"logo"`
	National int    `json:"national" dc:"是否国家队"`
	//FoundationTime int    `json:"foundation_time" dc:"成立时间"`
	//Website        string `json:"website" dc:"球队官网"`
	VenueId int `json:"venue_id" dc:"场馆id"`
	//collect_api.ResResultMarket
	//TotalPlayers    int `json:"total_players" dc:"总球员数。-1表示没有数据"`
	//ForeignPlayers  int `json:"foreign_players" dc:"非本土球员数。-1表示没数据"`
	//NationalPlayers int `json:"national_players" dc:"国家队球员数。-1表示没数据"`
}

func GetTeamList(ctx context.Context, req collect_api.ReqList) (results []TeamResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamResult](ctx, Prefix, "/team/list", req)
}

type PlayerResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo          string `json:"logo"`
	CountryId     int    `json:"country_id"`
	NationalLogo  string `json:"national_logo" dc:"国家队球员logo, 判断球队是国家队时用"`
	Birthday      int    `json:"birthday"`
	Age           int    `json:"age"`
	Height        int    `json:"height" dc:"身高"`
	Weight        int    `json:"weight" dc:"体重"`
	ContractUntil string `json:"contract_until" dc:"合同截止时间"`
	Position      string `json:"position" dc:"擅长位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	//CoachId         int       `json:"coach_id" dc:"球员转为教练后的对应id"`
	Drafted         string `json:"drafted" dc:"选秀顺位"`
	LeagueCareerAge int    `json:"league_career_age" dc:"联盟球龄"`
	School          string `json:"school" dc:"毕业学校"`
	City            string `json:"city" dc:"城市"`
	Salary          int    `json:"salary" dc:"年薪$"`
	ShirtNumber     int    `json:"shirt_number"`
}

func GetPlayerList(ctx context.Context, req collect_api.ReqList) (results []PlayerResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[PlayerResult](ctx, Prefix, "/player/list", req)
}

type CoachResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Logo   string `json:"logo"`
	TeamId int    `json:"team_id" dc:"执教球队id"`
}

func GetCoachList(ctx context.Context, req collect_api.ReqList) (results []CoachResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CoachResult](ctx, Prefix, "/coach/list", req)
}
