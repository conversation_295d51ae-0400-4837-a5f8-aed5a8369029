package basketball

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type MatchResult struct {
	Id             int      `json:"id"`               // 比赛id
	SeasonId       int      `json:"season_id"`        // 赛季id
	CompetitionId  int      `json:"competition_id"`   // 赛事id
	HomeTeamId     int      `json:"home_team_id"`     // 主队id
	AwayTeamId     int      `json:"away_team_id"`     // 客队id
	Kind           int      `json:"kind"`             // 类型id，1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无
	StatusId       int      `json:"status_id"`        // 比赛状态，详见状态码->比赛状态
	MatchTime      int64    `json:"match_time"`       // 比赛时间
	VenueId        int      `json:"venue_id"`         // 场馆id
	HomeScores     [5]int   `json:"home_scores"`      // 主队比分，分别是前4节和加时的比分
	AwayScores     [5]int   `json:"away_scores"`      // 客队比分，分别是前4节和加时的比分
	OverTimeScores [2][]int `json:"over_time_scores"` // 加时赛比分，i:0主队，1客队；j:第n节的比分
	UpdatedAt      int      `json:"updated_at"`
}

type MatchAnalysisResult struct {
	Info    InfoParam    `json:"info"`    // 比赛信息
	History HistoryParam `json:"history"` // 进球分布
}

type HistoryParam struct {
	Vs   []InfoParam `json:"vs"`   // 历史交锋/近期战绩
	Home []InfoParam `json:"home"` // 主队近期战绩
	Away []InfoParam `json:"away"` // 客队近期战绩
}
type InfoParam struct {
	Id            int    `json:"id"`             // 比赛id
	SeasonId      int    `json:"season_id"`      // 赛季id
	CompetitionId int    `json:"competition_id"` // 赛事id
	HomeTeamId    int    `json:"home_team_id"`   // 主队id
	AwayTeamId    int    `json:"away_team_id"`   // 客队id
	Kind          int    `json:"kind"`           // 类型id，1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无
	PeriodCount   int    `json:"period_count"`   // 比赛总节数(不包含加时)
	StatusId      int    `json:"status_id"`      //比赛状态，详见状态码->比赛状态
	MatchTime     int64  `json:"match_time"`     // 比赛时间
	Neutral       int    `json:"neutral"`        // 是否中立场，1-是、0-否
	HomeScores    []int  `json:"home_scores"`    // 比分字段说明
	AwayScores    []int  `json:"away_scores"`    // 比分字段说明
	HomePosition  string `json:"home_position"`  // 主队排名
	AwayPosition  string `json:"away_position"`  // 客队排名
}

type MatchLive struct {
	Id    int   `json:"id"` // 比赛id
	Score []any `json:"score"`
	Timer []any `json:"timer"`
}

func GetMatchList(ctx context.Context, req collect_api.ReqList) (results []MatchResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchResult](ctx, Prefix, "/match/list", req)
}
func GetMatchAnalysis(ctx context.Context, req collect_api.ReqList) (results MatchAnalysisResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetNotSlice[MatchAnalysisResult](ctx, Prefix, "/match/analysis", req)
}

func GetMatchLive(ctx context.Context, req collect_api.ReqList) (results []MatchLive, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MatchLive](ctx, Prefix, "/match/live", req)
}

type SquadResult struct {
	collect_api.ResResultBase
	Squad []Squad `json:"squad"`
}

type Squad struct {
	PlayerId    int    `json:"player_id" dc:"球员id"`
	Position    string `json:"position" dc:"球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	ShirtNumber string `json:"shirt_number" dc:"球衣号码"` // 其实还是int, 但纳米用了str
}

type VideoCollection struct {
	Type       int    `json:"type" dc:"类型，1-集锦、2-录像"`
	Title      string `json:"title" dc:"标题"`
	MobileLink string `json:"mobile_link" dc:"wap直播地址"`
	PcLink     string `json:"pc_link" dc:"web直播地址"`
	Cover      string `json:"cover" dc:"图片"`
	Duration   int    `json:"duration" dc:"时长-秒（s）"`
}

func GetSquadList(ctx context.Context, req collect_api.ReqList) (results []SquadResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[SquadResult](ctx, Prefix, "/team/squad/list", req)
}

func GetVideoCollectionList(ctx context.Context, req collect_api.ReqList) (results []VideoCollection, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[VideoCollection](ctx, Prefix, "/match/stream/video_collection", req)
}
