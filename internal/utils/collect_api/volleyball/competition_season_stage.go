package volleyball

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type CompetitionResult struct {
	collect_api.ResResultBase
	CategoryId int `json:"category_id"`
	CountryId  int `json:"country_id"`
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo           string           `json:"logo"`
	Type           int              `json:"type" dc:"赛事类型，0-未知、1-联赛、2-杯赛、3-友谊赛"`
	TitleHolder    []int            `json:"title_holder" dc:"卫冕冠军，index:0:球队id, 1:赛事冠军次数"`
	MostTitles     []any            `json:"most_titles" dc:"夺冠最多球队, 格式：[[10135],20], 分别是球队id, 赛事冠军次数"`
	NewComers      [][]int          `json:"new_comers" dc:"晋级淘汰球队, index 0:升级球队列表， 1:降级球队列表"`
	Divisions      [][]int          `json:"divisions" dc:"赛事层级， 0:高一级赛事id; 1:低一级赛事id"`
	Host           *CompetitionHost `json:"host" dc:"东道主"`
	PrimaryColor   string           `json:"primary_color" dc:"主颜色，可能不存在"`
	SecondaryColor string           `json:"secondary_color" dc:"主颜色，可能不存在"`
}

type CompetitionHost struct {
	Country string `json:"country"`
	City    string `json:"city"`
}

type CompetitionRuleResult struct {
	collect_api.ResResultBase
	CompetitionId int     `json:"competition_id"`
	SeasonIds     [][]int `json:"season_ids" dc:"赛季id列表"`
	Text          string  `json:"text" dc:"赛制说明"`
}

type SeasonResult struct {
	collect_api.ResResultBase
	CompetitionId  int    `json:"competition_id"`
	Year           string `json:"year"`
	StartTime      int    `json:"start_time"`
	EndTime        int    `json:"end_time"`
	IsCurrent      int    `json:"is_current" dc:"是否最新赛季 1是"`
	HasPlayerStats int    `json:"has_player_stats" dc:"是否有球员统计"`
	HasTeamStats   int    `json:"has_team_stats" dc:"是否有球队统计"`
	HasTable       int    `json:"has_table" dc:"是否有积分榜"`
}

type ScheduleDiaryResult struct {
	collect_api.ResResultBase
	Match []ScheduleDiaryMatch `json:"match"`
}

type ScheduleDiaryMatch struct {
	Id            int                `json:"id"`
	SeasonId      int                `json:"season_id"`
	CompetitionId int                `json:"competition_id"`
	Round         ScheduleDiaryRound `json:"round"`
	MatchTime     int                `json:"match_time"`
}

type ScheduleDiaryRound struct {
	StageId  int `json:"stage_id"`
	GroupNum int `json:"group_num"`
	RoundNum int `json:"round_num"`
}

// 阶段
type StageResult struct {
	collect_api.ResResultBase
	SeasonId int `json:"season_id" dc:"赛季id"`
	collect_api.ResResultName
	Mode       int `json:"mode" dc:"比赛模式，1-积分赛、2-淘汰赛"`
	GroupCount int `json:"group_count" dc:"总组数"`
	RoundCount int `json:"round_count" dc:"总轮数"`
	Order      int `json:"order" dc:"排序，阶段的先后顺序"`
}

type MultiLanguageResult struct {
	Id       int    `json:"id" dc:"对应的维度id (分类、国家、赛事、球队、球员)"`
	NameId   string `json:"name_id" dc:"印尼语"`
	NameVi   string `json:"name_vi" dc:"越南语"`
	UpdateAt int64  `json:"update_at" dc:"更新时间"`
}

func GetCompetitionList(ctx context.Context, req collect_api.ReqList) (results []CompetitionResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CompetitionResult](ctx, Prefix, "/competition/list", req)
}

func GetCompetitionRuleList(ctx context.Context, req collect_api.ReqList) (results []CompetitionRuleResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CompetitionRuleResult](ctx, Prefix, "/competition/rule/list", req)
}

func GetSeasonList(ctx context.Context, req collect_api.ReqList) (results []SeasonResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[SeasonResult](ctx, Prefix, "/season/list", req)
}

func GetMultiLanguageList(ctx context.Context, req collect_api.ReqList) (results []MultiLanguageResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[MultiLanguageResult](ctx, Prefix, "/language/list", req)
}

func GetScheduleDiaryList(ctx context.Context, req collect_api.ReqList) (results ScheduleDiaryResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetNotSlice[ScheduleDiaryResult](ctx, Prefix, "/match/schedule/diary", req)
}
