package tennis

import (
	"context"
	"gtcms/internal/utils/collect_api"
)

type TeamResult struct {
	collect_api.ResResultBase
	CompetitionId int `json:"competition_id"`
	CountryId     int `json:"country_id"`
	CoachId       int `json:"coach_id" dc:"教练id"`
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo           string `json:"logo"`
	National       int    `json:"national" dc:"是否国家队"`
	CountryLogo    string `json:"country_logo" dc:"国家队logo(国家队时存在)"`
	FoundationTime int    `json:"foundation_time" dc:"成立时间"`
	Website        string `json:"website" dc:"球队官网"`
	VenueId        int    `json:"venue_id" dc:"场馆id"`
	collect_api.ResResultMarket
	TotalPlayers    int `json:"total_players" dc:"总球员数。-1表示没有数据"`
	ForeignPlayers  int `json:"foreign_players" dc:"非本土球员数。-1表示没数据"`
	NationalPlayers int `json:"national_players" dc:"国家队球员数。-1表示没数据"`
}

func GetTeamList(ctx context.Context, req collect_api.ReqList) (results []TeamResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[TeamResult](ctx, Prefix, "/team/list", req)
}

type PlayerResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	collect_api.ResResultShortName
	Logo         string `json:"logo"`
	CountryId    int    `json:"country_id"`
	Nationality  string `json:"nationality" dc:"国籍"`
	NationalLogo string `json:"national_logo" dc:"国家队球员logo, 判断球队是国家队时用"`
	Birthday     int    `json:"birthday"`
	Age          int    `json:"age"`
	Height       int    `json:"height" dc:"身高"`
	Weight       int    `json:"weight" dc:"体重"`
	collect_api.ResResultMarket
	ContractUntil   int       `json:"contract_until" dc:"合同截止时间"`
	PreferredFoot   int       `json:"preferred_foot" dc:"惯用脚。0-未知、1-左脚、2-右脚、3-左右脚"`
	Suffix          string    `json:"suffix" dc:"球员u系列(??)"`
	CoachId         int       `json:"coach_id" dc:"球员转为教练后的对应id"`
	Uid             int       `json:"uid" dc:"重复球员合并后的对应球员id"`
	Ability         [][3]int  `json:"ability" dc:"能力评分。[类型，评分，平均分] 满分100. 能力评分字段说明：1-扑救 2-预判3-处理球 4-空中 5-战术 6-进攻 7-防守 8-创造力 9-技术。eg:[6,99,67]"`
	Characteristics [][][]int `json:"characteristics" dc:"技术特点说明, TODO:等拿到接口再研究"`
	Position        string    `json:"position" dc:"擅长位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	Positions       []any     `json:"positions" dc:"详细位置。eg:[\"RW\", [\"ST\"]] index 0:主要位置，1:次要位置列表。 详细位置字段说明（没有字段不存在'LW'-左边锋 'RW'-右边锋 'ST'-前锋 'AM'-攻击型中场 'ML'-左中场 'MC'-中路中场 'MR'-右中场 'DM'-防守型中场 'DL'-左后卫 'DC'-中后卫 'DR'-右后卫 'GK'-守门员" `
}

func GetPlayerList(ctx context.Context, req collect_api.ReqList) (results []PlayerResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[PlayerResult](ctx, Prefix, "/player/list", req)
}

type CoachResult struct {
	collect_api.ResResultBase
	collect_api.ResResultName
	Logo               string `json:"logo"`
	Birthday           int    `json:"birthday"`
	Age                int    `json:"age"`
	CountryId          int    `json:"country_id"`
	Nationality        string `json:"nationality" dc:"国籍"`
	PreferredFormation string `json:"preferred_formation" dc:"习惯阵形"`
	TeamId             int    `json:"team_id" dc:"执教球队id"`
	Joined             int    `json:"joined" dc:"加盟时间"`
	ContractUntil      int    `json:"contract_until" dc:"合同截止时间"`
}

func GetCoachList(ctx context.Context, req collect_api.ReqList) (results []CoachResult, q *collect_api.ResQuery, statusCode int, err error) {
	return collect_api.GetFullList[CoachResult](ctx, Prefix, "/coach/list", req)
}
