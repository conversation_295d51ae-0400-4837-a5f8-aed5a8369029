package resaveImage

import (
	"bytes"
	"context"
	"fmt"
	//"github.com/chai2010/webp"
	"github.com/nfnt/resize"
	"golang.org/x/image/bmp"
	"golang.org/x/image/tiff"
	_ "gtcms/internal/logic/file"
	"gtcms/internal/model"
	"gtcms/internal/service"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"
)

func ResaveImg(ctx context.Context, originUrl, prefix string, sportImg bool) (string, error) {
	var resp *http.Response
	var err error

	// **重试 3 次下载**
	for i := 0; i < 3; i++ {
		resp, err = http.DefaultClient.Get(originUrl)
		if err == nil {
			break
		}
		time.Sleep(time.Second)
	}
	if err != nil {
		return "", fmt.Errorf("failed to download image: %v", err)
	}
	defer resp.Body.Close()

	// **读取图片数据**
	b, err := io.ReadAll(resp.Body)
	if err != nil || len(b) == 0 {
		return "", fmt.Errorf("failed to read image data or image is empty")
	}

	rr := bytes.NewBuffer(b)

	// **检查图片类型**
	contentType := http.DetectContentType(b)
	var storageImage io.Reader

	if contentType == "image/gif" {
		storageImage = rr
	} else {
		compressedImg, err := compressImage(rr, contentType)
		if err != nil || compressedImg == nil {
			storageImage = rr
		} else {
			storageImage = compressedImg.Buf
		}
	}

	// **确保 storageImage 不为空**
	if storageImage == nil {
		return "", fmt.Errorf("image data is nil, cannot upload")
	}

	u, _ := url.Parse(originUrl)
	fileName := u.RequestURI()
	if strings.HasPrefix(fileName, "/") {
		fileName = fileName[1:]
	}

	if len(prefix) > 0 {
		if prefix[len(prefix)-1] != '/' {
			prefix += "/"
		}
		fileName = prefix + fileName
	}

	_, err = service.File().SingleUpload(ctx, model.SingleUploadInput{
		File:     storageImage,
		NameExt:  filepath.Ext(fileName),
		Name:     fileName,
		SportImg: 1,
	})

	return fileName, err
}

// **格式化文件名**
func formatFileName(originUrl, prefix string) string {
	u, _ := url.Parse(originUrl)
	fileName := u.RequestURI() // 获取 URL 路径
	if strings.HasPrefix(fileName, "/") {
		fileName = fileName[1:]
	}
	if len(prefix) > 0 {
		if !strings.HasSuffix(prefix, "/") {
			prefix += "/"
		}
		fileName = prefix + fileName
	}
	return fileName
}

// CompressedImage 结构体
type CompressedImage struct {
	Buf    *bytes.Buffer
	Width  int
	Height int
	Size   int64
}

// hasAlphaChannel 检测图片是否有透明通道
func hasAlphaChannel(img image.Image) bool {
	switch img.(type) {
	case *image.NRGBA, *image.RGBA, *image.NRGBA64, *image.RGBA64:
		return true
	default:
		return false
	}
}

// max 返回两个整数的较大值
func max(a, b uint) uint {
	if a > b {
		return a
	}
	return b
}

// min 返回两个整数的较小值
func min(a, b uint) uint {
	if a < b {
		return a
	}
	return b
}

// compressImage 压缩图片，并保持透明背景（如果格式支持）
func compressImage(imgst io.Reader, fileType string) (*CompressedImage, error) {
	var img image.Image
	var err error

	// **解析图片格式**
	switch fileType {
	case "image/png":
		img, err = png.Decode(imgst)
	case "image/jpeg", "image/jpg":
		img, err = jpeg.Decode(imgst)
	//case "image/webp":
	//	img, err = webp.Decode(imgst)
	case "image/bmp":
		img, err = bmp.Decode(imgst)
	case "image/tiff":
		img, err = tiff.Decode(imgst)
	default:
		return nil, fmt.Errorf("unsupported image format: %s", fileType)
	}

	if err != nil {
		return nil, err
	}

	bounds := img.Bounds()
	width, height := bounds.Dx(), bounds.Dy()

	// **确保宽高大于 0**
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("invalid image dimensions: width=%d, height=%d", width, height)
	}

	// **计算缩放比例**
	xratio := max(uint(width)/100, 1)
	yratio := max(uint(height)/100, 1)

	minratio := min(xratio, yratio)
	xvalue := max(uint(width)/minratio, 1)
	yvalue := max(uint(height)/minratio, 1)

	// **缩放图片**
	cimg := resize.Resize(xvalue, yvalue, img, resize.Bilinear)

	buffer := new(bytes.Buffer)

	// **根据原始格式选择编码方式**
	switch fileType {
	case "image/png":
		err = png.Encode(buffer, cimg) // PNG 保持透明
	//case "image/webp":
	//	err = webp.Encode(buffer, cimg, &webp.Options{Lossless: true}) // WebP 也支持透明
	default:
		// **JPEG 不支持透明，需要填充白色背景**
		if hasAlphaChannel(img) {
			rgba := image.NewRGBA(cimg.Bounds())
			draw.Draw(rgba, rgba.Bounds(), &image.Uniform{C: color.White}, image.Point{}, draw.Src)
			draw.Draw(rgba, rgba.Bounds(), cimg, image.Point{}, draw.Over)
			cimg = rgba
		}
		err = jpeg.Encode(buffer, cimg, &jpeg.Options{Quality: 80})
	}

	if err != nil {
		return nil, err
	}

	return &CompressedImage{
		Buf:    buffer,
		Width:  width,
		Height: height,
		Size:   int64(buffer.Len()),
	}, nil
}
