package task_helper

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"gtcms/internal/dao"
	"gtcms/internal/model/entity"
	"gtcms/internal/utils/collect_api"
	"gtcms/internal/utils/crawler"
	"time"
)

// 对于所有纳米的需要按id/time分页的接口都适用
type TaskRunnerPaged struct {
	//Task        *entity.CollectTask
	//CurrentInst *entity.CollectTaskInst
	TaskBase
	RunStatus *NamiFootballRunStatus // 初始化&加载数据时就要处理好
}

func (t *TaskRunnerPaged) UpdateRunStatus() (err error) {
	runStatus := new(NamiFootballRunStatus)
	err = gjson.Unmarshal([]byte(t.Task.RunStatus), runStatus)
	if err != nil {
		return err
	}
	t.RunStatus = runStatus
	if t.RunStatus.ByTime == 0 {
		t.RunStatus.Id++
	} else {
		if t.RunStatus.UpdateTime == 0 {
			t.RunStatus.UpdateTime = int(gtime.Now().Unix() - 86400*7) // 往前预一周
		}
		// updateTime 不需要递增，因为这个时间上的数据有可能还没读完，但id是唯一的
		//t.RunStatus.UpdateTime++
	}
	return nil
}

func (t *TaskRunnerPaged) Load(ctx context.Context, id int) (err error) {
	err = t.LoadTask(ctx, id)
	if err != nil {
		return err
	}
	t.UpdateRunStatus()

	g.Log().Line().Debug(ctx, "Load-Action:", t.Action)
	switch t.Action {
	case "run":
	case "new": // 先新建inst, 再执行
		nowts := time.Now().Unix()
		inst := &entity.CollectTaskInst{
			TaskId:     t.Task.Id,
			Symbol:     t.Task.Symbol,
			InstSymbol: t.makeInstSymbol(),
			RetryTimes: 0,
			Status:     1,
			CreateTime: nowts,
			UpdateTime: nowts,
			RunStatus:  t.Task.RunStatus,
		}
		t.InsertNewTaskInst(ctx, inst)
	case "retry":
		t.Retry(ctx)
	}
	return nil
}

// 执行了 save 操作后，对 Task & taskInst 的状态维护
func (t *TaskRunnerPaged) AfterSave(ctx context.Context, q *collect_api.ResQuery) error {
	dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
		"status":      2,
		"update_time": gtime.Now().UnixMilli(),
	})
	if q.Total > 0 { // 按id后面还有: 只更新id
		if t.RunStatus.ByTime == 1 {
			if q.MaxTime > 0 {
				t.RunStatus.UpdateTime = q.MaxTime
			}
		} else {
			if q.MaxId > 0 {
				t.RunStatus.Id = q.MaxId
			}
		}
		b, _ := json.Marshal(&t.RunStatus)
		dao.CollectTask.Ctx(ctx).Where("id = ?", t.CurrentInst.TaskId).Update(g.Map{
			"run_status": string(b),
		})
	} else { // 按id已经更新到最后了， 下次切换为update模式
		if t.RunStatus.ByTime == 1 {
			if q.MaxTime > 0 {
				t.RunStatus.UpdateTime = q.MaxTime
			}
		} else {
			if q.MaxId > 0 {
				t.RunStatus.Id = q.MaxId
			}
			t.RunStatus.ByTime = 1
		}
		b, _ := json.Marshal(&t.RunStatus)
		dao.CollectTask.Ctx(ctx).Where("id = ?", t.CurrentInst.TaskId).Update(g.Map{
			"run_status": string(b),
		})
	}
	return nil
}

func (t *TaskRunnerPaged) makeInstSymbol() string {
	if t.RunStatus.ByTime == 0 {
		return t.Task.Symbol + "_id_" + gconv.String(t.RunStatus.Id)
	} else {
		return t.Task.Symbol + "_time_" + gconv.String(t.RunStatus.UpdateTime)
	}
}

// 对于所有纳米的不分页的接口都生效
type TaskRunnerFull struct {
	//Task        *entity.CollectTask
	//CurrentInst *entity.CollectTaskInst
	TaskBase
	RunStatus *NamiFootballSimpleRunStatus // 初始化&加载数据时就要处理好
	Category  string
}

func (t *TaskRunnerFull) UpdateRunStatus() (err error) {
	runStatus := new(NamiFootballSimpleRunStatus)
	err = gjson.Unmarshal([]byte(t.Task.RunStatus), runStatus)
	if err != nil {
		return err
	}
	t.RunStatus = runStatus
	t.RunStatus.UpdatedAt++
	return nil
}

func (t *TaskRunnerFull) Load(ctx context.Context, id int) (err error) {
	err = t.LoadTask(ctx, id)
	t.UpdateRunStatus()
	g.Log().Line().Debug(ctx, "Load-Action:", t.Action)
	switch t.Action {
	case "run":
		//t.CurrentInst = CurrentInst
		//return t.DoCollect(ctx) // 直接执行
	case "new": // 先新建inst, 再执行
		nowts := time.Now().Unix()
		inst := &entity.CollectTaskInst{
			TaskId:     t.Task.Id,
			Symbol:     t.Task.Symbol,
			InstSymbol: t.makeInstSymbol(),
			RetryTimes: 0,
			Status:     1,
			CreateTime: nowts,
			UpdateTime: nowts,
			RunStatus:  t.Task.RunStatus,
		}
		t.InsertNewTaskInst(ctx, inst)
	case "retry":
		t.Retry(ctx)
	}
	return nil
}

func (t *TaskRunnerFull) makeInstSymbol() string {
	return t.Task.Symbol + "_" + gconv.String(t.RunStatus.UpdatedAt)
}

func (t *TaskRunnerFull) AfterSave(ctx context.Context) error {

	now := gtime.Now()
	nowts, nowms := now.Unix(), now.UnixMilli()
	dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
		"status":      2,
		"update_time": nowms,
	})
	t.RunStatus.UpdatedAt = nowts
	b, _ := json.Marshal(&t.RunStatus)
	dao.CollectTask.Ctx(ctx).Where("id = ?", t.CurrentInst.TaskId).Update(g.Map{
		"run_status":  string(b),
		"update_time": nowms,
	})
	return nil

}

// 对于所有纳米的by id+time分页的接口都生效
// 解析方式： 先解析bytime, 判断现在是在按time还是按id更新的阶段。
// 先是按id更新， 更新到“成功但空”之后，转成bytime模式，这时要更新bytime字段和updateTime字段。
// 然后是按bytime更新，
type NamiFootballRunStatus struct {
	ByTime     int `json:"by_time"`     // 球员是否可以按更新时间来更新。0:false, 表示还处在按id的全量更新阶段。 1:true,表示已经在按时间更新了
	UpdateTime int `json:"update_time"` // 球员，按更新时间 从0开始
	Id         int `json:"id"`          // 球员，按id查询 从0开始
}

type NamiFootballSimpleRunStatus struct {
	UpdatedAt int64 `json:"updated_at"`
}

type CommonPageStatus struct {
	ByTime     int `json:"by_time"`     // 按更新时间/页码来查询 0: 按页码， 1:按更新时间只爬第1页
	UpdateTime int `json:"update_time"` // 上次更新时间
	Page       int `json:"page"`        // 上次爬的页码
}

// 对于所有纳米的需要按id/time分页的接口都适用
type TaskRunnerCommonPage struct {
	TaskBase
	RunStatus *CommonPageStatus // 初始化&加载数据时就要处理好
}

func (t *TaskRunnerCommonPage) UpdateRunStatus() (err error) {
	runStatus := new(CommonPageStatus)
	err = gjson.Unmarshal([]byte(t.Task.RunStatus), runStatus)
	if err != nil {
		return err
	}
	t.RunStatus = runStatus
	if t.RunStatus.ByTime == 0 {
		t.RunStatus.Page--
		if t.RunStatus.Page <= 0 {
			t.RunStatus.Page = 1
		}
	} else { // ByTime=1, 按time来
		if t.RunStatus.UpdateTime == 0 {
			t.RunStatus.UpdateTime = int(gtime.Now().Unix() - 86400*7) // 往前预一周
		}
		t.RunStatus.Page = 1
	}
	return nil
}

func (t *TaskRunnerCommonPage) Load(ctx context.Context, id int) (err error) {
	err = t.LoadTask(ctx, id)
	if err != nil {
		return err
	}
	err = t.UpdateRunStatus()
	if err != nil {
		return err
	}

	g.Log().Line().Debug(ctx, "Load-Action:", t.Action)
	switch t.Action {
	case "run":
	case "new": // 先新建inst, 再执行
		nowms := time.Now().UnixMilli()
		inst := &entity.CollectTaskInst{
			TaskId:     t.Task.Id,
			Symbol:     t.Task.Symbol,
			InstSymbol: t.makeInstSymbol(),
			RetryTimes: 0,
			Status:     1,
			CreateTime: nowms,
			UpdateTime: nowms,
			RunStatus:  t.Task.RunStatus,
		}
		t.InsertNewTaskInst(ctx, inst)
	case "retry":
		t.Retry(ctx)
	}
	return nil
}

// 执行了 save 操作后，对 Task & taskInst 的状态维护
func (t *TaskRunnerCommonPage) AfterSave(ctx context.Context, q crawler.SinaResult) error {
	b, _ := json.Marshal(t.RunStatus)
	_, err := dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
		"status":      2,
		"update_time": gtime.Now().UnixMilli(),
		"run_status":  string(b),
	})
	if err != nil {
		return err
	}
	// 先按预给的页码来爬，到1之后转为增量模式. 增量模式下，每次只爬第1页
	//if t.RunStatus.ByTime == 1 {
	//	t.RunStatus.UpdateTime = int(time.Now().Unix())
	//} else {
	//	t.RunStatus.Page--
	//}
	//if t.RunStatus.Page <= 0 {
	//	t.RunStatus.Page = 1
	//}
	_, err = dao.CollectTask.Ctx(ctx).Where("id = ?", t.Task.Id).Update(g.Map{
		"run_status": string(b),
	})
	if err != nil {
		return err
	}
	return nil
}

func (t *TaskRunnerCommonPage) makeInstSymbol() string {
	if t.RunStatus.ByTime == 0 {
		return t.Task.Symbol + "_page_" + gconv.String(t.RunStatus.Page)
	} else {
		return t.Task.Symbol + "_time_" + gconv.String(t.RunStatus.UpdateTime)
	}
}

type TaskBase struct {
	Task        *entity.CollectTask
	CurrentInst *entity.CollectTaskInst
	Action      string
}

func (t *TaskBase) LoadTask(ctx context.Context, id int) (err error) {
	t.Task = new(entity.CollectTask)
	err = dao.CollectTask.Ctx(ctx).Where("id = ?", id).Scan(t.Task)
	if err != nil {
		return err
	}
	currentInst := new(entity.CollectTaskInst)
	err = dao.CollectTaskInst.Ctx(ctx).Where("task_id = ?", t.Task.Id).Where("id = ?", t.Task.CurrentInstId).Scan(currentInst)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return err
	}
	action := "unknown"
	if currentInst.Symbol != t.Task.Symbol { // 错误的inst
		dao.CollectTask.Ctx(ctx).Where("id = ?", id).Update(g.Map{"current_inst_id": 0})
		action = "new"
		t.CurrentInst = new(entity.CollectTaskInst) // 置空，又不能为nil
	}
	t.CurrentInst = currentInst
	if t.Task.Status != 1 {
		return errors.New("Task status != 1")
	}
	if currentInst.Id == 0 { // 创建新任务。
		action = "new"
	} else {
		switch currentInst.Status {
		case 1:
			action = "run"
		case 2:
			action = "new"
		case 3:
			action = "retry"
			// TODO 执行失败：retry+1, 修改状态，再判断后续
		default:
			// TODO: 正常不应该 走到这里
		}
	}
	t.Action = action
	return nil
}

func (t *TaskBase) Retry(ctx context.Context) (err error) {
	nowts := gtime.Now().Unix()
	t.CurrentInst.RetryTimes++
	if t.CurrentInst.RetryTimes >= t.Task.MaxRetry { // 不再重试，直接失败
		t.Task.Status = 2
		_, err := dao.CollectTask.Ctx(ctx).Where("id = ?", t.Task.Id).Update(g.Map{
			"status": 2,
		})
		if err != nil {
			return err
		}
		_, err = dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
			"retry_times": t.CurrentInst.RetryTimes,
			"status":      3,
			"update_time": nowts,
		})
		if err != nil {
			return err
		}
		t.CurrentInst.Status = 3
	} else { // 重试一次
		_, err = dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
			"retry_times": t.CurrentInst.RetryTimes,
			"status":      1,
			"update_time": nowts,
		})
		if err != nil {
			return err
		}
		t.CurrentInst.Status = 1
	}
	return nil
}

func (t *TaskBase) InsertNewTaskInst(ctx context.Context, inst *entity.CollectTaskInst) (err error) {
	var insertedId int64
	insertedId, err = dao.CollectTaskInst.Ctx(ctx).InsertAndGetId(inst)
	if err != nil {
		return err
	}
	t.Task.CurrentInstId = uint(insertedId)
	_, err = dao.CollectTask.Ctx(ctx).Where("id = ?", t.Task.Id).Update(g.Map{
		"current_inst_id": insertedId,
	})
	if err != nil {
		return err
	}
	inst.Id = uint(insertedId)
	t.CurrentInst = inst
	return nil
}
func (t *TaskBase) OnError(ctx context.Context, err error, statusCode int) error {
	t.CurrentInst.Status = 3
	dao.CollectTaskInst.Ctx(ctx).Where("id = ?", t.CurrentInst.Id).Update(g.Map{
		"status": 3,
	})
	return errors.New("statusCode:" + gconv.String(statusCode))
}
