package risk

import (
	"gtcms/internal/consts"
)

type BlackModifyLog struct {
	BlackType  string `json:"blackType"     description:"分类[1 IP, 2 设备, 3 手机号, 4 银行卡,  5 邮箱, 6 虚拟地址]"`
	BlackValue string `json:"blackValue"    description:"值"`
	ModifyType int    `json:"modifyType"    description:"类型[0 无, 1 后台添加 2 后台删除 3 玩家删除]"`
	Accounts   string `json:"accounts"      description:"用户账号信息"`
	Remark     string `json:"remark"        description:"备注"`
	Option     string `json:"option"        description:"选项值： IP,设备选项:封号[1是 2否]   手机选项:区号  银行卡选项: bankId 虚拟地址选项：币种id"`
}

type WhiteModifyLog struct {
	WhiteType  string `json:"whiteType"     description:"分类[1 IP, 2 设备]"`
	WhiteValue string `json:"whiteValue"    description:"值"`
	Accounts   string `json:"accounts"      description:"用户账号信息"`
	Remark     string `json:"remark"        description:"备注"`
}

var MapTypeName = map[consts.RiskType]string{
	consts.RiskTypeIP:       "IP",
	consts.RiskTypeDevice:   "Device",
	consts.RiskTypePhone:    "Phone",
	consts.RiskTypeBankCard: "Bank Card",
	consts.RiskTypeEmail:    "Email",
	consts.RiskTypeAddress:  "Virtual Address",
}

func ConvertOpen(isOpen int) string {
	if isOpen == 1 {
		return "开"
	}

	return "关"
}
