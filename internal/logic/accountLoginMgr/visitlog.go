package accountLoginMgr

//func (s *sAccountLoginMgr) VisitLogList(ctx context.Context, startTime int64, endTime int64, whereList *v1.ListReq, whereSearch *v1.VisitLogSearch) (visitLogs []entity.VisitLog, listRes v1.ListRes, err error) {
//	visitLogs = make([]entity.VisitLog, 0)
//	listRes = v1.ListRes{
//		Current: whereList.Current,
//	}
//	var tableName = dao.VisitLog.Table()
//	var cls = dao.VisitLog.Columns()
//
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//	for i := 0; i < len(models); i++ {
//		models[i] = models[i].WhereGTE(cls.VisitTime, startTime).WhereLTE(cls.VisitTime, endTime).OmitEmptyWhere().OmitNilWhere()
//		//if whereSearch.UserAccount != nil {
//		//	models[i] = models[i].Where("user_account=?", whereSearch.UserAccount)
//		//}
//		if whereSearch.LeaveTimeStart != nil {
//			models[i] = models[i].WhereGTE(cls.LeaveTime, whereSearch.LeaveTimeStart)
//		}
//		if whereSearch.LeaveTimeEnd != nil {
//			models[i] = models[i].WhereLTE(cls.LeaveTime, whereSearch.LeaveTimeEnd)
//		}
//		if whereSearch.VisitDurationStart != nil {
//			models[i] = models[i].WhereGTE(cls.VisitDuration, whereSearch.VisitDurationStart)
//		}
//		if whereSearch.VisitDurationEnd != nil {
//			models[i] = models[i].WhereLTE(cls.VisitDuration, whereSearch.VisitDurationEnd)
//		}
//		//if whereSearch.AppType != nil {
//		//	models[i] = models[i].Where("app_type=?", whereSearch.AppType)
//		//}
//		models[i] = models[i].Where(cls.UserAccount, whereSearch.UserAccount)
//		models[i] = models[i].Where(cls.AppType, whereSearch.AppType)
//	}
//
//	listRes.Total, err = g.DB().UnionAll(
//		models...,
//	).Count()
//	if err != nil {
//		return
//	}
//
//	err = g.DB().UnionAll(
//		models...,
//	).OrderDesc(cls.VisitTime).OrderDesc(cls.Id).
//		Page(whereList.Current, whereList.PageSize).
//		Scan(&visitLogs)
//	return
//}
//
//func (s *sAccountLoginMgr) VisitLogGroupByUrlAppType(ctx context.Context, startTime int64, endTime int64) (logGroups []v1.VisitLogGroupByUrlAppType, err error) {
//	var tableName = dao.VisitLog.Table()
//	var cls = dao.VisitLog.Columns()
//	logGroups = make([]v1.VisitLogGroupByUrlAppType, 0)
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//	for i := 0; i < len(models); i++ {
//		// models[i] = models[i].Fields("*,count(1) as n").Where("visit_time>=?", startTime).Where("visit_time<=?", endTime).Group("visit_url,app_type")
//		// goframe框架有点问题，会去SHOW FULL COLUMNS FROM `app_type`
//		models[i] = models[i].Fields(fmt.Sprintf("%s,%s,count(1) as n", cls.VisitUrl, cls.AppType)).
//			WhereGTE(cls.VisitTime, startTime).WhereLTE(cls.VisitTime, endTime).Group(cls.VisitUrl, cls.AppType)
//	}
//	err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,%s,sum(n) as count", cls.VisitUrl, cls.AppType)).Group(cls.VisitUrl, cls.AppType).Scan(&logGroups)
//	return
//}
//
//func (s *sAccountLoginMgr) VisitLogGroupByUrlVipLevel(ctx context.Context, startTime int64, endTime int64) (logGroups []v1.VisitLogGroupByUrlVipLevel, err error) {
//	var tableName = dao.VisitLog.Table()
//	var cls = dao.VisitLog.Columns()
//	logGroups = make([]v1.VisitLogGroupByUrlVipLevel, 0)
//	models := service.TableDivision().GetModels(ctx, tableName, startTime, endTime, true)
//	for i := 0; i < len(models); i++ {
//		models[i] = models[i].Fields(fmt.Sprintf("%s,%s,count(1) as n", cls.VisitUrl, cls.VipLevel)).
//			WhereGTE(cls.VisitTime, startTime).WhereLTE(cls.VisitTime, endTime).Group(cls.VisitUrl, cls.VipLevel)
//	}
//	err = g.Model("? as tem", g.DB().UnionAll(
//		models...,
//	)).Fields(fmt.Sprintf("%s,%s,sum(n) as count", cls.VisitUrl, cls.VipLevel)).Group(cls.VisitUrl, cls.VipLevel).Scan(&logGroups)
//	return
//}
