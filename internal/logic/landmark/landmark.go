package landmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
)

type sLandmark struct{}

func init() {
	service.RegisterLandmark(New())
}

func New() *sLandmark {
	return &sLandmark{}
}

// ==================== 地标类型相关接口实现 ====================

func (s *sLandmark) TypeList(ctx context.Context, req *v1.LandmarkTypeListReq) (out *v1.LandmarkTypeListRes, err error) {
	out = new(v1.LandmarkTypeListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var types []entity.HajiLandmarkType
	query := dao.HajiLandmarkType.Ctx(ctx).OrderAsc(dao.HajiLandmarkType.Columns().CreateTime)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&types)
	if err != nil {
		return nil, err
	}

	// 总数
	out.Total, err = dao.HajiLandmarkType.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 先提取id
	typeIds := gutil.ListItemValuesUnique(types, "Id")

	var typeLanguages []entity.HajiLandmarkTypeLanguages
	if len(typeIds) > 0 {
		err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			WhereIn(dao.HajiLandmarkTypeLanguages.Columns().TypeId, typeIds).
			Scan(&typeLanguages)
		if err != nil {
			return nil, err
		}
	}

	typeLanguageMap := make(map[uint64][]entity.HajiLandmarkTypeLanguages)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = append(typeLanguageMap[lang.TypeId], lang)
	}

	// 构建返回数据
	for _, typeItem := range types {
		item := &v1.LandmarkTypeListItem{
			Id:          typeItem.Id,
			IconUrl:     typeItem.IconUrl,
			CreateTime:  typeItem.CreateTime,
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		// 设置当前语言的内容
		for _, lang := range typeLanguageMap[typeItem.Id] {
			if int(lang.LanguageId) == languageId {
				item.TypeName = lang.TypeName
				break
			}
		}

		// 构建语言支持列表
		s.buildTypeLanguageArr(item, typeLanguageMap[typeItem.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// buildTypeLanguageArr 构建地标类型语言支持列表
func (s *sLandmark) buildTypeLanguageArr(item *v1.LandmarkTypeListItem, languages []entity.HajiLandmarkTypeLanguages) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, lang := range languages {
		languageSupport[int(lang.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sLandmark) TypeAdd(ctx context.Context, req *v1.LandmarkTypeCreateReq) (out *v1.LandmarkTypeCreateRes, err error) {
	out = new(v1.LandmarkTypeCreateRes)

	// 检查类型名称是否重复
	for _, item := range req.Content {
		count, err := dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	var typeId int64
	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		typeId, err1 = tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmarkType{IconUrl: req.IconUrl}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var languages []do.HajiLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkTypeLanguages{
				TypeId:     typeId,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = typeId
	return out, nil
}

func (s *sLandmark) TypeEdit(ctx context.Context, req *v1.LandmarkTypeEditReq) (out *v1.LandmarkTypeEditRes, err error) {
	out = new(v1.LandmarkTypeEditRes)

	// 检查类型是否存在
	var typeItem entity.HajiLandmarkType
	err = dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查类型名称是否重复（排除自己）
	for _, item := range req.Content {
		count, err := dao.HajiLandmarkTypeLanguages.Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, item.LanguageType).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeName, item.TypeName).
			WhereNot(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Count()
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, gerror.New("该类型名称已存在")
		}
	}

	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiLandmarkType{
				IconUrl: req.IconUrl,
			}).
			Where(dao.HajiLandmarkType.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var languages []do.HajiLandmarkTypeLanguages
		for _, item := range req.Content {
			languages = append(languages, do.HajiLandmarkTypeLanguages{
				TypeId:     req.Id,
				LanguageId: item.LanguageType,
				TypeName:   item.TypeName,
			})
		}

		if len(languages) > 0 {
			_, err1 = tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(languages).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sLandmark) TypeOne(ctx context.Context, req *v1.LandmarkTypeOneReq) (res *v1.LandmarkTypeOneRes, err error) {
	var typeItem entity.HajiLandmarkType
	err = dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Scan(&typeItem)
	if err != nil {
		return nil, err
	}
	if typeItem.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "地标类型不存在")
	}

	// 获取多语言内容
	var languages []entity.HajiLandmarkTypeLanguages
	err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
		Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
		Scan(&languages)
	if err != nil {
		return nil, err
	}

	// 构建返回数据
	item := &v1.LandmarkTypeListItem{
		Id:          typeItem.Id,
		IconUrl:     typeItem.IconUrl,
		CreateTime:  typeItem.CreateTime,
		LanguageArr: make([]v1.LanguageArrItem, 0),
	}

	// 获取当前语言的内容
	languageId := gconv.Int(ctx.Value(consts.LanguageId))
	for _, lang := range languages {
		if int(lang.LanguageId) == languageId {
			item.TypeName = lang.TypeName
			break
		}
	}

	// 构建语言支持列表
	s.buildTypeLanguageArr(item, languages)

	res = &v1.LandmarkTypeOneRes{
		LandmarkTypeListItem: *item,
	}
	return res, nil
}

func (s *sLandmark) TypeDelete(ctx context.Context, req *v1.LandmarkTypeDeleteReq) (out *v1.LandmarkTypeDeleteRes, err error) {
	out = new(v1.LandmarkTypeDeleteRes)

	// 检查类型是否存在
	count, err := dao.HajiLandmarkType.Ctx(ctx).
		Where(dao.HajiLandmarkType.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("地标类型不存在")
	}

	// 检查该类型下是否有关联的地标
	landmarkCount, err := dao.HajiLandmark.Ctx(ctx).
		Where(dao.HajiLandmark.Columns().TypeId, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if landmarkCount > 0 {
		return nil, gerror.New("该地点类型下有关联内容，不允许删除！")
	}

	err = dao.HajiLandmarkType.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.HajiLandmarkTypeLanguages.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkTypeLanguages.Columns().TypeId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除主表记录
		_, err1 = tx.Model(dao.HajiLandmarkType.Table()).Ctx(ctx).
			Where(dao.HajiLandmarkType.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sLandmark) TypeOptions(ctx context.Context, req *v1.LandmarkTypeOptionsReq) (out *v1.LandmarkTypeOptionsRes, err error) {
	out = new(v1.LandmarkTypeOptionsRes)
	out.Options = make([]v1.LandmarkTypeOption, 0)

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var types []entity.HajiLandmarkType
	err = dao.HajiLandmarkType.Ctx(ctx).
		OrderAsc(dao.HajiLandmarkType.Columns().CreateTime).
		Scan(&types)
	if err != nil {
		return nil, err
	}

	if len(types) == 0 {
		return out, nil
	}

	// 先提取id
	typeIds := gutil.ListItemValuesUnique(types, "Id")

	var typeLanguages []entity.HajiLandmarkTypeLanguages
	err = dao.HajiLandmarkTypeLanguages.Ctx(ctx).
		WhereIn(dao.HajiLandmarkTypeLanguages.Columns().TypeId, typeIds).
		Where(dao.HajiLandmarkTypeLanguages.Columns().LanguageId, languageId).
		Scan(&typeLanguages)
	if err != nil {
		return nil, err
	}

	typeLanguageMap := make(map[uint64]string)
	for _, lang := range typeLanguages {
		typeLanguageMap[lang.TypeId] = lang.TypeName
	}

	// 构建选项列表
	for _, typeItem := range types {
		typeName := typeLanguageMap[typeItem.Id]
		if typeName == "" {
			typeName = "未命名类型"
		}

		out.Options = append(out.Options, v1.LandmarkTypeOption{
			Value: int64(typeItem.Id),
			Label: typeName,
		})
	}

	return out, nil
}
