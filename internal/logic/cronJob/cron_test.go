package cronJob

import (
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"net/http"
	"testing"
)

func Test_sCronJob_Daemon(t *testing.T) {
	checkICP("bubent.com")
}

type IcpRecord struct {
	Domain   string `json:"domain"`
	RecordNo string `json:"recordNo"`
	Status   string `json:"status"`
}

func checkICP(domain string) {
	url := fmt.Sprintf("http://beian.miit.gov.cn/publish/query/indexFirst.action?siteDomain=%s", domain)
	resp, err := http.Get(url)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return
	}

	// 假设备案信息位于 <td> 标签内，根据页面结构调整选择器
	doc.Find("td").Each(func(i int, s *goquery.Selection) {
		fmt.Println(s.Text())
	})
}
