package cronJob

//type SimpleIntervalJob struct {
//	F        func(ctx context.Context)
//	JobName  string
//	Interval time.Duration
//}
//
//func (s *SimpleIntervalJob) Name() string {
//	return s.JobName
//}
//
//func (s *SimpleIntervalJob) GetCron() string {
//	return "@every " + gconv.String(int(s.Interval.Seconds())) + "s"
//}
//
//func (s *SimpleIntervalJob) Run() {
//	funcWrap(s.Name(), s.F)()
//}
//
//func (s *SimpleIntervalJob) Init(ctx context.Context) error {
//	return nil
//}
//
//func funcWrap(jobName string, f func(ctx context.Context)) (f2 func()) {
//	return func() {
//		ctx := gctx.New()
//		g.Log().Line().Infof(ctx, "job [%s] start", jobName)
//		f(ctx)
//		g.Log().Line().Infof(ctx, "job [%s] finish", jobName)
//	}
//}
