package libfile

import (
	"context"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gutil"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"gtcms/internal/consts"
	"gtcms/utility/mimedb"
	"io"
	"net/url"
	"path"
	"path/filepath"
	"time"
)

type MinioClient struct {
	client *minio.Client
	config MinioConfig
}

var _ S3Client = &MinioClient{}
var _ S3ClientPublicDir = &MinioClient{}

// MinioConfig minio的配置
type MinioConfig struct {
	Endpoint            string `json:"endpoint"`
	AccessKeyID         string `json:"accessKeyID"`
	SecretAccessKey     string `json:"secretAccessKey"`
	SessionToken        string `json:"sessionToken"`
	UseSSL              bool   `json:"useSSL"`
	BucketName          string `json:"bucketName"`
	GetObjectUrlExpires int    `json:"getObjectUrlExpires,omitempty"`
}

func NewMinioClient(ctx context.Context, config MinioConfig) (*MinioClient, error) {
	c, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, config.SessionToken),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, err
	}
	ins := &MinioClient{
		client: c,
		config: config,
	}
	return ins, nil
}

func (c *MinioClient) GetName(ctx context.Context) string {
	return consts.FileTypeS3Minio
}

// GetPublicObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *MinioClient) GetPublicObjectURL(ctx context.Context, objectName string) *url.URL {
	u := c.client.EndpointURL()
	u.Path = path.Join(c.config.BucketName, objectName)
	return u
}

// GetObjectAttributes 获取对象属性信息
func (c *MinioClient) GetObjectAttributes(ctx context.Context, objectName string) (out *ObjectAttributes, err error) {
	res, err := c.client.GetObjectAttributes(ctx, c.config.BucketName, objectName, minio.ObjectAttributesOptions{})
	if err != nil {
		return nil, err
	}
	if res == nil {
		err = gerror.NewCode(gcode.CodeInternalError)
		return
	}
	out = &ObjectAttributes{
		Key:        objectName,
		ObjectSize: int64(res.ObjectSize),
	}
	return out, nil
}

// PutObject 上传对象(小文件)
// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types
// https://www.iana.org/assignments/media-types/media-types.xhtml
func (c *MinioClient) PutObject(ctx context.Context, objectName string, reader io.Reader) error {
	_, err := c.client.PutObject(ctx, c.config.BucketName, objectName, reader, -1, minio.PutObjectOptions{
		ContentType: mimedb.TypeByExtension(filepath.Ext(objectName)),
	})
	if err != nil {
		return err
	}
	return nil
}

// CopyObject 复制对象
func (c *MinioClient) CopyObject(ctx context.Context, destObjectName string, srcObjectName string) error {
	_, err := c.client.CopyObject(ctx, minio.CopyDestOptions{
		Bucket: c.config.BucketName,
		Object: destObjectName,
	}, minio.CopySrcOptions{
		Bucket: c.config.BucketName,
		Object: srcObjectName,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObject 删除单个对象
func (c *MinioClient) RemoveObject(ctx context.Context, objectName string) error {
	err := c.client.RemoveObject(ctx, c.config.BucketName, objectName, minio.RemoveObjectOptions{
		GovernanceBypass: true,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObjects 批量删除对象
func (c *MinioClient) RemoveObjects(ctx context.Context, objectNames []string) error {
	opts := minio.RemoveObjectsOptions{
		GovernanceBypass: true,
	}
	objectsCh := make(chan minio.ObjectInfo)
	gutil.Go(ctx, func(ctx context.Context) {
		defer func() {
			close(objectsCh)
		}()
		for _, objectName := range objectNames {
			objectsCh <- minio.ObjectInfo{
				Key: objectName,
			}
		}
	}, nil)
	var removeErr error
	for removeRes := range c.client.RemoveObjects(ctx, c.config.BucketName, objectsCh, opts) {
		if removeRes.Err != nil {
			removeErr = errors.Join(removeErr, removeRes.Err, gerror.Newf("MinioClient.RemoveObjects, objectName:%s", removeRes.ObjectName))
		}
	}
	if removeErr != nil {
		return removeErr
	}
	return nil
}

// PresignedGetObject 获取对象的访问授权链接
func (c *MinioClient) PresignedGetObject(ctx context.Context, objectName string) (u *url.URL, err error) {
	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", fmt.Sprintf("attachment; filename=\"%s\"", objectName))
	expiresTime := c.config.GetObjectUrlExpires
	if expiresTime < 1 {
		expiresTime = 86400
	}
	u, err = c.client.PresignedGetObject(ctx, c.config.BucketName, objectName, time.Second*time.Duration(expiresTime), reqParams)
	if err != nil {
		return
	}
	return
}

func (c *MinioClient) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
	out, err := c.client.GetObject(ctx, c.config.BucketName, objectName, minio.GetObjectOptions{})
	if err != nil {
		return nil, err
	}
	if out == nil {
		return nil, errors.New("GetObjectNil")
	}
	return out, err
}
