package libfile

import (
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/smithy-go/ptr"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/consts"
	"gtcms/utility/mimedb"
	"io"
	"net/url"
	"path/filepath"
	"time"
)

type AwsS3Client struct {
	client        *s3.Client
	PresignClient *s3.PresignClient
	config        AwsS3Config
}

var _ S3Client = &AwsS3Client{}
var _ S3ClientPublicDir = &AwsS3Client{}

// AwsS3Config aws s3的配置
type AwsS3Config struct {
	Region              string `json:"region"`
	AccessKeyID         string `json:"accessKeyID"`
	SecretAccessKey     string `json:"secretAccessKey"`
	SessionToken        string `json:"sessionToken"`
	BucketName          string `json:"bucketName"`
	GetObjectUrlExpires int    `json:"getObjectUrlExpires,omitempty"`
}

func NewAwsS3Client(ctx context.Context, config AwsS3Config) (*AwsS3Client, error) {
	optFns := make([]func(*awsConfig.LoadOptions) error, 0)
	if len(config.Region) > 0 {
		optFns = append(optFns, awsConfig.WithRegion(config.Region))
	}
	if len(config.AccessKeyID) > 0 && len(config.SecretAccessKey) > 0 {
		optFns = append(optFns, awsConfig.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AccessKeyID, config.SecretAccessKey, config.SessionToken)))
	}
	sdkConfig, err := awsConfig.LoadDefaultConfig(ctx, optFns...)
	if err != nil {
		return nil, err
	}
	s3Client := s3.NewFromConfig(sdkConfig)
	client := &AwsS3Client{
		client:        s3Client,
		config:        config,
		PresignClient: s3.NewPresignClient(s3Client),
	}

	return client, nil
}

func (c *AwsS3Client) GetName(ctx context.Context) string {
	return consts.FileTypeS3Aws
}

// GetPublicObjectURL  获取对象的访问网址(只支持公开访问的)
func (c *AwsS3Client) GetPublicObjectURL(ctx context.Context, objectName string) *url.URL {
	var params = s3.EndpointParameters{
		Region:              ptr.String(c.config.Region),
		UseFIPS:             aws.Bool(false),
		UseDualStack:        aws.Bool(false),
		Accelerate:          aws.Bool(false),
		DisableAccessPoints: aws.Bool(false),
		Bucket:              &c.config.BucketName,
	}
	resolver := s3.NewDefaultEndpointResolverV2()
	res, err := resolver.ResolveEndpoint(context.Background(), params)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil
	}
	res.URI.Path = objectName
	return &res.URI
}

// GetObjectAttributes 获取对象属性信息
func (c *AwsS3Client) GetObjectAttributes(ctx context.Context, objectName string) (out *ObjectAttributes, err error) {
	res, err := c.client.GetObjectAttributes(ctx, &s3.GetObjectAttributesInput{
		Bucket: &c.config.BucketName,
		Key:    &objectName,
		ObjectAttributes: []types.ObjectAttributes{
			types.ObjectAttributesObjectSize,
		},
	})
	if err != nil {
		return nil, err
	}
	if res == nil {
		err = gerror.NewCode(gcode.CodeInternalError)
		return
	}
	out = &ObjectAttributes{
		Key:        objectName,
		ObjectSize: *res.ObjectSize,
	}
	return out, nil
}

// PutObject 上传对象(小文件)
func (c *AwsS3Client) PutObject(ctx context.Context, objectName string, reader io.Reader) error {
	_, err := c.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      &c.config.BucketName,
		Key:         &objectName,
		Body:        reader,
		ContentType: aws.String(mimedb.TypeByExtension(filepath.Ext(objectName))),
	})
	if err != nil {
		return err
	}
	return nil
}

func (c *AwsS3Client) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
	out, err := c.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: &c.config.BucketName,
		Key:    &objectName,
	})
	if err != nil {
		return nil, err
	}
	if out == nil {
		return nil, errors.New("GetObjectNil")
	}
	return out.Body, err
}

// CopyObject 复制对象
func (c *AwsS3Client) CopyObject(ctx context.Context, destObjectName string, srcObjectName string) error {
	_, err := c.client.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     &c.config.BucketName,
		CopySource: aws.String(fmt.Sprintf("%v/%v", c.config.BucketName, srcObjectName)),
		Key:        &destObjectName,
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObject 删除单个对象
func (c *AwsS3Client) RemoveObject(ctx context.Context, objectName string) error {
	_, err := c.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket:                    &c.config.BucketName,
		Key:                       &objectName,
		BypassGovernanceRetention: aws.Bool(true),
	})
	if err != nil {
		return err
	}
	return nil
}

// RemoveObjects 批量删除对象
func (c *AwsS3Client) RemoveObjects(ctx context.Context, objectNames []string) error {
	deletes := types.Delete{
		Objects: make([]types.ObjectIdentifier, 0, len(objectNames)),
	}
	for _, objectName := range objectNames {
		newObjectName := objectName
		deletes.Objects = append(deletes.Objects, types.ObjectIdentifier{Key: &newObjectName})
	}
	_, err := c.client.DeleteObjects(ctx, &s3.DeleteObjectsInput{
		Bucket:                    &c.config.BucketName,
		Delete:                    &deletes,
		BypassGovernanceRetention: aws.Bool(true),
	})
	if err != nil {
		return err
	}
	return nil
}

// PresignedGetObject 获取对象的访问授权链接
func (c *AwsS3Client) PresignedGetObject(ctx context.Context, objectName string) (u *url.URL, err error) {
	// responseContentDisposition := fmt.Sprintf("attachment; filename=\"%s\"", objectName)
	expiresTime := c.config.GetObjectUrlExpires
	if expiresTime < 1 {
		expiresTime = 86400
	}
	out, err := c.PresignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &c.config.BucketName,
		Key:    &objectName,
		// ResponseContentDisposition: &responseContentDisposition,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = time.Second * time.Duration(expiresTime)
		/*opts.ClientOptions = append(opts.ClientOptions, func(op *s3.Options) {
			op.BaseEndpoint = aws.String("https://tt.com/")
		})*/
	})
	if err != nil {
		return
	}
	u, err = url.Parse(out.URL)
	if err != nil {
		return
	}
	return
}
