package libfile

import (
	"context"
	"io"
	"net/url"
)

type S3Client interface {
	GetName(ctx context.Context) string
	GetObjectAttributes(ctx context.Context, objectName string) (out *ObjectAttributes, err error)
	PutObject(ctx context.Context, objectName string, reader io.Reader) error
	CopyObject(ctx context.Context, destObjectName string, srcObjectName string) error
	RemoveObject(ctx context.Context, objectName string) error
	RemoveObjects(ctx context.Context, objectNames []string) error
	PresignedGetObject(ctx context.Context, objectName string) (u *url.URL, err error)
	GetObject(ctx context.Context, objectName string) (io.ReadCloser, error)
}

type S3ClientPublicDir interface {
	GetPublicObjectURL(ctx context.Context, objectName string) *url.URL
}

type ObjectAttributes struct {
	Key        string
	ObjectSize int64
}
