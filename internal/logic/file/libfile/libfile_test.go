package libfile

import (
	"context"
	"fmt"
	"os"
	"path"
	"testing"
)

var awsS3Client *AwsS3Client
var minioClient *MinioClient

var c S3Client

func newAwsS3ClientTest() {
	config := AwsS3Config{
		Region:          "ap-southeast-1",
		AccessKeyID:     "********************",
		SecretAccessKey: "o7EqvoPaghCr/3kliZ3Job4cW/qZbVpwR9JybCNE",
		SessionToken:    "",
		BucketName:      "cms-test",
	}
	var err error
	awsS3Client, err = NewAwsS3Client(context.Background(), config)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func newMinioClientTest() {
	config := MinioConfig{
		Endpoint:        "192.168.10.31:9000",
		AccessKeyID:     "aNgEjJoWZEpWV1z3kh7s",
		SecretAccessKey: "mVIBFwZM8eGCtXka8KNHYpJOVlXcSbvcrShF71Vn",
		SessionToken:    "",
		UseSSL:          false,
		BucketName:      "cms-test",
	}
	var err error
	minioClient, err = NewMinioClient(context.Background(), config)
	if err != nil {
		fmt.Println(err)
		return
	}
}

func TestMain(m *testing.M) {
	newAwsS3ClientTest()
	newMinioClientTest()
	c = minioClient // TODO 测试哪个客户端就切换哪个
	m.Run()
}

func TestGetObjectURL(t *testing.T) {
	public, ok := c.(S3ClientPublicDir)
	if !ok {
		t.Error("can not support public url")
	}
	uri := public.GetPublicObjectURL(context.Background(), "pub/tt5.png")
	if uri == nil {
		t.Error("uri is nil")
		return
	}
	t.Log(uri.String())
}

func TestGetObjectAttributes(t *testing.T) {
	res, err := c.GetObjectAttributes(context.Background(), "pub/tt5.png")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(res)
}

func TestPutObject(t *testing.T) {
	wd, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	reader, err := os.Open(path.Join(wd, "test_99.png"))
	if err != nil {
		t.Error(err)
		return
	}
	err = c.PutObject(context.Background(), "pub/tt5.png", reader)
	if err != nil {
		t.Error(err)
		return
	}
}

func TestCopyObject(t *testing.T) {
	err := c.CopyObject(context.Background(), "test/tt2.svg", "test/tt.svg")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestRemoveObject(t *testing.T) {
	err := c.RemoveObject(context.Background(), "test/tt2.svg")
	if err != nil {
		t.Error(err)
		return
	}
}

func TestRRemoveObjects(t *testing.T) {
	err := c.RemoveObjects(context.Background(), []string{"test/tt4.svg", "test/tt5.svg"})
	if err != nil {
		t.Error(err)
		return
	}
}

func TestPresignedGetObject(t *testing.T) {
	u, err := c.PresignedGetObject(context.Background(), "test/tt.svg")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(u)
}
