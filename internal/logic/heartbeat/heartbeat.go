package heartbeat

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	"gtcms/internal/service"
	"os/exec"
)

type sHeartbeat struct {
	sites []siteCheck
}

type siteCheck struct {
	URL       string
	ErrorCode float64
}

func init() {
	service.RegisterHeartbeat(New())
}

func New() *sHeartbeat {
	s := &sHeartbeat{
		sites: []siteCheck{
			{"gtcms01.com/", 1000},
			{"gtcms02.com/", 2000},
			{"gtcms03.com/", 3000},
			{"gtcms04.com/", 4000},
			{"mmcms01.com/", 5000},
			{"idcms01.com/", 6000},
			{"ftycms01.com/", 7000},
		},
	}
	return s
}

// CheckSiteHeartbeat 检查网站心跳
// 在后台服务器的hosts，本地解析66good.com到前台服务器ip
// 这样只需要访问66good.com即可知道前台服务器的状态
func (s *sHeartbeat) CheckSiteHeartbeat(ctx context.Context) float64 {
	var errorCodes []float64

	for _, site := range s.sites {
		cmd := exec.CommandContext(ctx, "curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", site.URL)
		output, err := cmd.Output()
		if err != nil {
			g.Log().Line().Error(ctx, fmt.Sprintf("Failed to check site heartbeat: %s", site.URL), err)
			errorCodes = append(errorCodes, site.ErrorCode+gconv.Float64(gconv.String(output)))
			continue
		}
		code := gconv.String(output)
		if code != "200" {
			g.Log().Line().Error(ctx, fmt.Sprintf("Failed to check site heartbeat: %s", site.URL), code)
			errorCodes = append(errorCodes, site.ErrorCode+gconv.Float64(gconv.String(output)))
		}
	}

	if len(errorCodes) > 0 {
		// 合并错误码（例如：取最大值，或自定义规则）
		return errorCodes[0] // 或 return sum(errorCodes) 等
	}

	return 200
}
