package FileProcessor

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/xuri/excelize/v2"
	"gtcms/internal/service"
	"gtcms/utility"
	"io"
	"os"
	"reflect"
	"strconv"
	"strings"
)

// 文件导入导出处理类
type sFileProcessor struct {
}

func init() {
	service.RegisterFileProcessor(New())
}

func New() service.IFileProcessor {
	return &sFileProcessor{}
}

// 导入需要把数据放在第三行，第一行第二行是中文和英文字段含义
func (s *sFileProcessor) Import(ctx context.Context, file *ghttp.UploadFile, dest interface{}) (err error) {
	filePath, err := s.saveFile(file)
	if err != nil {
		return err
	}
	err = s.readExcel(filePath, dest)
	if err != nil {
		return err
	}
	return nil
}

func (s *sFileProcessor) saveFile(file *ghttp.UploadFile) (filePath string, err error) {
	fr, err := file.Open()
	if err != nil {
		return "", err
	}
	defer fr.Close()

	// 读取上传文件内容
	content, err := io.ReadAll(fr)
	if err != nil {
		return "", err
	}

	// 创建本地文件
	filePath = gfile.Join(gfile.Temp(), gtime.Now().Format("YmdHis")+"_"+file.Filename)
	localFile, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer localFile.Close()

	// 将上传文件内容写入本地文件
	_, err = localFile.Write(content)
	if err != nil {
		return "", err
	}
	return filePath, nil
}

// 从Excel文件中读取数据到结构体切片
func (s *sFileProcessor) readExcel(filePath string, targetSlice interface{}) error {
	// 读取Excel文件
	xlsx, err := excelize.OpenFile(filePath)
	if err != nil {
		return err
	}

	// 获取目标切片的类型
	sliceType := reflect.TypeOf(targetSlice)

	// 获取切片元素的类型
	elementType := sliceType.Elem().Elem()

	// 创建切片值
	sliceValue := reflect.ValueOf(targetSlice).Elem()

	// 获取工作表
	sheet := xlsx.GetSheetName(0)

	// 获取所有行
	rows, err := xlsx.GetRows(sheet)
	if err != nil {
		return err
	}

	// 遍历每一行
	for rowIndex, row := range rows {
		// 忽略标题行
		if rowIndex <= 1 {
			continue
		}

		// 创建结构体实例
		elementValue := reflect.New(elementType).Elem()

		// 遍历每一列
		for colIndex, cellValue := range row {
			// 获取字段名
			fieldName, err := s.getFieldName(xlsx, sheet, colIndex+1)
			if err != nil {
				return err
			}

			// 获取结构体字段
			field, found := elementType.FieldByNameFunc(func(f string) bool {
				return strings.ToLower(f) == strings.ToLower(fieldName)
			})

			// 如果字段存在，则使用反射设置字段值
			if found {
				fieldValue := elementValue.FieldByName(field.Name)
				switch field.Type.Kind() {
				case reflect.String:
					fieldValue.SetString(cellValue)
				case reflect.Uint, reflect.Uint64, reflect.Uint32, reflect.Uint16, reflect.Uint8:
					// 将字符串转为整数
					age, _ := strconv.Atoi(cellValue)
					fieldValue.SetUint(uint64(age))
				case reflect.Int, reflect.Int64, reflect.Int32, reflect.Int16, reflect.Int8:
					// 将字符串转为整数
					age, _ := strconv.Atoi(cellValue)
					fieldValue.SetInt(int64(age))
				case reflect.Float64:
					// 将字符串转为浮点数
					age, _ := strconv.ParseFloat(cellValue, 64)
					fieldValue.SetFloat(age)
				case reflect.Float32:
					// 将字符串转为浮点数
					age, _ := strconv.ParseFloat(cellValue, 32)
					fieldValue.SetFloat(age)
				case reflect.Bool:
					// 将字符串转为布尔值
					age, _ := strconv.ParseBool(cellValue)
					fieldValue.SetBool(age)
				case reflect.Map:
				default:
					return gerror.Newf("不支持的类型：%s", field.Type.Kind())
				}
			}
		}

		// 将结构体实例添加到切片中
		sliceValue = reflect.Append(sliceValue, elementValue)
	}

	// 将切片值写回到目标切片
	reflect.ValueOf(targetSlice).Elem().Set(sliceValue)
	return nil
}

// getFieldName 根据列索引获取字段名
func (s *sFileProcessor) getFieldName(xlsx *excelize.File, sheet string, colIndex int) (string, error) {
	// 获取列的字母表示，例如A、B、C...
	colLetter, err := excelize.ColumnNumberToName(colIndex)
	if err != nil {
		return "", err
	}

	// 获取字段名
	fieldName, err := xlsx.GetCellValue(sheet, colLetter+"2")
	if err != nil {
		return "", err
	}

	return fieldName, nil
}

// Export2Excel 导出为excel，前端即时下载
func (s *sFileProcessor) Export2Excel(ctx context.Context, data interface{}) (err error) {
	r := g.RequestFromCtx(ctx)
	// 获取结构体类型
	dataList := gconv.SliceAny(data)
	dataType := reflect.TypeOf(dataList[0])

	// 创建 Excel 文件
	file := excelize.NewFile()
	sheet, err := file.NewSheet("Sheet1")
	if err != nil {
		return fmt.Errorf("failed to create new sheet: %w", err)
	}

	// 添加表头
	headers := make([]string, dataType.NumField())
	for i := 0; i < dataType.NumField(); i++ {
		field := dataType.Field(i)
		headers[i] = field.Tag.Get("json")
		cellName, _ := excelize.CoordinatesToCellName(i+1, 2)
		err = file.SetCellValue("Sheet1", cellName, headers[i])
		if err != nil {
			return fmt.Errorf("failed to set cell value: %w", err)
		}
	}

	// 添加数据行
	for rowIndex, data := range dataList {
		//dataValue := reflect.ValueOf(data)
		dataValue := utility.GetRealValue(data)
		for colIndex := 0; colIndex < dataType.NumField(); colIndex++ {
			fieldValue := dataValue.Field(colIndex)
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+3)
			err = file.SetCellValue("Sheet1", cellName, fieldValue.Interface())
			if err != nil {
				return fmt.Errorf("failed to set cell value: %w", err)
			}
		}
	}

	// 将第一个工作表设置为活动工作表
	file.SetActiveSheet(sheet)

	// 保存文件
	fileName := s.createFileName(".xlsx")
	filePath := gfile.Join(gfile.Temp(), fileName)
	if err := file.SaveAs(filePath); err != nil {
		r.Response.WriteExit(err)
	}

	// 设置 HTTP 响应头
	r.Response.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	r.Response.Header().Set("Content-Type", "application/octet-stream")

	g.RequestFromCtx(ctx).Response.ServeFileDownload(filePath, fileName)

	defer func() {
		if err := gfile.Remove(filePath); err != nil {
			glog.Error(ctx, err)
		}
	}()

	return
}

// Export2Excel 导出为excel，前端即时下载
func (s *sFileProcessor) Export2ExcelMoreSheet(ctx context.Context, data interface{}, data2 interface{}) (err error) {
	r := g.RequestFromCtx(ctx)
	// 获取结构体类型
	dataList := gconv.SliceAny(data)
	dataType := reflect.TypeOf(dataList[0])

	// 创建 Excel 文件
	file := excelize.NewFile()
	sheet, err := file.NewSheet("Sheet1")
	if err != nil {
		return fmt.Errorf("failed to create new sheet: %w", err)
	}

	// 添加表头
	headers := make([]string, dataType.NumField())
	for i := 0; i < dataType.NumField(); i++ {
		field := dataType.Field(i)
		headers[i] = field.Tag.Get("json")
		cellName, _ := excelize.CoordinatesToCellName(i+1, 1)
		err = file.SetCellValue("Sheet1", cellName, headers[i])
		if err != nil {
			return fmt.Errorf("failed to set cell value: %w", err)
		}
	}

	// 添加数据行
	for rowIndex, data := range dataList {
		//dataValue := reflect.ValueOf(data)
		dataValue := utility.GetRealValue(data)
		for colIndex := 0; colIndex < dataType.NumField(); colIndex++ {
			fieldValue := dataValue.Field(colIndex)
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			err = file.SetCellValue("Sheet1", cellName, fieldValue.Interface())
			if err != nil {
				return fmt.Errorf("failed to set cell value: %w", err)
			}
		}
	}

	dataList2 := gconv.SliceAny(data2)
	if len(dataList2) > 0 {
		dataType2 := reflect.TypeOf(dataList2[0])

		_, _ = file.NewSheet("Sheet2")
		if err != nil {
			return fmt.Errorf("failed to create new sheet: %w", err)
		}

		// 添加表头
		headers2 := make([]string, dataType2.NumField())
		for i := 0; i < dataType2.NumField(); i++ {
			field := dataType2.Field(i)
			headers2[i] = field.Tag.Get("json")
			cellName, _ := excelize.CoordinatesToCellName(i+1, 1)
			err = file.SetCellValue("Sheet2", cellName, headers2[i])
			if err != nil {
				return fmt.Errorf("failed to set cell value: %w", err)
			}
		}

		// 添加数据行
		for rowIndex, data := range dataList2 {
			//dataValue := reflect.ValueOf(data)
			dataValue := utility.GetRealValue(data)
			for colIndex := 0; colIndex < dataType.NumField(); colIndex++ {
				fieldValue := dataValue.Field(colIndex)
				cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
				err = file.SetCellValue("Sheet2", cellName, fieldValue.Interface())
				if err != nil {
					return fmt.Errorf("failed to set cell value: %w", err)
				}
			}
		}
	}

	// 将第一个工作表设置为活动工作表
	file.SetActiveSheet(sheet)

	// 保存文件
	fileName := s.createFileName(".xlsx")
	filePath := gfile.Join(gfile.Temp(), fileName)
	if err := file.SaveAs(filePath); err != nil {
		r.Response.WriteExit(err)
	}

	// 设置 HTTP 响应头
	r.Response.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	r.Response.Header().Set("Content-Type", "application/octet-stream")

	g.RequestFromCtx(ctx).Response.ServeFileDownload(filePath, fileName)

	defer func() {
		if err := gfile.Remove(filePath); err != nil {
			glog.Error(ctx, err)
		}
	}()

	return
}

func (s *sFileProcessor) Export2Txt(ctx context.Context, data []string) (err error) {
	// 将字符串数组转换为单个字符串
	content := strings.Join(data, "\n")

	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "output-*.txt")
	if err != nil {
		return gerror.Wrap(err, "failed to create temp file")
	}
	defer os.Remove(tmpFile.Name()) // 函数结束后删除临时文件

	// 将内容写入临时文件
	if _, err = tmpFile.WriteString(content); err != nil {
		return gerror.Wrap(err, "failed to write to temp file")
	}

	// 确保文件写入完成
	if err = tmpFile.Sync(); err != nil {
		return gerror.Wrap(err, "failed to sync temp file")
	}

	// 关闭临时文件
	if err = tmpFile.Close(); err != nil {
		return gerror.Wrap(err, "failed to close temp file")
	}

	// 获取 HTTP 响应对象
	r := ghttp.RequestFromCtx(ctx)
	if r == nil {
		return gerror.New("failed to get request from context")
	}

	// 使用 ServeFileDownload 方法将文件下载给前端
	r.Response.ServeFileDownload(tmpFile.Name(), "output.txt")
	return nil
}

func (s *sFileProcessor) createFileName(suffix string) (fileName string) {
	fileName = "output_" + gtime.Now().Format("YmdHis") + suffix
	return
}

// Export2ExcelCn 导出为excel，前端即时下载
func (s *sFileProcessor) Export2ExcelCn(ctx context.Context, data interface{}) (err error) {
	r := g.RequestFromCtx(ctx)
	// 获取结构体类型
	dataList := gconv.SliceAny(data)
	dataType := reflect.TypeOf(dataList[0])

	// 创建 Excel 文件
	file := excelize.NewFile()
	sheet, err := file.NewSheet("Sheet1")
	if err != nil {
		return fmt.Errorf("failed to create new sheet: %w", err)
	}

	// 添加表头
	headers := make([]string, dataType.NumField())
	for i := 0; i < dataType.NumField(); i++ {
		field := dataType.Field(i)
		headers[i] = field.Tag.Get("dc")
		cellName, _ := excelize.CoordinatesToCellName(i+1, 1)
		err = file.SetCellValue("Sheet1", cellName, headers[i])
		if err != nil {
			return fmt.Errorf("failed to set cell value: %w", err)
		}
	}

	// 添加数据行
	for rowIndex, data := range dataList {
		//dataValue := reflect.ValueOf(data)
		dataValue := utility.GetRealValue(data)
		for colIndex := 0; colIndex < dataType.NumField(); colIndex++ {
			fieldValue := dataValue.Field(colIndex)
			cellName, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			err = file.SetCellValue("Sheet1", cellName, fieldValue.Interface())
			_ = file.SetColWidth("Sheet1", "A", "H", 20)
			if err != nil {
				return fmt.Errorf("failed to set cell value: %w", err)
			}
		}
	}

	// 将第一个工作表设置为活动工作表
	file.SetActiveSheet(sheet)

	// 保存文件
	fileName := s.createFileName(".xlsx")
	filePath := gfile.Join(gfile.Temp(), fileName)
	if err := file.SaveAs(filePath); err != nil {
		r.Response.WriteExit(err)
	}

	// 设置 HTTP 响应头
	r.Response.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	r.Response.Header().Set("Content-Type", "application/octet-stream")

	g.RequestFromCtx(ctx).Response.ServeFileDownload(filePath, fileName)

	defer func() {
		if err := gfile.Remove(filePath); err != nil {
			glog.Error(ctx, err)
		}
	}()

	return
}
