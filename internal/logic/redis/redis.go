package redis

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/redis/go-redis/v9"
)

var (
	ctx     = context.Background()
	cluster *redis.ClusterClient
)

type sRedis struct {
}

func init() {
	//service.RegisterRedis(New())
}

func New() *sRedis {
	s := &sRedis{}
	s.initRedis()
	return s
}

// 初始化 Redis 连接（单机或集群）
func (s *sRedis) initRedis() {
	config := g.Cfg().MustGet(ctx, "redisCluster").MapStrVar()

	cluster = redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:    config["addresses"].Strings(),
		Password: config["pass"].String(),
		PoolSize: config["poolSize"].Int(),
	})

	_, err := cluster.Ping(ctx).Result()
	if err != nil {
		g.Log().Error(ctx, "Redis Cluster 连接失败:", err)
		return
	}
	g.Log().Info(ctx, "Redis Cluster 连接成功")
}

// **存储单个 key**
func (s *sRedis) Set(key string, value interface{}) error {
	jsonData, err := gjson.Encode(value) // 转换成 JSON
	if err != nil {
		return err
	}
	return cluster.Set(ctx, key, jsonData, 0).Err()
}

// **获取单个 key**
func (s *sRedis) Get(key string, obj interface{}) error {
	jsonData, err := cluster.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	return gjson.DecodeTo(jsonData, obj) // 解析 JSON
}

// **批量存储**
func (s *sRedis) MSet(data map[string]interface{}) error {
	pipe := cluster.Pipeline()

	for k, v := range data {
		jsonData, err := gjson.Encode(v) // 转换成 JSON
		if err != nil {
			return err
		}
		pipe.Set(ctx, k, jsonData, 0)
	}

	_, err := pipe.Exec(ctx)
	return err
}

func (s *sRedis) Del(keys ...string) error {
	return cluster.Del(ctx, keys...).Err()
}
