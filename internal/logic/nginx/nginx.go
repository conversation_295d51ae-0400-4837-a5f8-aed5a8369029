package nginx

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
	"net"
	"os"
	"os/exec"
	"strings"
)

var ctx = gctx.New()

const file = "/etc/nginx/conf.d/gttest.conf"

type sNginx struct {
	domain string
}

func init() {
	service.RegisterNginx(New())
}

func New() *sNginx {
	s := &sNginx{}

	cfg := g.Cfg()
	if cfg != nil {
		s.domain = gconv.String(cfg.MustGet(ctx, "host.domain"))
	}

	s.Update(ctx)
	return s
}

func (s *sNginx) Update(ctx context.Context) {
	// 判断文件是否存在
	_, err := os.Stat(file)
	if os.IsNotExist(err) {
		// 文件不存在，直接返回
		return
	}

	s.clearNginxCfg(ctx)

	res, err := service.CmsLoginRisk().List(ctx, &v1.CmsLoginRiskListReq{IsOpen: 1})
	if err != nil {
		return
	}
	allowIps := ""
	for _, v := range res.List {
		if strings.Contains(v.Content, "*") {
			continue
		}

		if strings.Contains(v.Content, "~") {
			startIP, endIP := s.parseIPRange(v.Content)
			ipList := s.generateIPList(startIP, endIP)
			for _, ip := range ipList {
				allowIps += fmt.Sprintf("allow %s;\n", ip)
			}
		} else {
			allowIps += fmt.Sprintf("allow %s;\n", v.Content)
		}
	}

	// 添加到配置文件中
	f, err := os.OpenFile(file, os.O_APPEND|os.O_WRONLY, os.ModePerm)
	if err != nil {
		return
	}
	defer f.Close()

	_, err = f.WriteString(fmt.Sprintf(`
server {
        listen 80;
        server_name  %s;
        root         /usr/share/nginx/html;

		%s  
    	deny all;            # 拒绝所有其他 IP 地址

        location /aapi {
            proxy_pass http://127.0.0.1:8001;
        }

        location / {
            try_files $uri $uri/ /index.html;
        }
}
	`, s.domain, allowIps))
	if err != nil {
		return
	}

	err = s.reloadNginx(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "reloadNginx error: %v", err)
		return
	}
}

func (s *sNginx) clearNginxCfg(ctx context.Context) {
	// 打开文件以截断内容
	file, err := os.OpenFile(file, os.O_TRUNC|os.O_WRONLY, 0644)
	if err != nil {
		g.Log().Error(ctx, "openFile error:", err)
	}
	defer file.Close()

	// 清空文件的内容
	if err := file.Truncate(0); err != nil {
		g.Log().Error(ctx, "truncateFile error:", err)
	}
}

func (s *sNginx) reloadNginx(ctx context.Context) error {
	scriptPath := "/home/<USER>/reload-nginx.sh"
	//scriptPath := "/home/<USER>/reload-nginx.sh"

	// 检查文件是否存在
	if _, err := os.Stat(scriptPath); os.IsNotExist(err) {
		g.Log().Line().Error(ctx, "Script file does not exist:", scriptPath)
		return err
	}

	g.Log().Line().Info(ctx, "Executing script:", scriptPath)

	// 执行脚本
	reloadCmd := exec.Command(scriptPath)

	// 运行命令并检查是否有错误
	if err := reloadCmd.Run(); err != nil {
		return err
	}

	g.Log().Line().Info(ctx, "Nginx restart success")
	return nil
}

func (s *sNginx) parseIPRange(ipRange string) (net.IP, net.IP) {
	parts := strings.Split(ipRange, "~")
	return net.ParseIP(parts[0]), net.ParseIP(parts[1])
}

// 生成单个 IP 地址列表
func (s *sNginx) generateIPList(startIP, endIP net.IP) []string {
	var ipList []string
	start := s.ipToUint32(startIP)
	end := s.ipToUint32(endIP)

	for i := start; i <= end; i++ {
		ipList = append(ipList, s.uint32ToIP(i).String())
	}

	return ipList
}

func (s *sNginx) ipToUint32(ip net.IP) uint32 {
	ip = ip.To4()
	return uint32(ip[0])<<24 | uint32(ip[1])<<16 | uint32(ip[2])<<8 | uint32(ip[3])
}

func (s *sNginx) uint32ToIP(ip uint32) net.IP {
	return net.IPv4(byte(ip>>24), byte(ip>>16), byte(ip>>8), byte(ip)).To4()
}
