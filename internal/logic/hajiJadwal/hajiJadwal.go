package hajiJadwal

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	dao "gtcms/internal/dao/islamic_content_svc"
	do "gtcms/internal/model/do/islamic_content_svc"
	entity "gtcms/internal/model/entity/islamic_content_svc"
	"gtcms/internal/service"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/gutil"
)

type sHajiJadwal struct{}

func init() {
	service.RegisterHajiJadwal(New())
}

func New() *sHajiJadwal {
	return &sHajiJadwal{}
}

// ==================== 日程说明相关接口实现 ====================

func (s *sHajiJadwal) DescriptionGet(ctx context.Context, req *v1.HajiJadwalDescriptionGetReq) (out *v1.HajiJadwalDescriptionGetRes, err error) {
	out = new(v1.HajiJadwalDescriptionGetRes)
	out.Description = make([]v1.HajiJadwalDescriptionItem, 0)

	var descriptions []entity.HajiJadwalDescription
	err = dao.HajiJadwalDescription.Ctx(ctx).
		Scan(&descriptions)
	if err != nil {
		return out, err
	}

	for _, desc := range descriptions {
		out.Description = append(out.Description, v1.HajiJadwalDescriptionItem{
			LanguageType: int(desc.LanguageId),
			Description:  desc.Description,
		})
	}

	return out, nil
}

func (s *sHajiJadwal) DescriptionSave(ctx context.Context, req *v1.HajiJadwalDescriptionSaveReq) (out *v1.HajiJadwalDescriptionSaveRes, err error) {
	out = new(v1.HajiJadwalDescriptionSaveRes)

	err = dao.HajiJadwalDescription.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 插入新的说明
		var descriptions []do.HajiJadwalDescription
		for _, item := range req.Description {
			descriptions = append(descriptions, do.HajiJadwalDescription{
				Year:        time.Now().Year(), // 这个字段暂时没用的
				LanguageId:  item.LanguageType,
				Description: item.Description,
			})
		}

		if len(descriptions) > 0 {
			var err1 error
			// 先删除所有说明
			_, err1 = tx.Model(dao.HajiJadwalDescription.Table()).Ctx(ctx).Where("id > 0").Delete()
			if err1 != nil {
				return err1
			}

			_, err1 = tx.Model(dao.HajiJadwalDescription.Table()).Ctx(ctx).Data(descriptions).Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return out, err
	}

	return out, nil
}

// ==================== 日程相关接口实现 ====================

func (s *sHajiJadwal) List(ctx context.Context, req *v1.HajiJadwalListReq) (out *v1.HajiJadwalListRes, err error) {
	out = new(v1.HajiJadwalListRes)
	out.Current = req.Current
	out.Offset = req.Offset

	// 获取当前语言
	languageId := gconv.Int(ctx.Value(consts.LanguageId))

	var jadwals []entity.HajiJadwal
	query := dao.HajiJadwal.Ctx(ctx).OrderAsc(dao.HajiJadwal.Columns().ItemNo)

	// 分页
	if req.Current > 0 && req.PageSize > 0 {
		query = query.Page(req.Current, req.PageSize)
	}

	err = query.Scan(&jadwals)
	if err != nil {
		return nil, err
	}

	// 总数
	out.Total, err = dao.HajiJadwal.Ctx(ctx).Count()
	if err != nil {
		return nil, err
	}

	// 先提取id
	jadwalIds := gutil.ListItemValuesUnique(jadwals, "Id")

	var contents []entity.HajiJadwalContent
	if len(jadwalIds) > 0 {
		err = dao.HajiJadwalContent.Ctx(ctx).
			WhereIn(dao.HajiJadwalContent.Columns().JadwalId, jadwalIds).
			Scan(&contents)
		if err != nil {
			return nil, err
		}
	}

	contentMap := make(map[uint64][]entity.HajiJadwalContent)
	for _, content := range contents {
		contentMap[content.JadwalId] = append(contentMap[content.JadwalId], content)
	}

	// 构建返回数据

	for _, jadwal := range jadwals {
		item := &v1.HajiJadwalListItem{
			Id:          jadwal.Id,
			ItemNo:      jadwal.ItemNo,
			LanguageArr: make([]v1.LanguageArrItem, 0),
		}

		// 设置当前语言的内容
		for _, content := range contentMap[jadwal.Id] {
			if int(content.LanguageId) == languageId {
				item.TimeInfo = content.TimeInfo
				item.EventSummary = content.EventSummary
				break
			}
		}

		// 构建语言支持列表
		s.buildLanguageArr(item, contentMap[jadwal.Id])

		out.List = append(out.List, *item)
	}

	return out, nil
}

// buildLanguageArr 构建语言支持列表
func (s *sHajiJadwal) buildLanguageArr(item *v1.HajiJadwalListItem, contents []entity.HajiJadwalContent) {
	languageSupport := map[int]int{
		consts.ArticleLanguageZh: consts.Zero,
		consts.ArticleLanguageEn: consts.Zero,
		consts.ArticleLanguageId: consts.Zero,
	}

	// 检查支持的语言
	for _, content := range contents {
		languageSupport[int(content.LanguageId)] = consts.One
	}

	// 构建语言数组
	item.LanguageArr = []v1.LanguageArrItem{
		{
			LanguageType:     consts.ArticleLanguageZh,
			LanguageTypeText: "ZH",
			IsSupport:        languageSupport[consts.ArticleLanguageZh],
		},
		{
			LanguageType:     consts.ArticleLanguageEn,
			LanguageTypeText: "EN",
			IsSupport:        languageSupport[consts.ArticleLanguageEn],
		},
		{
			LanguageType:     consts.ArticleLanguageId,
			LanguageTypeText: "ID",
			IsSupport:        languageSupport[consts.ArticleLanguageId],
		},
	}
}

func (s *sHajiJadwal) Add(ctx context.Context, req *v1.HajiJadwalCreateReq) (out *v1.HajiJadwalCreateRes, err error) {
	out = new(v1.HajiJadwalCreateRes)

	// 检查展示顺序是否重复
	count, err := dao.HajiJadwal.Ctx(ctx).
		Where(dao.HajiJadwal.Columns().ItemNo, req.ItemNo).
		Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, gerror.New("此编号已存在，请输入不同的顺序编号")
	}

	var jadwalId int64
	err = dao.HajiJadwal.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var err1 error
		jadwalId, err1 = tx.Model(dao.HajiJadwal.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiJadwal{ItemNo: req.ItemNo}).
			InsertAndGetId()
		if err1 != nil {
			return err1
		}

		// 插入多语言内容
		var contents []do.HajiJadwalContent
		for _, item := range req.Content {
			contents = append(contents, do.HajiJadwalContent{
				JadwalId:     jadwalId,
				LanguageId:   item.LanguageType,
				TimeInfo:     item.TimeInfo,
				EventSummary: item.EventSummary,
				ArticleText:  item.ArticleText,
			})
		}

		if len(contents) > 0 {
			_, err1 = tx.Model(dao.HajiJadwalContent.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(contents).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	out.Id = jadwalId
	return out, nil
}

func (s *sHajiJadwal) Edit(ctx context.Context, req *v1.HajiJadwalEditReq) (out *v1.HajiJadwalEditRes, err error) {
	out = new(v1.HajiJadwalEditRes)

	// 检查日程是否存在
	var jadwal entity.HajiJadwal
	err = dao.HajiJadwal.Ctx(ctx).
		Where(dao.HajiJadwal.Columns().Id, req.Id).
		Scan(&jadwal)
	if err != nil {
		return nil, err
	}
	if jadwal.Id == 0 {
		return nil, gerror.New("日程不存在")
	}

	// 检查展示顺序是否重复（排除自己）
	count, err := dao.HajiJadwal.Ctx(ctx).
		Where(dao.HajiJadwal.Columns().ItemNo, req.ItemNo).
		WhereNot(dao.HajiJadwal.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, gerror.New("此编号已存在，请输入不同的顺序编号")
	}

	err = dao.HajiJadwal.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err1 := tx.Model(dao.HajiJadwal.Table()).Ctx(ctx).
			SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
			Data(do.HajiJadwal{
				ItemNo: req.ItemNo,
			}).
			Where(dao.HajiJadwal.Columns().Id, req.Id).
			Update()
		if err1 != nil {
			return err1
		}

		// 删除旧的，插入新的
		_, err1 = tx.Model(dao.HajiJadwalContent.Table()).Ctx(ctx).
			Where(dao.HajiJadwalContent.Columns().JadwalId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 插入新的多语言内容
		var contents []do.HajiJadwalContent
		for _, item := range req.Content {
			contents = append(contents, do.HajiJadwalContent{
				JadwalId:     req.Id,
				LanguageId:   item.LanguageType,
				TimeInfo:     item.TimeInfo,
				EventSummary: item.EventSummary,
				ArticleText:  item.ArticleText,
			})
		}

		if len(contents) > 0 {
			_, err1 = tx.Model(dao.HajiJadwalContent.Table()).Ctx(ctx).
				SoftTime(gdb.SoftTimeOption{SoftTimeType: gdb.SoftTimeTypeTimestampMilli}).
				Data(contents).
				Insert()
			if err1 != nil {
				return err1
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}

func (s *sHajiJadwal) One(ctx context.Context, req *v1.HajiJadwalOneReq) (res *v1.HajiJadwalOneRes, err error) {
	var jadwal entity.HajiJadwal
	err = dao.HajiJadwal.Ctx(ctx).
		Where(dao.HajiJadwal.Columns().Id, req.Id).
		Scan(&jadwal)
	if err != nil {
		return nil, err
	}
	if jadwal.Id == 0 {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "日程不存在")
	}

	// 获取多语言内容
	var contents []entity.HajiJadwalContent
	err = dao.HajiJadwalContent.Ctx(ctx).
		Where(dao.HajiJadwalContent.Columns().JadwalId, req.Id).
		Scan(&contents)
	if err != nil {
		return nil, err
	}

	// 构建返回数据
	item := &v1.HajiJadwalListItem{
		Id:     jadwal.Id,
		ItemNo: int(jadwal.ItemNo),
		// TimeInfo:     jadwal.,
		// EventSummary: jadwal.CreateTime,
		LanguageArr: make([]v1.LanguageArrItem, 0),
	}

	for _, content := range contents {
		item.TimeInfo = content.TimeInfo
		item.EventSummary = content.EventSummary
	}

	// 构建语言支持列表
	s.buildLanguageArr(item, contents)

	res = &v1.HajiJadwalOneRes{
		HajiJadwalListItem: *item,
	}
	return res, nil
}

func (s *sHajiJadwal) Delete(ctx context.Context, req *v1.HajiJadwalDeleteReq) (out *v1.HajiJadwalDeleteRes, err error) {
	out = new(v1.HajiJadwalDeleteRes)

	// 检查日程是否存在
	count, err := dao.HajiJadwal.Ctx(ctx).
		Where(dao.HajiJadwal.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, gerror.New("日程不存在")
	}

	err = dao.HajiJadwal.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 删除多语言内容
		_, err1 := tx.Model(dao.HajiJadwalContent.Table()).Ctx(ctx).
			Where(dao.HajiJadwalContent.Columns().JadwalId, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		// 删除主表记录
		_, err1 = tx.Model(dao.HajiJadwal.Table()).Ctx(ctx).
			Where(dao.HajiJadwal.Columns().Id, req.Id).
			Delete()
		if err1 != nil {
			return err1
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return out, nil
}
