package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"sync"
	"time"
)

type RequestBody struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// 所有可用风格
var styles = []string{
	"正式", "非正式", "口语化", "描述性", "叙述性", "说明性", "议论文",
	"比较对比", "批判性", "技术性", "新闻风格", "特写报道", "广告风格",
	"SEO风格", "演讲风格", "社交媒体风格", "剧本风格",
	"轻松幽默", "讽刺挖苦", "温暖治愈", "感性情绪化", "冷静理性",
}

func randomStyle() string {
	rand.Seed(time.Now().UnixNano())
	return styles[rand.Intn(len(styles))]
}

// 封装改写函数
func RewriteHTMLArticle(originalHTML string, style string) (string, error) {
	url := "https://qianfan.baidubce.com/v2/chat/completions"

	// 构造请求体
	content := fmt.Sprintf(`请将以下带有 HTML 标签的文章改写为“%s”风格：\n- 不要更改或删除任何 HTML 标签（如 <p>、<div>、<strong> 等），保留其原有结构和顺序；\n- 只改写标签内的中文文本内容；\n- 不要添加任何说明或前缀，仅输出纯正文内容。\n文章原文：\n %s`, style, originalHTML)

	// 构造RequestBody
	requestBody := RequestBody{
		Model: "ernie-4.5-turbo-128k",
		Messages: []Message{
			{
				Role:    "user",
				Content: content,
			},
		},
	}

	// 将RequestBody结构体转换为JSON格式
	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		return "", err
	}

	// 发起请求
	client := &http.Client{}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBodyBytes))
	if err != nil {
		return "", err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("appid", "") // 请填入你的 appid
	req.Header.Add("Authorization", "Bearer bce-v3/ALTAK-3bb5GnSWI7cloHUBq6Sy4/8d4cdbe15873116b3f76b332ba162fad30ea8a33")

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 解析响应
	var response Response
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		fmt.Println("解析响应失败:", err)
		return "", nil
	}

	return response.Choices[0].Message.Content, nil
}

// 定义返回响应的结构
type Response struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
	Flag         int     `json:"flag"`
}

type Usage struct {
	PromptTokens        int                 `json:"prompt_tokens"`
	CompletionTokens    int                 `json:"completion_tokens"`
	TotalTokens         int                 `json:"total_tokens"`
	PromptTokensDetails PromptTokensDetails `json:"prompt_tokens_details"`
}

type PromptTokensDetails struct {
	CachedTokens int `json:"cached_tokens"`
}

// 并发改写文章的函数
func RewriteMultipleArticles(originalHTML string, num int) ([]string, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var results []string
	var err error

	for i := 0; i < num; i++ {
		wg.Add(1)

		go func() {
			defer wg.Done()
			// 随机选择风格
			style := randomStyle()
			// 调用改写函数
			result, rewriteErr := RewriteHTMLArticle(originalHTML, style)
			if rewriteErr != nil {
				err = rewriteErr
				return
			}

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		}()
	}

	// 等待所有Goroutine完成
	wg.Wait()

	// 如果有错误则返回
	if err != nil {
		return nil, err
	}

	return results, nil
}
