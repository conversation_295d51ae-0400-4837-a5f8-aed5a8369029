package googleAuthenticator

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha1"
	"encoding/base32"
	"gtcms/internal/service"
	"strings"
	"time"
)

type (
	sGoogleAuthenticator struct{}
)

func init() {
	service.RegisterGoogleAuthenticator(New())
}

func New() service.IGoogleAuthenticator {
	return &sGoogleAuthenticator{}
}

func toBytes(value int64) []byte {
	var result []byte
	mask := int64(0xFF)
	shifts := [8]uint16{56, 48, 40, 32, 24, 16, 8, 0}
	for _, shift := range shifts {
		result = append(result, byte((value>>shift)&mask))
	}
	return result
}

func toUint32(bytes []byte) uint32 {
	return (uint32(bytes[0]) << 24) + (uint32(bytes[1]) << 16) +
		(uint32(bytes[2]) << 8) + uint32(bytes[3])
}

func oneTimePassword(key []byte, value []byte) uint32 {
	// sign the value using HMAC-SHA1
	hmacSha1 := hmac.New(sha1.New, key)
	hmacSha1.Write(value)
	hash := hmacSha1.Sum(nil)

	// We're going to use a subset of the generated hash.
	// Using the last nibble (half-byte) to choose the index to start from.
	// This number is always appropriate as it's maximum decimal 15, the hash will
	// have the maximum index 19 (20 bytes of SHA1) and we need 4 bytes.
	offset := hash[len(hash)-1] & 0x0F

	// get a 32-bit (4-byte) chunk from the hash starting at offset
	hashParts := hash[offset : offset+4]

	// ignore the most significant bit as per RFC 4226
	hashParts[0] = hashParts[0] & 0x7F

	number := toUint32(hashParts)

	// size to 6 digits
	// one million is the first number with 7 digits so the remainder
	// of the division will always return < 7 digits
	pwd := number % 1000000

	return pwd
}

func randStr(strSize int) string {
	dictionary := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var bytes = make([]byte, strSize)
	_, _ = rand.Read(bytes)
	for k, v := range bytes {
		bytes[k] = dictionary[v%byte(len(dictionary))]
	}
	return string(bytes)
}

// 生成16位密钥
func (s *sGoogleAuthenticator) CreateSecret() string {
	randomStr := randStr(16)
	return strings.ToUpper(randomStr)
}

// 获取验证码
func (s *sGoogleAuthenticator) GetCode(secretKey string, epochSeconds int64) (code int32) {
	secretKeyUpper := strings.ToUpper(secretKey)
	key, err := base32.StdEncoding.DecodeString(secretKeyUpper)
	if err != nil {
		// log.Error(err)
		return
	}

	// generate a one-time password using the time at 30-second intervals
	code = int32(oneTimePassword(key, toBytes(epochSeconds/30)))
	return
}

// 验证,传入验证key和code代码，返回验证是否成功
func (s *sGoogleAuthenticator) VerifyCode(secretKey string, code int32) bool {
	// 当前google值
	epochSeconds := time.Now().Unix()
	if s.GetCode(secretKey, epochSeconds) == code {
		return true
	}

	// 前30秒google值
	if s.GetCode(secretKey, epochSeconds-30) == code {
		return true
	}

	// 后30秒google值
	if s.GetCode(secretKey, epochSeconds+30) == code {
		return true
	}

	return false
}
