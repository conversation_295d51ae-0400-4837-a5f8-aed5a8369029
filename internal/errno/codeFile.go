package errno

import "github.com/gogf/gf/v2/errors/gcode"

var (
	CodeFileClientConfigError = gcode.New(17001, "file.client.config.error", nil) // 文件配置错误
	CodeFileClientError       = gcode.New(17002, "file.client.error", nil)        // 文件客户端错误
	CodeFileClientHandleError = gcode.New(17003, "file.client.handle.error", nil) // 文件客户端处理错误
	CodeFileUploadError       = gcode.New(17004, "file.upload.error", nil)        // 文件上传错误
)
