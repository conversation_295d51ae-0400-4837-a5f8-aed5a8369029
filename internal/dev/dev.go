package dev

import (
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gres"
	"strings"
)

func init() {
	ctx := gctx.New()
	gres.Dump()
	data, cErr := g.Config().Data(ctx)
	if cErr != nil {
		fmt.Println(cErr)
		return
	}
	fmt.Println("----", data)
	cAdapter := g.Config().GetAdapter()
	available := cAdapter.Available(ctx)
	fmt.Println("ConfigFile: available ", available)

	if fileConfig, ok := cAdapter.(*gcfg.AdapterFile); ok {
		fmt.Println("cAdapter is *gcfg.AdapterFile")
		fmt.Println("GetPaths:", strings.Join(fileConfig.GetPaths(), ","))
		configFile := "config.yaml"
		fp, ferr := fileConfig.GetFilePath(configFile)
		fmt.Printf("GetFilePath: %s %s %+v\n", configFile, fp, ferr)
		cc := gres.GetContent("manifest/config/config.yaml")
		fmt.Println("config.yaml", string(cc))

	}
	if _, ok := cAdapter.(*gcfg.AdapterContent); ok {
		fmt.Println("cAdapter is *gcfg.AdapterContent")

	}
	panic("exit")

}
