package role_v2

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) List(ctx context.Context, req *v1.RoleMgrListReq) (res *v1.RoleMgrListRes, err error) {
	res, err = service.RoleV2().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.RoleMgrAddReq) (res *v1.EmptyDataRes, err error) {
	err = service.RoleV2().Create(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.RoleMgrEditReq) (res *v1.EmptyDataRes, err error) {
	err = service.RoleV2().Edit(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.RoleMgrDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.RoleV2().Delete(ctx, req)
	return
}

func (c *Controller) Detail(ctx context.Context, req *v1.RoleMgrDetailReq) (res *v1.RoleMgrDetailRes, err error) {
	res = &v1.RoleMgrDetailRes{}
	res.RoleConfig, err = service.RoleV2().Detail(ctx, req.Id)
	if err != nil {
		return
	}

	res.PermissionNames, err = service.PermissionV2().GetPermNames(ctx, res.PermissionSet)
	return
}

func (c *Controller) Options(ctx context.Context, req *v1.RoleMgrOptionsReq) (res *v1.RoleMgrOptionsRes, err error) {
	res = &v1.RoleMgrOptionsRes{}
	res.Options, err = service.RoleV2().Options(ctx)
	return
}
