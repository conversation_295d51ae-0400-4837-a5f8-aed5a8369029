package permission_v2

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) List(ctx context.Context, req *v1.PermissionMgrListReq) (res *v1.PermissionMgrListRes, err error) {
	res, err = service.PermissionV2().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.PermissionMgrAddReq) (res *v1.EmptyDataRes, err error) {
	err = service.PermissionV2().Create(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.PermissionMgrEditReq) (res *v1.EmptyDataRes, err error) {
	err = service.PermissionV2().Edit(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.PermissionMgrDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.PermissionV2().Delete(ctx, req)
	return
}

func (c *Controller) Detail(ctx context.Context, req *v1.PermissionMgrDetailReq) (res *v1.PermissionMgrDetailRes, err error) {
	res = new(v1.PermissionMgrDetailRes)
	res.PermissionNode, err = service.PermissionV2().Detail(ctx, req.Id)
	return
}

func (c *Controller) Subs(ctx context.Context, req *v1.PermissionMgrChildReq) (res *v1.PermissionMgrChildRes, err error) {
	//subs, err := service.PermissionV2().Subs(ctx, req.Id)
	//if err != nil {
	//	return
	//}
	//res = &v1.PermissionMgrChildRes{
	//	//Child: subs,
	//}

	return
}

func (c *Controller) PermissionMgrMask(ctx context.Context, req *v1.PermissionMgrMaskReq) (res *v1.EmptyDataRes, err error) {
	//usr := &v1.UserListReq{
	//	ListReq: v1.ListReq{
	//		Current:  1,
	//		PageSize: 2,
	//	},
	//	OrderBy: "",
	//}
	//out, err := service.User().List(ctx, usr, []int{})
	//if err != nil {
	//	return nil, err
	//}
	////  service.PermissionV2().MaskingRespPacket(ctx, reflect.ValueOf(&out),
	////	[]string{"List", "IsOnline"}, 0)
	//
	//var isIntegerMask bool
	//service.PermissionV2().MaskingRespPacket(ctx, reflect.ValueOf(&out),
	//	[]string{"List", "PhoneNum"}, 0, &isIntegerMask)

	//service.PermissionV2().MaskingRespPacket2(ctx, out,
	//	[]string{"List", "PhoneNum"})
	return
}
