package module

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) List(ctx context.Context, req *v1.ModuleListReq) (res *v1.ModuleList<PERSON>es, err error) {
	res, err = service.Module().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.ModuleAddReq) (res *v1.ModuleAddRes, err error) {
	res, err = service.Module().Add(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.ModuleEditReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.Module().Edit(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.ModuleDeleteReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.Module().Delete(ctx, req)
	return
}

func (c *Controller) Options(ctx context.Context, req *v1.ModuleOptionsReq) (res *v1.ModuleOptionsRes, err error) {
	res = new(v1.ModuleOptionsRes)
	res.Options, err = service.Module().Options(ctx, req)
	return
}
