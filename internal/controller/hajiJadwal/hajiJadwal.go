package hajiJadwal

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// ==================== 日程说明相关接口 ====================

func (c *Controller) DescriptionGet(ctx context.Context, req *v1.HajiJadwalDescriptionGetReq) (res *v1.HajiJadwalDescriptionGetRes, err error) {
	res, err = service.HajiJadwal().DescriptionGet(ctx, req)
	return
}

func (c *Controller) DescriptionSave(ctx context.Context, req *v1.HajiJadwalDescriptionSaveReq) (res *v1.HajiJadwalDescriptionSaveRes, err error) {
	res, err = service.HajiJadwal().DescriptionSave(ctx, req)
	return
}

// ==================== 日程相关接口 ====================

func (c *Controller) List(ctx context.Context, req *v1.HajiJadwalListReq) (res *v1.HajiJadwalListRes, err error) {
	res, err = service.HajiJadwal().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.HajiJadwalCreateReq) (res *v1.HajiJadwalCreateRes, err error) {
	res, err = service.HajiJadwal().Add(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.HajiJadwalEditReq) (res *v1.HajiJadwalEditRes, err error) {
	res, err = service.HajiJadwal().Edit(ctx, req)
	return
}

func (c *Controller) One(ctx context.Context, req *v1.HajiJadwalOneReq) (res *v1.HajiJadwalOneRes, err error) {
	res, err = service.HajiJadwal().One(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.HajiJadwalDeleteReq) (res *v1.HajiJadwalDeleteRes, err error) {
	res, err = service.HajiJadwal().Delete(ctx, req)
	return
}
