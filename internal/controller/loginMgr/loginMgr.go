package loginMgr

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) LoginOut(ctx context.Context, req *v1.LoginOutReq) (res *v1.EmptyDataRes, err error) {
	err = service.AccountLoginMgr().SignOut(ctx, req)
	return
}

func (c *Controller) LoginLog(ctx context.Context, req *v1.LoginLogReq) (res *v1.LoginLogRes, err error) {
	res = &v1.LoginLogRes{}
	res, err = service.AccountLoginMgr().ListLoginLog(ctx, req)
	return
}

func (c *Controller) ModifyPassword(ctx context.Context, req *v1.ModifyPasswordReq) (res *v1.EmptyDataRes, err error) {
	err = service.AccountLoginMgr().ModifyPassword(ctx, req)
	return
}
