package quranCrawler

import (
	"context"

	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

// Controller 古兰经爬虫控制器
type Controller struct{}

// New 创建古兰经爬虫控制器
func New() *Controller {
	return &Controller{}
}

// Start 开始爬取古兰经数据
func (c *Controller) Start(ctx context.Context, req *v1.QuranCrawlerStartReq) (res *v1.QuranCrawlerStartRes, err error) {
	return service.QuranCrawler().Start(ctx, req)
}

// Status 查询古兰经数据状态
func (c *Controller) Status(ctx context.Context, req *v1.QuranCrawlerStatusReq) (res *v1.QuranCrawlerStatusRes, err error) {
	return service.QuranCrawler().Status(ctx, req)
}

// SuratList 获取章节列表
func (c *Controller) SuratList(ctx context.Context, req *v1.QuranCrawlerSuratListReq) (res *v1.QuranCrawlerSuratListRes, err error) {
	return service.QuranCrawler().SuratList(ctx, req)
}

// SuratDetail 获取章节详情
func (c *Controller) SuratDetail(ctx context.Context, req *v1.QuranCrawlerSuratDetailReq) (res *v1.QuranCrawlerSuratDetailRes, err error) {
	return service.QuranCrawler().SuratDetail(ctx, req)
}
