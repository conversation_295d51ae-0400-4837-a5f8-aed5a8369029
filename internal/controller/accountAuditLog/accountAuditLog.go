package accountAuditLog

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) AdminModifyLog(ctx context.Context, req *v1.AccountAuditLogReq) (res *v1.AccountAuditLogRes, err error) {
	res, err = service.AccountAuditLog().ListModifyLog(ctx, req)
	for i, v := range res.List {
		if !g.IsEmpty(v.DiffOld) {
			res.List[i].ValueBefore = v.DiffOld
		}
		if !g.IsEmpty(v.DiffNew) {
			res.List[i].ValueAfter = v.DiffNew
		}
	}
	return
}

func (c *Controller) AdminNodeLevel(ctx context.Context, req *v1.CmsNodeReq) (res *v1.CmsNodeRes, err error) {
	res = &v1.CmsNodeRes{}
	res.List, err = service.AccountNodeConfig().GetNodeLevelInfo()
	return
}
