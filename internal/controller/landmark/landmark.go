package landmark

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

// ==================== 地标类型相关接口 ====================

func (c *Controller) TypeList(ctx context.Context, req *v1.LandmarkTypeListReq) (res *v1.LandmarkTypeListRes, err error) {
	res, err = service.Landmark().TypeList(ctx, req)
	return
}

func (c *Controller) TypeAdd(ctx context.Context, req *v1.LandmarkTypeCreateReq) (res *v1.LandmarkTypeCreateRes, err error) {
	res, err = service.Landmark().TypeAdd(ctx, req)
	return
}

func (c *Controller) TypeEdit(ctx context.Context, req *v1.LandmarkTypeEditReq) (res *v1.LandmarkTypeEditRes, err error) {
	res, err = service.Landmark().TypeEdit(ctx, req)
	return
}

func (c *Controller) TypeOne(ctx context.Context, req *v1.LandmarkTypeOneReq) (res *v1.LandmarkTypeOneRes, err error) {
	res, err = service.Landmark().TypeOne(ctx, req)
	return
}

func (c *Controller) TypeDelete(ctx context.Context, req *v1.LandmarkTypeDeleteReq) (res *v1.LandmarkTypeDeleteRes, err error) {
	res, err = service.Landmark().TypeDelete(ctx, req)
	return
}

func (c *Controller) TypeOptions(ctx context.Context, req *v1.LandmarkTypeOptionsReq) (res *v1.LandmarkTypeOptionsRes, err error) {
	res, err = service.Landmark().TypeOptions(ctx, req)
	return
}

// ==================== 地标相关接口 ====================

func (c *Controller) List(ctx context.Context, req *v1.LandmarkListReq) (res *v1.LandmarkListRes, err error) {
	res, err = service.Landmark().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.LandmarkCreateReq) (res *v1.LandmarkCreateRes, err error) {
	res, err = service.Landmark().Add(ctx, req)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.LandmarkEditReq) (res *v1.LandmarkEditRes, err error) {
	res, err = service.Landmark().Edit(ctx, req)
	return
}

func (c *Controller) One(ctx context.Context, req *v1.LandmarkOneReq) (res *v1.LandmarkOneRes, err error) {
	res, err = service.Landmark().One(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.LandmarkDeleteReq) (res *v1.LandmarkDeleteRes, err error) {
	res, err = service.Landmark().Delete(ctx, req)
	return
}
