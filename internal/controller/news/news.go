package news

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

type Controller struct {
}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) List(ctx context.Context, req *v1.NewsListReq) (res *v1.NewsListRes, err error) {
	res, err = service.News().List(ctx, req)
	return
}

func (c *Controller) Add(ctx context.Context, req *v1.NewsAddReq) (res *v1.NewsAddRes, err error) {
	res, err = service.News().Add(ctx, req, false)
	return
}

func (c *Controller) Edit(ctx context.Context, req *v1.NewsEditReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.News().Edit(ctx, req)
	return
}

func (c *Controller) Delete(ctx context.Context, req *v1.NewsDeleteReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.News().Delete(ctx, req)
	return
}

func (c *Controller) One(ctx context.Context, req *v1.NewsOneReq) (res *v1.NewsOneRes, err error) {
	res, err = service.News().One(ctx, req)
	return
}

func (c *Controller) UpdateStatus(ctx context.Context, req *v1.NewsUpdateStatusReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.News().UpdateStatus(ctx, req)
	return
}

func (c *Controller) ImportTxt(ctx context.Context, req *v1.NewsImportTxtReq) (res *v1.EmptyDataRes, err error) {
	res, err = service.News().ImportTxt(ctx, req)
	return
}
