// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	do "gtcms/internal/model/do/admin"
	entity "gtcms/internal/model/entity/admin"
)

type (
	IRisk interface {
		RiskContentList(ctx context.Context, tabType int, key string, isOpen int, startTime int64, endTime int64, page *v1.ListReq) (labels []entity.RiskControlContent, listRes v1.ListRes, err error)
		RiskContentEdit(ctx context.Context, id uint, do *do.RiskControlContent) (err error)
		RiskContentAdd(ctx context.Context, do *do.RiskControlContent) (err error)
		RiskContentDelete(ctx context.Context, id uint) (err error)
		RiskTabList(ctx context.Context) (tabs []entity.RiskControlTab, err error)
		RiskTabEditSwitch(ctx context.Context, tabType uint, isOpen int) (err error)
		RiskTabEditMetas(ctx context.Context, tabType uint, metas string) (err error)
	}
)

var (
	localRisk IRisk
)

func Risk() IRisk {
	if localRisk == nil {
		panic("implement not found for interface IRisk, forgot register?")
	}
	return localRisk
}

func RegisterRisk(i IRisk) {
	localRisk = i
}
