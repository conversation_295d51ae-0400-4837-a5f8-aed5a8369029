// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	ICalendarMgr interface {
		// Init 初始化日历数据
		Init(ctx context.Context, year int) error
	}
)

var (
	localCalendarMgr ICalendarMgr
)

func CalendarMgr() ICalendarMgr {
	if localCalendarMgr == nil {
		panic("implement not found for interface ICalendarMgr, forgot register?")
	}
	return localCalendarMgr
}

func RegisterCalendarMgr(i ICalendarMgr) {
	localCalendarMgr = i
}
