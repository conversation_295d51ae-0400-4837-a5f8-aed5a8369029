// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IVideo interface {
		// 添加
		Add(ctx context.Context, req *v1.VideoAddReq) error
		// 编辑
		Edit(ctx context.Context, req *v1.VideoEditReq) error
		// 详情
		Info(ctx context.Context, req *v1.VideoInfoReq) (out *v1.VideoInfoRes, err error)
		// 列表
		List(ctx context.Context, req *v1.VideoListReq) (out *v1.VideoListRes, err error)
		// 删除
		Delete(ctx context.Context, req *v1.VideoDeleteReq) error
		// 上线
		SetOnline(ctx context.Context, req *v1.VideoSetOnlineReq) error
		// 下线
		SetOffline(ctx context.Context, req *v1.VideoSetOfflineReq) error
	}
)

var (
	localVideo IVideo
)

func Video() IVideo {
	if localVideo == nil {
		panic("implement not found for interface IVideo, forgot register?")
	}
	return localVideo
}

func RegisterVideo(i IVideo) {
	localVideo = i
}
