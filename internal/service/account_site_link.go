// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
)

type (
	IAccountSiteLink interface {
		Create(ctx context.Context, in *entity.AccountSiteLink) (err error)
		Delete(ctx context.Context, id uint, accountId uint) (err error)
		Edit(ctx context.Context, id uint, in *entity.AccountSiteLink) (err error)
		Detail(ctx context.Context, id uint) (out *v1.AccountSiteLinkDetailRes, err error)
		List(ctx context.Context, in *v1.AccountSiteLinkListReq) (ret map[uint]*v1.AccountSiteLinkVo, err error)
		GetSiteIDsByAccount(ctx context.Context, accountID uint) (rets []uint, err error)
		GetGroupIDsByAccount(ctx context.Context, accountID uint) (rets []uint, err error)
		IsIdExist(ctx context.Context, id uint) (bool, error)
	}
)

var (
	localAccountSiteLink IAccountSiteLink
)

func AccountSiteLink() IAccountSiteLink {
	if localAccountSiteLink == nil {
		panic("implement not found for interface IAccountSiteLink, forgot register?")
	}
	return localAccountSiteLink
}

func RegisterAccountSiteLink(i IAccountSiteLink) {
	localAccountSiteLink = i
}
