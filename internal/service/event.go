// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IEvent interface {
		// 验证码获取事件
		OnGetCaptcha(ctx context.Context)
		// 登录失败事件
		OnLoginFail(ctx context.Context)
		// 登录成功事件
		OnLogin(ctx context.Context, account string, status int)
		// 退出事件
		OnLogout(ctx context.Context, account string, status int)
		// OnModifyPassword 修改密码事件
		OnModifyPassword(ctx context.Context, account string, status int)
		PublishConfigUpdate(ctx context.Context, channelName string) error
	}
)

var (
	localEvent IEvent
)

func Event() IEvent {
	if localEvent == nil {
		panic("implement not found for interface IEvent, forgot register?")
	}
	return localEvent
}

func RegisterEvent(i IEvent) {
	localEvent = i
}
