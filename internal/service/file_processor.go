// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IFileProcessor interface {
		// 导入需要把数据放在第三行，第一行第二行是中文和英文字段含义
		Import(ctx context.Context, file *ghttp.UploadFile, dest interface{}) (err error)
		// Export2Excel 导出为excel，前端即时下载
		Export2Excel(ctx context.Context, data interface{}) (err error)
		// Export2Excel 导出为excel，前端即时下载
		Export2ExcelMoreSheet(ctx context.Context, data interface{}, data2 interface{}) (err error)
		Export2Txt(ctx context.Context, data []string) (err error)
		// Export2ExcelCn 导出为excel，前端即时下载
		Export2ExcelCn(ctx context.Context, data interface{}) (err error)
	}
)

var (
	localFileProcessor IFileProcessor
)

func FileProcessor() IFileProcessor {
	if localFileProcessor == nil {
		panic("implement not found for interface IFileProcessor, forgot register?")
	}
	return localFileProcessor
}

func RegisterFileProcessor(i IFileProcessor) {
	localFileProcessor = i
}
