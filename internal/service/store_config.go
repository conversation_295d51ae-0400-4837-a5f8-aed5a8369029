// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
)

type (
	IStoreConfig interface {
		// GetTypesOption 存储类型列表
		GetTypesOption(ctx context.Context) []model.SelectOption
		// BaseOne 查询单个存储配置的基础配置
		BaseOne(ctx context.Context) (out *entity.StoreBaseConfig, err error)
		// BaseEdit 修改单个存储配置的基础配置
		BaseEdit(ctx context.Context, in model.StoreBaseConfigEdit) (err error)
		// CloudOne 查看指定类型的存储配置-云配置
		CloudOne(ctx context.Context, cloudType string) (out *model.StoreCloudConfig, err error)
		// CloudOneEdit 修改存储配置-云配置
		CloudOneEdit(ctx context.Context, in model.StoreCloudConfigEditInput) (err error)
		// CloudOneAdd 添加存储配置-云配置
		CloudOneAdd(ctx context.Context, in model.StoreCloudConfigAddInput) (id uint, err error)
	}
)

var (
	localStoreConfig IStoreConfig
)

func StoreConfig() IStoreConfig {
	if localStoreConfig == nil {
		panic("implement not found for interface IStoreConfig, forgot register?")
	}
	return localStoreConfig
}

func RegisterStoreConfig(i IStoreConfig) {
	localStoreConfig = i
}
