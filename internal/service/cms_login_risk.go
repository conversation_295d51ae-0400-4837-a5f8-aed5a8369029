// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
)

type (
	ICmsLoginRisk interface {
		Create(ctx context.Context, in *entity.CmsLoginRisk) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Edit(ctx context.Context, id uint, in *entity.CmsLoginRisk) (err error)
		Detail(ctx context.Context, id uint) (out *v1.CmsLoginRiskDetailRes, err error)
		List(ctx context.Context, in *v1.CmsLoginRiskListReq) (out *v1.CmsLoginRiskListRes, err error)
		IsIdExist(ctx context.Context, id uint) (bool, error)
	}
)

var (
	localCmsLoginRisk ICmsLoginRisk
)

func CmsLoginRisk() ICmsLoginRisk {
	if localCmsLoginRisk == nil {
		panic("implement not found for interface ICmsLoginRisk, forgot register?")
	}
	return localCmsLoginRisk
}

func RegisterCmsLoginRisk(i ICmsLoginRisk) {
	localCmsLoginRisk = i
}
