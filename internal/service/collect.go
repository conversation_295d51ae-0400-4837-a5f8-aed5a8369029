// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ICollect interface {
		FootballTeamList(ctx context.Context, req *v1.CollectFootballTeamListReq) (res *v1.CollectFootballTeamListRes, err error)
		FootballTeamEdit(ctx context.Context, req *v1.CollectFootballTeamEditReq) (res *v1.EmptyDataRes, err error)
		BasketballTeamList(ctx context.Context, req *v1.CollectBasketballTeamListReq) (res *v1.CollectBasketballTeamListRes, err error)
		BasketballTeamEdit(ctx context.Context, req *v1.CollectBasketballTeamEditReq) (res *v1.EmptyDataRes, err error)
		SnookerTeamList(ctx context.Context, req *v1.CollectSnookerTeamListReq) (res *v1.CollectSnookerTeamListRes, err error)
		SnookerTeamEdit(ctx context.Context, req *v1.CollectSnookerTeamEditReq) (res *v1.EmptyDataRes, err error)
		GetTeamIdByName(ctx context.Context, name string) (id int, err error)
	}
)

var (
	localCollect ICollect
)

func Collect() ICollect {
	if localCollect == nil {
		panic("implement not found for interface ICollect, forgot register?")
	}
	return localCollect
}

func RegisterCollect(i ICollect) {
	localCollect = i
}
