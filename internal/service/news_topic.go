// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	INewsTopic interface {
		// 新增
		Add(ctx context.Context, req *v1.NewsTopicAddReq) error
		// 修改
		Edit(ctx context.Context, req *v1.NewsTopicEditReq) error
		// 详情
		Info(ctx context.Context, req *v1.NewsTopicInfoReq) (out *v1.NewsTopicInfoRes, err error)
		// 列表
		List(ctx context.Context, req *v1.NewsTopicListReq) (out *v1.NewsTopicListRes, err error)
		// 删除
		Delete(ctx context.Context, req *v1.NewsTopicDeleteReq) error
	}
)

var (
	localNewsTopic INewsTopic
)

func NewsTopic() INewsTopic {
	if localNewsTopic == nil {
		panic("implement not found for interface INewsTopic, forgot register?")
	}
	return localNewsTopic
}

func RegisterNewsTopic(i INewsTopic) {
	localNewsTopic = i
}
