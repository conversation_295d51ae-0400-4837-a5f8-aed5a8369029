// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IUtility interface {
		// ----Captcha
		GenerateCaptcha() (id string, b64s string, err error)
		VerifyCaptcha(id string, answer string, clear bool) (match bool)
		VerifySmsCaptcha(areaCode string, phoneNum string, smsCaptcha string, clear bool) (match bool)
		// ---- JWT
		GenerateJWT(id uint, ty string, secretKey string) (token string, err error)
		ParseJWT(token string, secretKey string) (*model.JWTD, error)
		// ---- Password
		HashPassword(password string) (string, error)
		CheckPasswordHash(password string, hash string) bool
		// ----Client Ip
		// todo 需要做 代理 ip，ip段过滤
		// $remote_addr、$proxy_add_x_forwarded_for、$http_x_forwarded_for
		// X-Forwarded-For: client, proxy1, proxy2
		// X-Real-IP是否为指定的代理ip或ip段，是则到X-Forwarded-For里的，否则取X-Real-IP
		/*
		   	ip := net.ParseIP("***********")
		   	_, ipNet, _ := net.ParseCIDR("***********/24")

		   	if ipNet.Contains(ip) {
		   		fmt.Println("IP is in subnet")
		   	} else {
		   		fmt.Println("IP is not in subnet")
		   	}

		   	start = net.ParseIP("*************")
		       end = net.ParseIP("*************")
		   	input := net.ParseIP(ip)
		   	//input.To4() == nil//判断是否为IP4
		   	if bytes.Compare(input, start) >= 0 && bytes.Compare(input, end) <= 0 {
		   		fmt.Println("IP is in subnet")
		       }
		*/
		/*
			todo：要与运维约定好ip的获取方式
		*/
		GetClientIp(r *ghttp.Request) string
		// IsInIps 检查给定的 IP 是否在一组 IP 地址或 IP 范围中（如 x.x.x.x~x.x.x.x 或 x.x.x.x/x）。
		// 支持使用通配符 "*" 匹配任意 IP。
		IsInIps(ips []string, ip string) bool
		// 查找字符串是否在切片及在切片中的位置
		StringSliceFind(slice []string, val string) (int, bool)
		// 查找int是否在切片及切片中的位置
		IntSliceFind(slice []int, val int) (int, bool)
		// 检测服务器自身网络是否异常
		NetWorkStatus() bool
		// ctx中获取self
		GetSelf(ctx context.Context) (selfCtx *entity.Account, err error)
		GetAccountRole(ctx context.Context) (roleInfo *model.AccountRole, err error)
		GetRolePermission(ctx context.Context) (ret *model.RolePermissionInfo, err error)
		// ctx中获取self
		GetApiNode(ctx context.Context) (nodes []string, apiUrl string, err error)
		// GetSelfAccount 当前账号
		GetSelfAccount(ctx context.Context) (account string)
		// GetCurrentLanguageCode 获取当前会话的语言编码
		GetCurrentLanguageCode(ctx context.Context) (languageCode string)
		// 比较两个浮点数是否相等（在配置的误差精度内）
		IsEqualFloat64(a float64, b float64) (isEqual bool)
		// todo:生成订单号
		GenNbbId() (id string, err error)
		GetIPRegion(ctx context.Context, ip string) (region string)
		// 获取host
		GetHost(ctx context.Context) (host string)
		// IsFieldValueExist 检查一个表中是否有指定字段的值；前置条件：表名和字段名必须正确
		IsFieldValueExist(ctx context.Context, in model.IsFieldValueExistInput) (bool, error)
		// CheckTimeSecDiff 判断两个时间差是否在有效时间秒内
		CheckTimeSecDiff(startTime string, endTime string, effSec float64) bool
		// GenerateSupplierUniqueNumber 生成唯一的编号
		GenerateSupplierUniqueNumber(ctx context.Context, in model.GenerateSupplierUniqueNumberInput) (res string)
		// Md5Encode 使用MD5算法对传入的字符串进行加密，并返回加密后的字符串
		Md5Encode(str string) string
		// Base64Encode 使用Base64算法对传入的字符串进行加密，并返回加密后的字符串
		Base64Encode(str string) string
		// ComputeHmac256 计算Hmac256
		ComputeHmac256(message string, secret string) string
		GetIPCity(ctx context.Context, ip string) (addr string)
		// 函数名为 IsAlphaNumeric，接收一个字符串参数 str，返回一个布尔值
		IsAlphaNumeric(str string) bool
		// GetPinyin 获取中文字符串的拼音
		GetPinyin(text string) (py string)
		GetPinyinFirstLetter(text string) (py string)
		ExtractAndGenerate(content string, typ int) (string, string)
		GenerateRandomLetters(n int) string
		// GenerateRandomNumbers 生成指定数量的随机数字
		GenerateRandomNumbers(n int) string
		// 获取今天0点到24点的毫秒时间戳
		GetTodayTimestamps() (startOfDay int64, endOfDay int64)
		// 获取昨天的0点到24点的毫秒时间戳
		GetYesterdayTimestamps() (startOfYesterday int64, endOfYesterday int64)
		// GetMonthTimestamps 获取本月的开始和结束时间的毫秒时间戳
		GetMonthTimestamps() (startOfMonthMillis int64, endOfMonthMillis int64)
		RemoveTrailingLeadingSlash(str string) string
		// RemoveSpacesAndSymbols 函数用于去除字符串中的空格和符号
		RemoveSpacesAndSymbols(input string) string
		// 解析自定义的内容
		ParseCustomContent(content string, params ...string) (c string)
		FormatPath(str string) string
		// CleanURL 去除 URL 中连续的多个斜杠，保留一个
		CleanURL(url string) string
		// AddWWWPrefix 如果url没有www前缀，则加上www.
		AddWWWPrefix(url string) string
		// IsToday 是否今天
		IsToday(timestamp int64) bool
		// MinInt 返回两个整数中的较小值
		MinInt(a int, b int) int
		CutString(content string, num int) (extractedChars string)
		RemoveEmojis(input string) string
		EncryptPassword(secret string, cryptoKey string) (string, error)
		DecryptPassword(aesPwd string, cryptoKey string) (string, error)
	}
)

var (
	localUtility IUtility
)

func Utility() IUtility {
	if localUtility == nil {
		panic("implement not found for interface IUtility, forgot register?")
	}
	return localUtility
}

func RegisterUtility(i IUtility) {
	localUtility = i
}
