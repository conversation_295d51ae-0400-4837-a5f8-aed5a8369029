// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
)

type (
	IAccount interface {
		Create(ctx context.Context, in *entity.Account) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Edit(ctx context.Context, password *string, auditPassword *string, in *entity.Account) (err error)
		GetGoogleAuthSecret() (googleSecret string)
		Detail(ctx context.Context, id uint) (out *v1.AccountDetailRes, err error)
		GetByAccountName(ctx context.Context, account string) (in *entity.Account, err error)
		SetStatus(ctx context.Context, id uint, auditPassword string, isAffect int) (err error)
		List(ctx context.Context, in *v1.AccountListReq) (out *v1.AccountListRes, err error)
		MapIDName(ctx context.Context) (id2Name map[uint]string, err error)
		IsAccountExist(ctx context.Context, account string, excludeIds []uint) (bool, error)
		IsIdExist(ctx context.Context, id uint) (bool, error)
		IsRoleUsed(ctx context.Context, roleId uint) (bool, error)
		ChildID(ctx context.Context, id uint) (out []uint, err error)
		GetAdminFromMasterCache(ctx context.Context, id uint) (admin *entity.Account, err error)
		UpdatePassword(ctx context.Context, id uint, newPassword string) (err error)
		SetOnlineStatus(ctx context.Context, id uint, status int) (err error)
		// 获取所有管理员账号
		GetAdminAccount(ctx context.Context) (accountId []uint)
		SetGoogleAuth(ctx context.Context, id uint, auditPassword string, isAffect int) (err error)
		GetByAccount(ctx context.Context, account string) (out *entity.Account, err error)
		SetTmpl(ctx context.Context, req *v1.AccountSetTemplateReq) (res *v1.EmptyDataRes, err error)
	}
)

var (
	localAccount IAccount
)

func Account() IAccount {
	if localAccount == nil {
		panic("implement not found for interface IAccount, forgot register?")
	}
	return localAccount
}

func RegisterAccount(i IAccount) {
	localAccount = i
}
