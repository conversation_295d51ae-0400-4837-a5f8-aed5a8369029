// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	model "gtcms/internal/model/admin"
)

type (
	IAccountNodeConfig interface {
		GetNodeLevelInfo() (rets []*model.NodeInfo, err error)
		IsNodeIdExist(ctx context.Context, id uint) (bool, error)
		PushLog(ctx context.Context, input *model.AdminModifyInput) (err error)
		FillLogFromContext(ctx context.Context, item *model.ChannelElemModifyLog) (err error)
	}
)

var (
	localAccountNodeConfig IAccountNodeConfig
)

func AccountNodeConfig() IAccountNodeConfig {
	if localAccountNodeConfig == nil {
		panic("implement not found for interface IAccountNodeConfig, forgot register?")
	}
	return localAccountNodeConfig
}

func RegisterAccountNodeConfig(i IAccountNodeConfig) {
	localAccountNodeConfig = i
}
