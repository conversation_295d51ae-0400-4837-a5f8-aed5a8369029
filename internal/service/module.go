// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
)

type (
	IModule interface {
		List(ctx context.Context, req *v1.ModuleListReq) (res *v1.ModuleListRes, err error)
		Add(ctx context.Context, req *v1.ModuleAddReq) (res *v1.ModuleAddRes, err error)
		Edit(ctx context.Context, req *v1.ModuleEditReq) (res *v1.EmptyDataRes, err error)
		Delete(ctx context.Context, req *v1.ModuleDeleteReq) (res *v1.EmptyDataRes, err error)
		Options(ctx context.Context, req *v1.ModuleOptionsReq) (options []model.SelectOption, err error)
		One(ctx context.Context, id string) (item *entity.Module, err error)
	}
)

var (
	localModule IModule
)

func Module() IModule {
	if localModule == nil {
		panic("implement not found for interface IModule, forgot register?")
	}
	return localModule
}

func RegisterModule(i IModule) {
	localModule = i
}
