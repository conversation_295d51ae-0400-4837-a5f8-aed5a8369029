// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
	entity "gtcms/internal/model/entity/admin"
)

type (
	ISensitive interface {
		Create(ctx context.Context, in *entity.Sensitive) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Edit(ctx context.Context, id uint, in *entity.Sensitive) (err error)
		Detail(ctx context.Context, id uint) (out *v1.SensitiveDetailRes, err error)
		List(ctx context.Context, in *v1.SensitiveListReq) (out *v1.SensitiveListRes, err error)
		IsIdExist(ctx context.Context, id uint) (bool, error)
		Options(ctx context.Context) (options []model.SelectOption, err error)
	}
)

var (
	localSensitive ISensitive
)

func Sensitive() ISensitive {
	if localSensitive == nil {
		panic("implement not found for interface ISensitive, forgot register?")
	}
	return localSensitive
}

func RegisterSensitive(i ISensitive) {
	localSensitive = i
}
