// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IVideoCategory interface {
		// 新增
		Add(ctx context.Context, req *v1.VideoCategoryAddReq) error
		// 修改
		Edit(ctx context.Context, req *v1.VideoCategoryEditReq) error
		// 详情
		Info(ctx context.Context, req *v1.VideoCategoryInfoReq) (out *v1.VideoCategoryInfoRes, err error)
		// 列表
		List(ctx context.Context, req *v1.VideoCategoryListReq) (out *v1.VideoCategoryListRes, err error)
		// 删除
		Delete(ctx context.Context, req *v1.VideoCategoryDeleteReq) error
	}
)

var (
	localVideoCategory IVideoCategory
)

func VideoCategory() IVideoCategory {
	if localVideoCategory == nil {
		panic("implement not found for interface IVideoCategory, forgot register?")
	}
	return localVideoCategory
}

func RegisterVideoCategory(i IVideoCategory) {
	localVideoCategory = i
}
