// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IHeartbeat interface {
		// CheckSiteHeartbeat 检查网站心跳
		// 在后台服务器的hosts，本地解析66good.com到前台服务器ip
		// 这样只需要访问66good.com即可知道前台服务器的状态
		CheckSiteHeartbeat(ctx context.Context) float64
	}
)

var (
	localHeartbeat IHeartbeat
)

func Heartbeat() IHeartbeat {
	if localHeartbeat == nil {
		panic("implement not found for interface IHeartbeat, forgot register?")
	}
	return localHeartbeat
}

func RegisterHeartbeat(i IHeartbeat) {
	localHeartbeat = i
}
