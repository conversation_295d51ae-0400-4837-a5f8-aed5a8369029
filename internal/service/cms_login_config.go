// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
)

type (
	ICmsLoginConfig interface {
		Edit(ctx context.Context, id uint, in *entity.CmsLoginConfig) (err error)
		// Detail 查询登录配置详情
		Detail(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error)
		// DetailCache 查询登录配置详情(有缓存)
		DetailCache(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error)
		SynQuranJuz(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error)
		SynTahlil(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error)
		SynDoa(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error)
		SynWirid(ctx context.Context, id int) (out *v1.CmsLoginConfigDetailRes, err error)
	}
)

var (
	localCmsLoginConfig ICmsLoginConfig
)

func CmsLoginConfig() ICmsLoginConfig {
	if localCmsLoginConfig == nil {
		panic("implement not found for interface ICmsLoginConfig, forgot register?")
	}
	return localCmsLoginConfig
}

func RegisterCmsLoginConfig(i ICmsLoginConfig) {
	localCmsLoginConfig = i
}
