// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IMiddleware interface {
		// 2.5版本兼容:Ctx传播给异步流程或者保持和之前逻辑兼容
		NeverDoneCtx(r *ghttp.Request)
		// 记录访问请求头，请求体
		LogRequest(r *ghttp.Request)
		// 多语言
		I18n(r *ghttp.Request)
		// 身份认证
		Auth(r *ghttp.Request)
		// 记录接口、域名、ip访问次数 (定时任务每天0点清除集合)
		Counter(r *ghttp.Request)
		// 拒绝黑名单ip
		CheckBlackIp(r *ghttp.Request)
		HandlerResponse(r *ghttp.Request)
		SetCORSOptions(r *ghttp.Request)
		AccessControlCheck(r *ghttp.Request)
	}
)

var (
	localMiddleware IMiddleware
)

func Middleware() IMiddleware {
	if localMiddleware == nil {
		panic("implement not found for interface IMiddleware, forgot register?")
	}
	return localMiddleware
}

func RegisterMiddleware(i IMiddleware) {
	localMiddleware = i
}
