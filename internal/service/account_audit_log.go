// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
)

type (
	IAccountAuditLog interface {
		ListModifyLog(ctx context.Context, in *v1.AccountAuditLogReq) (out *v1.AccountAuditLogRes, err error)
		GetNodeLevelInfo() (rets []*model.NodeInfo, err error)
		IsNodeIdExist(ctx context.Context, id uint) (bool, error)
		PushLog(ctx context.Context, input *model.AdminModifyInput) (err error)
		FillLogFromContext(ctx context.Context, item *model.ChannelElemModifyLog) (err error)
	}
)

var (
	localAccountAuditLog IAccountAuditLog
)

func AccountAuditLog() IAccountAuditLog {
	if localAccountAuditLog == nil {
		panic("implement not found for interface IAccountAuditLog, forgot register?")
	}
	return localAccountAuditLog
}

func RegisterAccountAuditLog(i IAccountAuditLog) {
	localAccountAuditLog = i
}
