// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ILandmark interface {
		TypeList(ctx context.Context, req *v1.LandmarkTypeListReq) (out *v1.LandmarkTypeListRes, err error)
		TypeAdd(ctx context.Context, req *v1.LandmarkTypeCreateReq) (out *v1.LandmarkTypeCreateRes, err error)
		TypeEdit(ctx context.Context, req *v1.LandmarkTypeEditReq) (out *v1.LandmarkTypeEditRes, err error)
		TypeOne(ctx context.Context, req *v1.LandmarkTypeOneReq) (res *v1.LandmarkTypeOneRes, err error)
		TypeDelete(ctx context.Context, req *v1.LandmarkTypeDeleteReq) (out *v1.LandmarkTypeDeleteRes, err error)
		TypeOptions(ctx context.Context, req *v1.LandmarkTypeOptionsReq) (out *v1.LandmarkTypeOptionsRes, err error)
		List(ctx context.Context, req *v1.LandmarkListReq) (out *v1.LandmarkListRes, err error)
		Add(ctx context.Context, req *v1.LandmarkCreateReq) (out *v1.LandmarkCreateRes, err error)
		Edit(ctx context.Context, req *v1.LandmarkEditReq) (out *v1.LandmarkEditRes, err error)
		One(ctx context.Context, req *v1.LandmarkOneReq) (res *v1.LandmarkOneRes, err error)
		Delete(ctx context.Context, req *v1.LandmarkDeleteReq) (out *v1.LandmarkDeleteRes, err error)
	}
)

var (
	localLandmark ILandmark
)

func Landmark() ILandmark {
	if localLandmark == nil {
		panic("implement not found for interface ILandmark, forgot register?")
	}
	return localLandmark
}

func RegisterLandmark(i ILandmark) {
	localLandmark = i
}
