// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IAccountLoginMgr interface {
		SignIn(ctx context.Context, in *v1.LoginInReq) (token string, err error)
		SignOut(ctx context.Context, in *v1.LoginOutReq) (err error)
		ListLoginLog(ctx context.Context, in *v1.LoginLogReq) (out *v1.LoginLogRes, err error)
		ModifyPassword(ctx context.Context, req *v1.ModifyPasswordReq) (err error)
	}
)

var (
	localAccountLoginMgr IAccountLoginMgr
)

func AccountLoginMgr() IAccountLoginMgr {
	if localAccountLoginMgr == nil {
		panic("implement not found for interface IAccountLoginMgr, forgot register?")
	}
	return localAccountLoginMgr
}

func RegisterAccountLoginMgr(i IAccountLoginMgr) {
	localAccountLoginMgr = i
}
