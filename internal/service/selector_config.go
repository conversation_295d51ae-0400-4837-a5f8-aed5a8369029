// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	entity "gtcms/internal/model/entity/admin"
)

type (
	ISelectorConfig interface {
		Create(ctx context.Context, in *entity.SelectorConfigContent) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Edit(ctx context.Context, id uint, in *entity.SelectorConfigContent) (err error)
		Detail(ctx context.Context, id uint) (out *v1.SelectorConfigDetailRes, err error)
		List(ctx context.Context, in *v1.SelectorConfigListReq) (out *v1.SelectorConfigListRes, err error)
		IsIdExist(ctx context.Context, id uint) (bool, error)
	}
)

var (
	localSelectorConfig ISelectorConfig
)

func SelectorConfig() ISelectorConfig {
	if localSelectorConfig == nil {
		panic("implement not found for interface ISelectorConfig, forgot register?")
	}
	return localSelectorConfig
}

func RegisterSelectorConfig(i ISelectorConfig) {
	localSelectorConfig = i
}
