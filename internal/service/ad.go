// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IAd interface {
		NginxWebsList(ctx context.Context, reqp *v1.NginxWebsReq) (res *v1.NginxWebsRes, err error)
		NginxWebOne(oneUrl string) (res *v1.NginxWebsRes, err error)
		NginxOverallList(ctx context.Context, reqp *v1.NginxOverallReq) (res *v1.NginxOverallRes, err error)
		NginxSeriesList(ctx context.Context, reqp *v1.NginxSeriesReq) (res *v1.NginxSeriesRes, err error)
		// nginx access record
		NginxList(ctx context.Context, req *v1.NginxReq) (res *v1.NginxRes, err error)
	}
)

var (
	localAd IAd
)

func Ad() IAd {
	if localAd == nil {
		panic("implement not found for interface IAd, forgot register?")
	}
	return localAd
}

func RegisterAd(i IAd) {
	localAd = i
}
