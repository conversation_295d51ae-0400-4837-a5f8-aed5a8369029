// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	model "gtcms/internal/model/admin"
)

type (
	IRoleV2 interface {
		Create(ctx context.Context, in *v1.RoleMgrAddReq) (err error)
		Edit(ctx context.Context, in *v1.RoleMgrEditReq) (err error)
		Delete(ctx context.Context, in *v1.RoleMgrDeleteReq) (err error)
		List(ctx context.Context, in *v1.RoleMgrListReq) (out *v1.RoleMgrListRes, err error)
		Detail(ctx context.Context, id uint) (out v1.RoleConfig, err error)
		AccessCheck(ctx context.Context, roleId uint, nodePath string, url string) (isPassed bool, fieldPaths []string, err error)
		GetPermissionIds(ctx context.Context, roleId uint) (permIds []uint, err error)
		GetPermissionIdSensitive(ctx context.Context, roleId uint) ([]uint, map[uint]string)
		Unique(intSlice []string) []string
		Contain(ctx context.Context, subs []*v1.PermissionItem, item *v1.PermissionItem) bool
		Options(ctx context.Context) (options []model.SelectOption, err error)
		MapIDName(ctx context.Context) (id2Name map[uint]string, err error)
	}
)

var (
	localRoleV2 IRoleV2
)

func RoleV2() IRoleV2 {
	if localRoleV2 == nil {
		panic("implement not found for interface IRoleV2, forgot register?")
	}
	return localRoleV2
}

func RegisterRoleV2(i IRoleV2) {
	localRoleV2 = i
}
