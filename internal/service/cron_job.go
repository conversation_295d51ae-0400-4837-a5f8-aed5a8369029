// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	ICronJob interface {
		Daemon(ctx context.Context)
	}
)

var (
	localCronJob ICronJob
)

func CronJob() ICronJob {
	if localCronJob == nil {
		panic("implement not found for interface ICronJob, forgot register?")
	}
	return localCronJob
}

func RegisterCronJob(i ICronJob) {
	localCronJob = i
}
