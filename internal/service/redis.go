// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

type (
	IRedis interface {
		// **存储单个 key**
		Set(key string, value interface{}) error
		// **获取单个 key**
		Get(key string, obj interface{}) error
		// **批量存储**
		MSet(data map[string]interface{}) error
		Del(keys ...string) error
	}
)

var (
	localRedis IRedis
)

func Redis() IRedis {
	if localRedis == nil {
		panic("implement not found for interface IRedis, forgot register?")
	}
	return localRedis
}

func RegisterRedis(i IRedis) {
	localRedis = i
}
