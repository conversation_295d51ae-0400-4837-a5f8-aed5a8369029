// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	model "gtcms/internal/model/admin"

	"github.com/gogf/gf/v2/container/gvar"
)

type (
	IDbCache interface {
		Register(classID string, u model.MemoryUpdater)
		DeleteDbCache(ctx context.Context, name string) (*gvar.Var, error)
		DeleteDbCacheGoFunc(ctx context.Context, name string)
		PublishUpdate(ctx context.Context, params model.MemoryUpdateMessage)
	}
)

var (
	localDbCache IDbCache
)

func DbCache() IDbCache {
	if localDbCache == nil {
		panic("implement not found for interface IDbCache, forgot register?")
	}
	return localDbCache
}

func RegisterDbCache(i IDbCache) {
	localDbCache = i
}
