// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
	"reflect"
)

type (
	IPermissionV2 interface {
		Create(ctx context.Context, in *v1.PermissionMgrAddReq) (err error)
		Edit(ctx context.Context, in *v1.PermissionMgrEditReq) (err error)
		Delete(ctx context.Context, in *v1.PermissionMgrDeleteReq) (err error)
		List(ctx context.Context, in *v1.PermissionMgrListReq) (out *v1.PermissionMgrListRes, err error)
		IsUrlOpen(ctx context.Context, url string) (isOpen bool, err error)
		GetIDByNodeUrl(ctx context.Context, ids []uint, nodePath string, url string) (permId uint, err error)
		GetSubs(ctx context.Context) (err error)
		Detail(ctx context.Context, id uint) (node v1.PermissionNode, err error)
		CheckRolePermissionCfg(ctx context.Context, cfg []*v1.RolePermissionCfg) error
		GetPermNames(ctx context.Context, ids []uint) (names []string, err error)
		GetPermTree(ctx context.Context) (nodeTree []*v1.PermissionNode, err error)
		MaskingRespPacket(ctx context.Context, reflectValue reflect.Value, fieldPath []string, index int, isIntegerMask *bool)
		MaskingRespPacket2(ctx context.Context, respItf interface{}, fieldPath []string)
	}
)

var (
	localPermissionV2 IPermissionV2
)

func PermissionV2() IPermissionV2 {
	if localPermissionV2 == nil {
		panic("implement not found for interface IPermissionV2, forgot register?")
	}
	return localPermissionV2
}

func RegisterPermissionV2(i IPermissionV2) {
	localPermissionV2 = i
}
