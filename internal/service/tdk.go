// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	ITdk interface {
		// 获取一个TDK

		// FastFill 从模板中获取快速填充的内容, 在不报错的情况下tdk的返回也可能为空.
		FastFill(ctx context.Context, category, belongId int) (title, desc, keywords string, err error)
	}
)

var (
	localTdk ITdk
)

func Tdk() ITdk {
	if localTdk == nil {
		panic("implement not found for interface ITdk, forgot register?")
	}
	return localTdk
}

func RegisterTdk(i ITdk) {
	localTdk = i
}
