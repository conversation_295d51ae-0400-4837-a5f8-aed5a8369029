// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	INewsCategory interface {
		// 新增
		Add(ctx context.Context, req *v1.NewsCategoryAddReq) error
		// 编辑
		Edit(ctx context.Context, req *v1.NewsCategoryEditReq) error
		// 详情
		Info(ctx context.Context, req *v1.NewsCategoryInfoReq) (out *v1.NewsCategoryInfoRes, err error)
		// 列表
		List(ctx context.Context, req *v1.NewsCategoryListReq) (out *v1.NewsCategoryListRes, err error)
		// 删除
		Delete(ctx context.Context, req *v1.NewsCategoryDeleteReq) error
	}
)

var (
	localNewsCategory INewsCategory
)

func NewsCategory() INewsCategory {
	if localNewsCategory == nil {
		panic("implement not found for interface INewsCategory, forgot register?")
	}
	return localNewsCategory
}

func RegisterNewsCategory(i INewsCategory) {
	localNewsCategory = i
}
