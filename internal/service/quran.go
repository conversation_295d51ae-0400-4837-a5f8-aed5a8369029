// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IQuran interface {
		SurahList(ctx context.Context, req *v1.SurahListReq) (res *v1.SurahListRes, err error)
		AyahList(ctx context.Context, req *v1.AyahListReq) (res *v1.AyahListRes, err error)
		JuzList(ctx context.Context, req *v1.JuzListReq) (res *v1.JuzListRes, err error)
	}
)

var (
	localQuran IQuran
)

func Quran() IQuran {
	if localQuran == nil {
		panic("implement not found for interface IQuran, forgot register?")
	}
	return localQuran
}

func RegisterQuran(i IQuran) {
	localQuran = i
}
