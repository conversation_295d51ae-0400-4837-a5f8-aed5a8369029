// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	INewsArticle interface {
		// 新增
		Add(ctx context.Context, req *v1.NewsArticleAddReq) error
		// 编辑
		Edit(ctx context.Context, req *v1.NewsArticleEditReq) error
		// 列表
		List(ctx context.Context, req *v1.NewsArticleListReq) (out *v1.NewsArticleListRes, err error)
		// 详情
		Info(ctx context.Context, req *v1.NewsArticleInfoReq) (out *v1.NewsArticleInfoRes, err error)
		// 删除
		Delete(ctx context.Context, req *v1.NewsArticleDeleteReq) error
		// 批量下线
		SetOffline(ctx context.Context, req *v1.NewsArticleSetOfflineReq) error
		// 批量上线
		SetOnline(ctx context.Context, req *v1.NewsArticleSetOnlineReq) error
		// 批量加入头条
		SetTop(ctx context.Context, req *v1.NewsArticleSetTopReq) error
		// 批量取消加入头条
		SetNotTop(ctx context.Context, req *v1.NewsArticleSetNotTopReq) error
		// 批量推荐
		SetRecommend(ctx context.Context, req *v1.NewsArticleSetRecommendReq) error
		// 批量不推荐
		SetNotRecommend(ctx context.Context, req *v1.NewsArticleSetNotRecommendReq) error
		// 草稿箱
		Draft(ctx context.Context, req *v1.NewsArticleDraftReq) (out *v1.NewsArticleDraftRes, err error)
	}
)

var (
	localNewsArticle INewsArticle
)

func NewsArticle() INewsArticle {
	if localNewsArticle == nil {
		panic("implement not found for interface INewsArticle, forgot register?")
	}
	return localNewsArticle
}

func RegisterNewsArticle(i INewsArticle) {
	localNewsArticle = i
}
