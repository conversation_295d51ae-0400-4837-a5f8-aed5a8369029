// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IDomainScreen interface {
		List(ctx context.Context, req *v1.DomainScreenReq) (res *v1.DomainScreenRes, err error)
	}
)

var (
	localDomainScreen IDomainScreen
)

func DomainScreen() IDomainScreen {
	if localDomainScreen == nil {
		panic("implement not found for interface IDomainScreen, forgot register?")
	}
	return localDomainScreen
}

func RegisterDomainScreen(i IDomainScreen) {
	localDomainScreen = i
}
