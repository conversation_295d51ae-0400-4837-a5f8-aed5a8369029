// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IAi interface {
		// 通过接口生成文章
		Gen(ctx context.Context, req *v1.AiGenReq) (res *v1.AiGenRes, err error)
	}
)

var (
	localAi IAi
)

func Ai() IAi {
	if localAi == nil {
		panic("implement not found for interface IAi, forgot register?")
	}
	return localAi
}

func RegisterAi(i IAi) {
	localAi = i
}
