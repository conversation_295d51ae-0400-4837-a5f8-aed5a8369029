// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ISite interface {
		List(ctx context.Context, req *v1.SiteListReq) (res *v1.SiteListRes, err error)
		AddBatch(ctx context.Context, list []v1.SiteItem)
		Add(ctx context.Context, req *v1.SiteAddReq) (res *v1.SiteAddRes, err error)
		IncludeQuery(ctx context.Context, req *v1.SiteIncludeReq) (res *v1.SiteIncludeRes, err error)
		Edit(ctx context.Context, req *v1.SiteEditReq) (res *v1.EmptyDataRes, err error)
		Delete(ctx context.Context, req *v1.SiteDeleteReq) (res *v1.EmptyDataRes, err error)
		One(ctx context.Context, req *v1.SiteOneReq) (res *v1.SiteOneRes, err error)
		EditBatch(ctx context.Context, req *v1.SiteEditBatchReq) (res *v1.EmptyDataRes, err error)
		Import(ctx context.Context, req *v1.SiteImportReq) (res *v1.EmptyDataRes, err error)
		ExportDomain(ctx context.Context, req *v1.SiteExportDomainReq) (res *v1.EmptyDataRes, err error)
		ExportAll(ctx context.Context, req *v1.SiteExportReq) (res *v1.EmptyDataRes, err error)
		ValidCount(ctx context.Context) (webSt v1.StatWebsite, err error)
		CheckSiteGroupExist(ctx context.Context, siteID uint, groupID uint) (isExist bool, err error)
		GetAccessRanking(ctx context.Context) (res []*v1.RankSitePv, err error)
		EditTdkBatch(ctx context.Context, req *v1.SiteEditTdkBatchReq) (res *v1.EmptyDataRes, err error)
		EditSignalBatch(ctx context.Context, req *v1.SiteEditSignalBatchReq) (res *v1.EmptyDataRes, err error)
	}
)

var (
	localSite ISite
)

func Site() ISite {
	if localSite == nil {
		panic("implement not found for interface ISite, forgot register?")
	}
	return localSite
}

func RegisterSite(i ISite) {
	localSite = i
}
