// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IFaq interface {
		CateAdd(ctx context.Context, req *v1.CateCreateReq) (res *v1.CateCreateRes, err error)
		CateEdit(ctx context.Context, req *v1.CateEditeReq) (res *v1.CateEditRes, err error)
		CateDelete(ctx context.Context, req *v1.CateDeleteReq) (res *v1.CateDeleteRes, err error)
		CateOne(ctx context.Context, req *v1.CateOneReq) (res *v1.CateOneRes, err error)
		CateList(ctx context.Context, req *v1.CateListReq) (res *v1.CateListRes, err error)
		QuestionAdd(ctx context.Context, req *v1.QuestionCreateReq) (res *v1.QuestionCreateRes, err error)
		QuestionEdit(ctx context.Context, req *v1.QuestionEditReq) (res *v1.QuestionEditRes, err error)
		QuestionDelete(ctx context.Context, req *v1.QuestionDeleteReq) (res *v1.QuestionDeleteRes, err error)
		QuestionOne(ctx context.Context, req *v1.QuestionOneReq) (res *v1.QuestionOneRes, err error)
		QuestionList(ctx context.Context, req *v1.QuestionListReq) (res *v1.QuestionListRes, err error)
	}
)

var (
	localFaq IFaq
)

func Faq() IFaq {
	if localFaq == nil {
		panic("implement not found for interface IFaq, forgot register?")
	}
	return localFaq
}

func RegisterFaq(i IFaq) {
	localFaq = i
}
