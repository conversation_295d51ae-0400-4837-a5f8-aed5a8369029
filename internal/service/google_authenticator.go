// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

type (
	IGoogleAuthenticator interface {
		// 生成16位密钥
		CreateSecret() string
		// 获取验证码
		GetCode(secretKey string, epochSeconds int64) (code int32)
		// 验证,传入验证key和code代码，返回验证是否成功
		VerifyCode(secretKey string, code int32) bool
	}
)

var (
	localGoogleAuthenticator IGoogleAuthenticator
)

func GoogleAuthenticator() IGoogleAuthenticator {
	if localGoogleAuthenticator == nil {
		panic("implement not found for interface IGoogleAuthenticator, forgot register?")
	}
	return localGoogleAuthenticator
}

func RegisterGoogleAuthenticator(i IGoogleAuthenticator) {
	localGoogleAuthenticator = i
}
