// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	model "gtcms/internal/model/admin"
)

type (
	IAttachment interface {
		// AttachmentCount 获取指定条件的附件数量
		AttachmentCount(ctx context.Context, in model.AttachmentCountInput) (count int, err error)
		// List 附件列表
		List(ctx context.Context, in model.AttachmentListInput) (out *model.AttachmentListOutput, err error)
		// Add 附件新增
		Add(ctx context.Context, in model.AttachmentAddInput) (id uint, err error)
		// Edit 附件编辑
		Edit(ctx context.Context, in model.AttachmentEditInput) (err error)
		// Delete 附件删除
		Delete(ctx context.Context, in model.AttachmentDeleteInput) (err error)
		// CategoryOne 获取指定条件的分类
		CategoryOne(ctx context.Context, in model.CategoryOneInput) (data *model.AttachmentCategory, err error)
		// CategoryCount 获取指定条件的分类数量
		CategoryCount(ctx context.Context, in model.AttachmentCategoryCountInput) (count int, err error)
		// CategoryList 附件分类列表
		CategoryList(ctx context.Context, in model.AttachmentCategoryListInput) (out *model.AttachmentCategoryListOutput, err error)
		// CategoryAdd 附件分类新增
		CategoryAdd(ctx context.Context, in model.AttachmentCategoryAddInput) (id uint, err error)
		// CategoryEdit 附件分类编辑
		CategoryEdit(ctx context.Context, in model.AttachmentCategoryEditInput) (err error)
		// CategoryDelete 附件分类删除
		CategoryDelete(ctx context.Context, in model.AttachmentCategoryDeleteInput) (err error)
	}
)

var (
	localAttachment IAttachment
)

func Attachment() IAttachment {
	if localAttachment == nil {
		panic("implement not found for interface IAttachment, forgot register?")
	}
	return localAttachment
}

func RegisterAttachment(i IAttachment) {
	localAttachment = i
}
