// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IVideoTopic interface {
		// Add 新增
		Add(ctx context.Context, req *v1.VideoTopicAddReq) error
		// Edit 编辑
		Edit(ctx context.Context, req *v1.VideoTopicEditReq) error
		// Info 详情
		Info(ctx context.Context, req *v1.VideoTopicInfoReq) (out *v1.VideoTopicInfoRes, err error)
		// List 列表
		List(ctx context.Context, req *v1.VideoTopicListReq) (out *v1.VideoTopicListRes, err error)
		// Delete 删除
		Delete(ctx context.Context, req *v1.VideoTopicDeleteReq) error
	}
)

var (
	localVideoTopic IVideoTopic
)

func VideoTopic() IVideoTopic {
	if localVideoTopic == nil {
		panic("implement not found for interface IVideoTopic, forgot register?")
	}
	return localVideoTopic
}

func RegisterVideoTopic(i IVideoTopic) {
	localVideoTopic = i
}
