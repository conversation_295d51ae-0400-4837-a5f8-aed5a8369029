// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IHajiJadwal interface {
		DescriptionGet(ctx context.Context, req *v1.HajiJadwalDescriptionGetReq) (out *v1.HajiJadwalDescriptionGetRes, err error)
		DescriptionSave(ctx context.Context, req *v1.HajiJadwalDescriptionSaveReq) (out *v1.HajiJadwalDescriptionSaveRes, err error)
		List(ctx context.Context, req *v1.HajiJadwalListReq) (out *v1.HajiJadwalListRes, err error)
		Add(ctx context.Context, req *v1.HajiJadwalCreateReq) (out *v1.HajiJadwalCreateRes, err error)
		Edit(ctx context.Context, req *v1.HajiJadwalEditReq) (out *v1.HajiJadwalEditRes, err error)
		One(ctx context.Context, req *v1.HajiJadwalOneReq) (res *v1.HajiJadwalOneRes, err error)
		Delete(ctx context.Context, req *v1.HajiJadwalDeleteReq) (out *v1.HajiJadwalDeleteRes, err error)
	}
)

var (
	localHajiJadwal IHajiJadwal
)

func HajiJadwal() IHajiJadwal {
	if localHajiJadwal == nil {
		panic("implement not found for interface IHajiJadwal, forgot register?")
	}
	return localHajiJadwal
}

func RegisterHajiJadwal(i IHajiJadwal) {
	localHajiJadwal = i
}
