// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	INews interface {
		List(ctx context.Context, req *v1.NewsListReq) (res *v1.NewsListRes, err error)
		Add(ctx context.Context, req *v1.NewsAddReq, auto bool) (res *v1.NewsAddRes, err error)
		Edit(ctx context.Context, req *v1.NewsEditReq) (res *v1.EmptyDataRes, err error)
		Delete(ctx context.Context, req *v1.NewsDeleteReq) (res *v1.EmptyDataRes, err error)
		One(ctx context.Context, req *v1.NewsOneReq) (res *v1.NewsOneRes, err error)
		UpdateStatus(ctx context.Context, req *v1.NewsUpdateStatusReq) (res *v1.EmptyDataRes, err error)
		ImportTxt(ctx context.Context, req *v1.NewsImportTxtReq) (res *v1.EmptyDataRes, err error)
		// 自动发布新闻
		// 采集的文章只发布中文站点和未过期域名的站点
		// ai生成的文章可发布各种语言站点
	}
)

var (
	localNews INews
)

func News() INews {
	if localNews == nil {
		panic("implement not found for interface INews, forgot register?")
	}
	return localNews
}

func RegisterNews(i INews) {
	localNews = i
}
