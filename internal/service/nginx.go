// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	INginx interface {
		Update(ctx context.Context)
	}
)

var (
	localNginx INginx
)

func Nginx() INginx {
	if localNginx == nil {
		panic("implement not found for interface INginx, forgot register?")
	}
	return localNginx
}

func RegisterNginx(i INginx) {
	localNginx = i
}
