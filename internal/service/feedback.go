// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IFeedback interface {
		// 反馈列表
		FeedbackList(ctx context.Context, req *v1.FeedbackListReq) (res *v1.FeedbackListRes, err error)
		// 反馈详情
		FeedbackOne(ctx context.Context, req *v1.FeedbackOneReq) (res *v1.FeedbackOneRes, err error)
		// 删除反馈
		FeedbackDelete(ctx context.Context, req *v1.FeedbackDeleteReq) (res *v1.FeedbackDeleteRes, err error)
		// 处理反馈
		FeedbackComplete(ctx context.Context, req *v1.FeedbackCompleteReq) (res *v1.FeedbackCompleteRes, err error)
	}
)

var (
	localFeedback IFeedback
)

func Feedback() IFeedback {
	if localFeedback == nil {
		panic("implement not found for interface IFeedback, forgot register?")
	}
	return localFeedback
}

func RegisterFeedback(i IFeedback) {
	localFeedback = i
}
