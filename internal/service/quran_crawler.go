// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	IQuranCrawler interface {
		// Start 开始爬取古兰经数据
		Start(ctx context.Context, req *v1.QuranCrawlerStartReq) (res *v1.QuranCrawlerStartRes, err error)
		// Status 查询古兰经数据状态
		Status(ctx context.Context, req *v1.QuranCrawlerStatusReq) (res *v1.QuranCrawlerStatusRes, err error)
		// SuratList 获取章节列表
		SuratList(ctx context.Context, req *v1.QuranCrawlerSuratListReq) (res *v1.QuranCrawlerSuratListRes, err error)
		// SuratDetail 获取章节详情
		SuratDetail(ctx context.Context, req *v1.QuranCrawlerSuratDetailReq) (res *v1.QuranCrawlerSuratDetailRes, err error)
	}
)

var (
	localQuranCrawler IQuranCrawler
)

func QuranCrawler() IQuranCrawler {
	if localQuranCrawler == nil {
		panic("implement not found for interface IQuranCrawler, forgot register?")
	}
	return localQuranCrawler
}

func RegisterQuranCrawler(i IQuranCrawler) {
	localQuranCrawler = i
}
