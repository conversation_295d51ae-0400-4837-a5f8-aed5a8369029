package cmd

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
)

type cMain struct {
	g.Meta `name:"cms"`
}

type cMainInput struct {
	g.<PERSON>a `name:"cms" brief:"默认启动http" dc:"默认启动http"`
}

type cMainOutput struct {
}

// Index CMain 默认方法
func (c cMain) Index(ctx context.Context, in cMainInput) (out *cMainOutput, err error) {
	// 默认启动http命令的默认方法
	_, err = http.Index(ctx, cHttpInput{})
	return
}

func Run(ctx context.Context) {
	M, err := gcmd.NewFromObject(cMain{})
	if err != nil {
		panic(err)
	}
	err = M.AddObject(http)
	if err != nil {
		panic(err)
	}
	M.Run(ctx)
}
