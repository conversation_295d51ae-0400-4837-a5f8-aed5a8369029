package cmd

import (
	"context"
	"gtcms/internal/controller/hajiJadwal"
	"gtcms/internal/controller/landmark"
	"gtcms/internal/controller/newsArticle"
	"gtcms/internal/controller/newsCategory"
	"gtcms/internal/controller/newsTopic"
	"gtcms/internal/controller/quran"
	"gtcms/internal/controller/quranCrawler"
	"gtcms/internal/controller/video"
	"gtcms/internal/controller/videoCategory"
	"gtcms/internal/controller/videoTopic"
	"gtcms/internal/logic/faq"

	//"git.go123.dev/lib/golang/cacher.git"
	"gtcms/internal/consts"
	"gtcms/internal/controller/account"
	"gtcms/internal/controller/accountAuditLog"
	"gtcms/internal/controller/accountSiteLink"
	"gtcms/internal/controller/ad"
	"gtcms/internal/controller/ai"
	"gtcms/internal/controller/articleTransformationConfig"
	"gtcms/internal/controller/attachment"
	"gtcms/internal/controller/calendarMgr"
	"gtcms/internal/controller/cmsLoginConfig"
	"gtcms/internal/controller/cmsLoginRisk"
	"gtcms/internal/controller/homePage"
	"gtcms/internal/controller/keyword"
	"gtcms/internal/controller/label"
	"gtcms/internal/controller/loginMgr"
	"gtcms/internal/controller/module"
	"gtcms/internal/controller/news"
	"gtcms/internal/controller/noAuth"
	permission_v2 "gtcms/internal/controller/permission-v2"
	role_v2 "gtcms/internal/controller/role-v2"
	"gtcms/internal/controller/selectorConfig"
	"gtcms/internal/controller/sensitive"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/net/goai"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gtime"

	"gtcms/internal/controller/sitemap"
	"gtcms/internal/controller/spider"
	"gtcms/internal/controller/stat"
	"gtcms/internal/controller/storeConfig"
	"gtcms/internal/controller/tool"
	"gtcms/internal/logic/validRule"
	"gtcms/internal/service"
)

const (
	HttpDefaultPort = 8004 // 默认端口号
)

var (
	http = cHttp{}
)

type cHttp struct {
	g.Meta `name:"http"`
}

type cHttpInput struct {
	g.Meta `name:"http" brief:"start http server"`
	Port   int `short:"p" name:"port"  brief:"http端口,默认8004"`
}

type cHttpOutput struct {
}

func (c *cHttp) Index(ctx context.Context, in cHttpInput) (out *cHttpOutput, err error) {
	if in.Port <= 0 || in.Port > 65535 { // 命令没有指定
		configPort := g.Cfg().MustGet(ctx, "server.address").Int()
		if configPort > 0 { // 优先配置文件的
			in.Port = configPort
		} else {
			in.Port = HttpDefaultPort
		}
	}

	s := g.Server()
	s.EnablePProf()
	// 统一使用全局的logger
	s.SetLogger(g.Log())
	// // 为数据库设置redis缓存
	redisCache := gcache.NewAdapterRedis(g.Redis())
	g.DB().GetCache().SetAdapter(redisCache)

	// 设置时区
	_ = gtime.SetTimeZone("Asia/Shanghai")

	// TODO 这一段是缓存读配置，可封装
	//cacheCost := g.Cfg().MustGet(ctx, "server.dbCacheCost").Int64()
	//cacheNumCounters := g.Cfg().MustGet(ctx, "server.dbCacheNumCounters").Int64()
	//optFuncs := []cacher.OptFunc{
	//	cacher.OptSetUpdateType(cacher.UpdateASAP, 0),
	//}
	//if cacheCost > 0 {
	//	optFuncs = append(optFuncs,
	//		cacher.OptSetInnerCacheCost(cacheCost),
	//	)
	//}
	//if cacheNumCounters > 0 {
	//	optFuncs = append(optFuncs,
	//		cacher.OptSetNumCounters(cacheNumCounters),
	//	)
	//}
	//cache := cacher.NewCache(ctx, "gtcms", "db", optFuncs...)
	//// 为数据库设置redis缓存
	//// redisCache := gcache.NewAdapterRedis(g.Redis())
	//g.DB().GetCache().SetAdapter(cache)

	// 设置多语言资源路径
	_ = g.I18n().SetPath("resource/i18n")
	// 设置自定义校验规则
	validRule.Register()

	// --------中间件（系统级）--------
	// s.Use(xxx)
	// 2.5版本兼容:Ctx传播给异步流程或者保持和之前逻辑兼
	s.Use(service.Middleware().NeverDoneCtx)
	// 为了尽量减少系统性能损耗，只配置到路由做中间件处理，只有路由匹配到控制器才会处理中间件

	s.Group("/aapi", func(group *ghttp.RouterGroup) {
		// 记录请求头和请求体相关数据
		group.Middleware(service.Middleware().LogRequest)
		// 跨域
		group.Middleware(service.Middleware().SetCORSOptions)
		// 统一返回格式
		group.Middleware(service.Middleware().HandlerResponse)
		// 多语言支持
		group.Middleware(service.Middleware().I18n)

		// --------无需认证接口--------
		group.Bind(
			noAuth.New(),
			quranCrawler.New(),
			calendarMgr.New(),
			faq.New(),
		)

		// --------需认证接口--------
		group.Group("/", func(group *ghttp.RouterGroup) {
			// --------中间件（需认证过）--------
			group.Middleware(service.Middleware().Auth)
			group.Middleware(service.Middleware().AccessControlCheck)
			// --------需认证接口--------
			group.Bind(
				accountSiteLink.New(),
				role_v2.New(),
				sensitive.New(),
				selectorConfig.New(),
				//risk.New(),
				cmsLoginRisk.New(),
				cmsLoginConfig.New(),
				account.New(),
				loginMgr.New(),
				news.New(),
				label.New(),
				ad.New(),
				keyword.New(),
				//tdkTmpl.New(),
				sitemap.New(),
				spider.New(),
				storeConfig.New(),
				articleTransformationConfig.New(),
				accountAuditLog.New(),
				module.New(),
				attachment.New(),
				tool.New(),
				homePage.New(),
				permission_v2.New(),
				stat.New(),
				ai.New(),
				quran.New(),
				newsArticle.New(),   // 新闻文章管理
				newsCategory.New(),  // 新闻分类管理
				newsTopic.New(),     // 精选专题管理
				videoCategory.New(), // 视频分类管理
				videoTopic.New(),    // 视频专题管理
				video.New(),         // 视频管理

				hajiJadwal.New(), // 朝觐日程管理
				landmark.New(),   // 地标管理
			)
		})
		//s.BindHandler("/testtokenDaPjhD6JyH", collect.GetTestToken) // 平时测试用的，线上环境里不生效

	})

	service.CronJob().Daemon(ctx)
	// API 文档
	enhanceOpenAPIDoc(s)

	s.SetPort(in.Port)
	if err = s.Start(); err != nil {
		panic(err)
	}
	g.Wait()
	return
}

func enhanceOpenAPIDoc(s *ghttp.Server) {
	openapi := s.GetOpenApi()
	openapi.Config.CommonResponse = ghttp.DefaultHandlerResponse{}
	openapi.Config.CommonResponseDataField = `Data`
	// API description.
	openapi.Info = goai.Info{
		Title:       consts.OpenAPITitle,
		Description: consts.OpenAPIDescription,
	}
}
