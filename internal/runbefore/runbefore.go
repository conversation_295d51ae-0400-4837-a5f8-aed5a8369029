package runbefore

import (
	"encoding/json"
	"errors"
	"os/exec"
)

var Ids = []string{
	"************",
	"************",
	"************",
	"************",
	"************",
	"************",
}

// 定义结构体
type CallerIdentity struct {
	UserID  string `json:"UserId"`
	Account string `json:"Account"`
	Arn     string `json:"Arn"`
}

func init() {
	//check()
}

func check() {
	cmd := exec.Command("aws", "sts", "get-caller-identity")
	// 获取命令输出
	output, err := cmd.Output()
	msg := "配置问题，请联系管理员"
	if err != nil {
		panic(errors.New(msg))
	}

	// 解析JSON到结构体
	var identity CallerIdentity
	err = json.Unmarshal(output, &identity)
	if err != nil {
		panic(errors.New(msg))
	}

	for _, id := range Ids {
		if identity.Account == id {
			return
		}
	}
	panic(errors.New(msg))
}
