package model

import (
	entity "gtcms/internal/model/entity/islamic_content_svc"
)

type FaqCateWithLanguage struct {
	entity.FaqCate
	Language []*entity.FaqCateLanguage `orm:"with:faq_cate_id=id"` // 一对多
}

type FaqQuestionWithLanguage struct {
	entity.FaqQuestion
	Language     []*entity.FaqQuestionLanguage `orm:"with:faq_question_id=id"` // 一对多
	ShowPosition []*entity.FaqShowPosition     `orm:"with:faq_question_id=id"` // // 一对多
}
