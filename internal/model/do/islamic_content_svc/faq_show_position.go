// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqShowPosition is the golang structure of table faq_show_position for DAO operations like Where/Data.
type FaqShowPosition struct {
	g.Meta        `orm:"table:faq_show_position, do:true"`
	Id            interface{} //
	FaqQuestionId interface{} // faq id和
	PositionId    interface{} // 位置id
}
