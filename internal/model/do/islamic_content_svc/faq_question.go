// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqQuestion is the golang structure of table faq_question for DAO operations like Where/Data.
type FaqQuestion struct {
	g.Meta        `orm:"table:faq_question, do:true"`
	Id            interface{} //
	IsZh          interface{} // 是否中文，0-否，1-是
	IsEn          interface{} // 是否英文，0-否，1-是
	IsId          interface{} // 是否印尼文，0-否，1-是
	FaqCateId     interface{} //
	IsOpen        interface{} // 状态 [ 1 启用  2 禁用]
	Sort          interface{} // 排序
	PublishTime   interface{} // 发布时间
	Views         interface{} // 浏览量
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} //
	UpdateTime    interface{} //
	DeleteTime    interface{} //
}
