// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HajiDoaRingkas is the golang structure of table haji_doa_ringkas for DAO operations like Where/Data.
type HajiDoaRingkas struct {
	g.Meta     `orm:"table:haji_doa_ringkas, do:true"`
	Id         interface{} // 主键ID
	DoaNo      interface{} // 祈祷文序号
	DoaName    interface{} // 祈祷文名称
	CreateTime interface{} // 创建时间（毫秒时间戳）
	UpdateTime interface{} // 更新时间（毫秒时间戳）
}
