// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqQuestionLanguage is the golang structure of table faq_question_language for DAO operations like Where/Data.
type FaqQuestionLanguage struct {
	g.Meta        `orm:"table:faq_question_language, do:true"`
	Id            interface{} //
	FaqQuestionId interface{} //
	LanguageId    interface{} // 语言
	Title         interface{} // 标题
	Desc          interface{} // 简介
}
