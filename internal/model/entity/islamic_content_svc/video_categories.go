// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// VideoCategories is the golang structure for table video_categories.
type VideoCategories struct {
	Id         uint   `json:"id"         orm:"id"          description:"主键ID"`
	VideoCount uint   `json:"videoCount" orm:"video_count" description:"分类下视频数量"`
	Remark     string `json:"remark"     orm:"remark"      description:"备注"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间(毫秒时间戳)"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间(毫秒时间戳)"`
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`
}
