// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiHikmah is the golang structure for table haji_hikmah.
type HajiHikmah struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	ArticleId  uint64 `json:"articleId"  orm:"article_id"  description:"文章ID，关联news_article.id"`
	SortOrder  uint   `json:"sortOrder"  orm:"sort_order"  description:"排序值，数字越小排序越靠前"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
