// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// SuratAyat is the golang structure for table surat_ayat.
type SuratAyat struct {
	Id          int    `json:"id"          orm:"id"           description:""`
	AyatId      int    `json:"ayatId"      orm:"ayat_id"      description:"经文全局ID"`
	SurahId     int    `json:"surahId"     orm:"surah_id"     description:"所属章节ID"`
	Nomor       int    `json:"nomor"       orm:"nomor"        description:"经文在章节中的编号"`
	Ar          string `json:"ar"          orm:"ar"           description:"阿拉伯语经文"`
	Tr          string `json:"tr"          orm:"tr"           description:"音译文本"`
	Idn         string `json:"idn"         orm:"idn"          description:"印尼语翻译"`
	Juz         int    `json:"juz"         orm:"juz"          description:"juz编号"`
	Page        int    `json:"page"        orm:"page"         description:"所在页码"`
	CreatedTime uint64 `json:"createdTime" orm:"created_time" description:"创建时间戳(毫秒)"`
	UpdatedTime uint64 `json:"updatedTime" orm:"updated_time" description:"修改时间戳(毫秒)"`
}
