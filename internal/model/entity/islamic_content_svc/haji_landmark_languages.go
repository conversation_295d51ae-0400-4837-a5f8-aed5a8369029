// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiLandmarkLanguages is the golang structure for table haji_landmark_languages.
type HajiLandmarkLanguages struct {
	Id               uint64 `json:"id"               orm:"id"                description:"主键ID"`
	LandmarkId       uint64 `json:"landmarkId"       orm:"landmark_id"       description:"地标ID，关联haji_landmark.id"`
	LanguageId       uint   `json:"languageId"       orm:"language_id"       description:"语言ID：0-中文，1-英文，2-印尼语"`
	LandmarkName     string `json:"landmarkName"     orm:"landmark_name"     description:"地标名称"`
	Country          string `json:"country"          orm:"country"           description:"国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)"`
	Address          string `json:"address"          orm:"address"           description:"详细地址"`
	ShortDescription string `json:"shortDescription" orm:"short_description" description:"简介"`
	InformationText  string `json:"informationText"  orm:"information_text"  description:"详细介绍"`
	CreateTime       uint64 `json:"createTime"       orm:"create_time"       description:"创建时间（毫秒时间戳）"`
	UpdateTime       uint64 `json:"updateTime"       orm:"update_time"       description:"更新时间（毫秒时间戳）"`
}
