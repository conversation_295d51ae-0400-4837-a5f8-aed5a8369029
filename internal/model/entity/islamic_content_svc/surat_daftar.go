// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// SuratDaftar is the golang structure for table surat_daftar.
type SuratDaftar struct {
	Id          int    `json:"id"          orm:"id"           description:""`
	Nomor       int    `json:"nomor"       orm:"nomor"        description:"章节编号 (1-114)"`
	Nama        string `json:"nama"        orm:"nama"         description:"阿拉伯语章节名"`
	NamaLatin   string `json:"namaLatin"   orm:"nama_latin"   description:"拉丁化章节名"`
	JumlahAyat  int    `json:"jumlahAyat"  orm:"jumlah_ayat"  description:"经文数量"`
	TempatTurun string `json:"tempatTurun" orm:"tempat_turun" description:"降示地点"`
	Arti        string `json:"arti"        orm:"arti"         description:"章节含义"`
	Deskripsi   string `json:"deskripsi"   orm:"deskripsi"    description:"章节描述"`
	Audio       string `json:"audio"       orm:"audio"        description:"音频文件URL"`
	Status      int    `json:"status"      orm:"status"       description:"状态标识"`
	IsPopular   int    `json:"isPopular"   orm:"is_popular"   description:"是否热门章节 0否 1是"`
	CreatedTime uint64 `json:"createdTime" orm:"created_time" description:"创建时间戳(毫秒)"`
	UpdatedTime uint64 `json:"updatedTime" orm:"updated_time" description:"修改时间戳(毫秒)"`
}
