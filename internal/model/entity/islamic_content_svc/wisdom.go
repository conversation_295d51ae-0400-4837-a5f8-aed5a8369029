// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// Wisdom is the golang structure for table wisdom.
type Wisdom struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	WisdomCateId  uint   `json:"wisdomCateId"  orm:"wisdom_cate_id" description:""`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"`
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`
	Views         int    `json:"views"         orm:"views"          description:"浏览量"`
	ArticleId     uint   `json:"articleId"     orm:"article_id"     description:"文章id"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:""`
}
