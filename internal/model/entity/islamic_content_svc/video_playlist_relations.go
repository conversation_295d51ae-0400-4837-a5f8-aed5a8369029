// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// VideoPlaylistRelations is the golang structure for table video_playlist_relations.
type VideoPlaylistRelations struct {
	Id         uint   `json:"id"         orm:"id"          description:"主键ID"`
	PlaylistId uint   `json:"playlistId" orm:"playlist_id" description:"播放列表ID"`
	VideoId    uint   `json:"videoId"    orm:"video_id"    description:"视频ID"`
	VideoName  string `json:"videoName"  orm:"video_name"  description:"视频名称"`
	SortOrder  uint   `json:"sortOrder"  orm:"sort_order"  description:"排序权重，数字越小越靠前"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间(毫秒时间戳)"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间(毫秒时间戳)"`
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`
}
