// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiJadwalDescription is the golang structure for table haji_jadwal_description.
type HajiJadwalDescription struct {
	Id          uint64 `json:"id"          orm:"id"          description:"主键ID"`
	Year        int    `json:"year"        orm:"year"        description:"朝觐年份"`
	LanguageId  uint   `json:"languageId"  orm:"language_id" description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	Description string `json:"description" orm:"description" description:"日程说明文字"`
}
