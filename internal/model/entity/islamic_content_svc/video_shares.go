// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// VideoShares is the golang structure for table video_shares.
type VideoShares struct {
	Id            uint64 `json:"id"            orm:"id"             description:"主键ID"`
	UserId        uint64 `json:"userId"        orm:"user_id"        description:"用户ID，0表示未登录用户"`
	VideoId       uint   `json:"videoId"       orm:"video_id"       description:"视频ID"`
	SharePlatform string `json:"sharePlatform" orm:"share_platform" description:"分享平台：wechat, facebook, twitter, whatsapp等"`
	CreateTime    uint64 `json:"createTime"    orm:"create_time"    description:"分享时间(毫秒时间戳)"`
}
