// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiUrutan is the golang structure for table haji_urutan.
type HajiUrutan struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	UrutanNo   int    `json:"urutanNo"   orm:"urutan_no"   description:"朝觐顺序（数字）"`
	IconUrl    string `json:"iconUrl"    orm:"icon_url"    description:"图标URL"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
