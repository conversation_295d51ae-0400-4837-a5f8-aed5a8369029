// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// NewsArticle is the golang structure for table news_article.
type NewsArticle struct {
	Id               uint   `json:"id"               orm:"id"                 description:""`
	IsZh             uint   `json:"isZh"             orm:"is_zh"              description:"是否中文，0-否，1-是"`
	IsEn             uint   `json:"isEn"             orm:"is_en"              description:"是否英文，0-否，1-是"`
	IsId             uint   `json:"isId"             orm:"is_id"              description:"是否印尼文，0-否，1-是"`
	CategoryId       uint   `json:"categoryId"       orm:"category_id"        description:"分类id"`
	AdminId          uint   `json:"adminId"          orm:"admin_id"           description:"分类负责人id"`
	CoverImgs        string `json:"coverImgs"        orm:"cover_imgs"         description:"专题图片"`
	Creater          uint   `json:"creater"          orm:"creater"            description:"创建者id"`
	CreateName       string `json:"createName"       orm:"create_name"        description:"后台创建者"`
	Author           string `json:"author"           orm:"author"             description:"创建人"`
	IsTop            uint   `json:"isTop"            orm:"is_top"             description:"是否加入头条，1启用，0关闭"`
	IsRecommend      uint   `json:"isRecommend"      orm:"is_recommend"       description:"是否推荐，1启用，0关闭"`
	IsPublish        uint   `json:"isPublish"        orm:"is_publish"         description:"是否发布，1启用，0关闭"`
	IsDraft          uint   `json:"isDraft"          orm:"is_draft"           description:"是否草稿状态，1是，0否"`
	CreateTime       int64  `json:"createTime"       orm:"create_time"        description:"创建时间"`
	PublishTime      int64  `json:"publishTime"      orm:"publish_time"       description:"发布时间"`
	UpdateTime       int64  `json:"updateTime"       orm:"update_time"        description:"修改时间"`
	DeleteTime       int64  `json:"deleteTime"       orm:"delete_time"        description:"删除时间"`
	AuthorLogo       string `json:"authorLogo"       orm:"author_logo"        description:"创建人头像"`
	AuthorAuthStatus int    `json:"authorAuthStatus" orm:"author_auth_status" description:"作者认证状态 0未认证 1已认证"`
	RecommendTime    int64  `json:"recommendTime"    orm:"recommend_time"     description:"加入推荐时间"`
	TopTime          int64  `json:"topTime"          orm:"top_time"           description:"加入头条时间"`
}
