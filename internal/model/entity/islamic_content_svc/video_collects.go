// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// VideoCollects is the golang structure for table video_collects.
type VideoCollects struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	UserId     uint64 `json:"userId"     orm:"user_id"     description:"用户ID"`
	VideoId    uint   `json:"videoId"    orm:"video_id"    description:"视频ID"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"收藏时间(毫秒时间戳)"`
}
