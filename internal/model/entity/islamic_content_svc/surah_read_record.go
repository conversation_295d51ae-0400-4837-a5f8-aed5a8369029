// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// SurahReadRecord is the golang structure for table surah_read_record.
type SurahReadRecord struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	UserId     uint   `json:"userId"     orm:"user_id"     description:"用户id"`
	AyahId     uint   `json:"ayahId"     orm:"ayah_id"     description:"ayah_id节id"`
	SurahName  string `json:"surahName"  orm:"surah_name"  description:"名称"`
	IsUserOp   uint   `json:"isUserOp"   orm:"is_user_op"  description:"是否用户操作，1是 0否"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间（注册时间）"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未更新"`
}
