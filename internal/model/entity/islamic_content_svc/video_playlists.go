// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// VideoPlaylists is the golang structure for table video_playlists.
type VideoPlaylists struct {
	Id           uint   `json:"id"           orm:"id"            description:"主键ID"`
	CoverUrl     string `json:"coverUrl"     orm:"cover_url"     description:"专题封面图片链接"`
	IsVisible    uint   `json:"isVisible"    orm:"is_visible"    description:"是否显示，0-隐藏，1-显示"`
	SortOrder    uint   `json:"sortOrder"    orm:"sort_order"    description:"排序权重，数字越小越靠前"`
	VideoCount   uint   `json:"videoCount"   orm:"video_count"   description:"播放列表下视频数量"`
	ViewCount    uint64 `json:"viewCount"    orm:"view_count"    description:"播放列表浏览次数"`
	ShareCount   uint64 `json:"shareCount"   orm:"share_count"   description:"播放列表分享次数"`
	CollectCount uint64 `json:"collectCount" orm:"collect_count" description:"播放列表收藏次数"`
	CreateTime   uint64 `json:"createTime"   orm:"create_time"   description:"创建时间(毫秒时间戳)"`
	UpdateTime   uint64 `json:"updateTime"   orm:"update_time"   description:"更新时间(毫秒时间戳)"`
	Creater      uint   `json:"creater"      orm:"creater"       description:"创建者id"`
	CreateName   string `json:"createName"   orm:"create_name"   description:"创建者"`
	DeleteTime   int64  `json:"deleteTime"   orm:"delete_time"   description:"删除时间"`
}
