// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiJadwal is the golang structure for table haji_jadwal.
type HajiJadwal struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	ItemNo     int    `json:"itemNo"     orm:"item_no"     description:"项目编号"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
