// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiDoaRingkas is the golang structure for table haji_doa_ringkas.
type HajiDoaRingkas struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	DoaNo      int    `json:"doaNo"      orm:"doa_no"      description:"祈祷文序号"`
	DoaName    string `json:"doaName"    orm:"doa_name"    description:"祈祷文名称"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
