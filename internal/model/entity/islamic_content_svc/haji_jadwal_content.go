// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiJadwalContent is the golang structure for table haji_jadwal_content.
type HajiJadwalContent struct {
	Id             uint64 `json:"id"             orm:"id"              description:"主键ID"`
	JadwalId       uint64 `json:"jadwalId"       orm:"jadwal_id"       description:"朝觐时间表ID，关联haji_jadwal.id"`
	LanguageId     uint   `json:"languageId"     orm:"language_id"     description:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TimeInfo       string `json:"timeInfo"       orm:"time_info"       description:"手动输入的时间信息"`
	EventSummary   string `json:"eventSummary"   orm:"event_summary"   description:"事件简述"`
	AdditionalInfo string `json:"additionalInfo" orm:"additional_info" description:"附加信息"`
	ArticleText    string `json:"articleText"    orm:"article_text"    description:"文章详情（副文本）"`
	CreateTime     uint64 `json:"createTime"     orm:"create_time"     description:"创建时间（毫秒时间戳）"`
	UpdateTime     uint64 `json:"updateTime"     orm:"update_time"     description:"更新时间（毫秒时间戳）"`
}
