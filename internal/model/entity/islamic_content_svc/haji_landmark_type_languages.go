// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package islamic_content_svc

// HajiLandmarkTypeLanguages is the golang structure for table haji_landmark_type_languages.
type HajiLandmarkTypeLanguages struct {
	Id         uint64 `json:"id"         orm:"id"          description:"主键ID"`
	TypeId     uint64 `json:"typeId"     orm:"type_id"     description:"地标类型ID，关联haji_landmark_type.id"`
	LanguageId uint   `json:"languageId" orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"`
	TypeName   string `json:"typeName"   orm:"type_name"   description:"类型名称"`
	CreateTime uint64 `json:"createTime" orm:"create_time" description:"创建时间（毫秒时间戳）"`
	UpdateTime uint64 `json:"updateTime" orm:"update_time" description:"更新时间（毫秒时间戳）"`
}
