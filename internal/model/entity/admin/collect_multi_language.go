// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CollectMultiLanguage is the golang structure for table collect_multi_language.
type CollectMultiLanguage struct {
	Table       string `json:"table"       orm:"table"         description:"表名"`
	Id          int    `json:"id"          orm:"id"            description:"表中的id"`
	Column      string `json:"column"      orm:"column"        description:"表中字段名"`
	UpdatedAt   int64  `json:"updatedAt"   orm:"updated_at"    description:"更新时间"`
	NameId      string `json:"nameId"      orm:"name_id"       description:"印尼语"`
	ShortNameId string `json:"shortNameId" orm:"short_name_id" description:"印尼语，去掉()\"'\""`
	NameVi      string `json:"nameVi"      orm:"name_vi"       description:"越南语"`
	ShortNameVi string `json:"shortNameVi" orm:"short_name_vi" description:"越南语，去掉()\"'\""`
}
