// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/shopspring/decimal"
)

// Sitemap is the golang structure for table sitemap.
type Sitemap struct {
	Id                 uint            `json:"id"                 orm:"id"                   description:""`
	Belong             int             `json:"belong"             orm:"belong"               description:"所属(1:分组 2:站点)"`
	BelongId           uint            `json:"belongId"           orm:"belong_id"            description:"所属分组/站点id"`
	Format             int             `json:"format"             orm:"format"               description:"格式(1:xml地图 2:txt地图 3:html地图)"`
	CreateType         int             `json:"createType"         orm:"create_type"          description:"生成方式(1:手动 2:自动)"`
	MainRefreshRate    int             `json:"mainRefreshRate"    orm:"main_refresh_rate"    description:"首页更新频率(1:每天 2:每星期 3:每月)"`
	ListRefreshRate    int             `json:"listRefreshRate"    orm:"list_refresh_rate"    description:"列表页更新频率(1:每天 2:每星期 3:每月)"`
	ContentRefreshRate int             `json:"contentRefreshRate" orm:"content_refresh_rate" description:"内容页更新频率(1:每天 2:每星期 3:每月)"`
	MainLevel          decimal.Decimal `json:"mainLevel"          orm:"main_level"           description:"首页优先级别"`
	ListLevel          decimal.Decimal `json:"listLevel"          orm:"list_level"           description:"列表页优先级别"`
	ContentLevel       decimal.Decimal `json:"contentLevel"       orm:"content_level"        description:"内容页优先级别"`
	Status             int             `json:"status"             orm:"status"               description:"状态(1:启用 2:禁用)"`
	Remark             string          `json:"remark"             orm:"remark"               description:"说明"`
	CreateTime         int64           `json:"createTime"         orm:"create_time"          description:"创建时间"`
	UpdateTime         int64           `json:"updateTime"         orm:"update_time"          description:"修改时间"`
	DeleteTime         int64           `json:"deleteTime"         orm:"delete_time"          description:"删除时间"`
	Creater            uint            `json:"creater"            orm:"creater"              description:"创建者"`
	LinkNum            int             `json:"linkNum"            orm:"link_num"             description:"每个地图文件内链最大数"`
}
