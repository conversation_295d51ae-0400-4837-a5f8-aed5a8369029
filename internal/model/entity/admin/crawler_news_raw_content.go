// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CrawlerNewsRawContent is the golang structure for table crawler_news_raw_content.
type CrawlerNewsRawContent struct {
	Id            int64  `json:"id"            orm:"id"             description:""`
	Content       string `json:"content"       orm:"content"        description:"文章正文"`
	ContentFilter string `json:"contentFilter" orm:"content_filter" description:"过滤标签后的内容"`
	IsFilter      int    `json:"isFilter"      orm:"is_filter"      description:"是否过滤 0-否1-是"`
}
