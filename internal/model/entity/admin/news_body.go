// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// NewsBody is the golang structure for table news_body.
type NewsBody struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	NewsId     uint   `json:"newsId"     orm:"news_id"     description:"新闻id"`
	Body       string `json:"body"       orm:"body"        description:"内容"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`
	Creater    uint   `json:"creater"    orm:"creater"     description:"创建者"`
}
