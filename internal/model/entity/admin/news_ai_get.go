// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// NewsAiGet is the golang structure for table news_ai_get.
type NewsAiGet struct {
	Id         int64  `json:"id"         orm:"id"          description:""`
	Title      string `json:"title"      orm:"title"       description:"标题"`
	Content    string `json:"content"    orm:"content"     description:"文章正文"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:""`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:""`
	Type       int    `json:"type"       orm:"type"        description:"类型。 1足球，2篮球"`
	IsPass     int    `json:"isPass"     orm:"is_pass"     description:"审核状态(1通过 2不通过)"`
}
