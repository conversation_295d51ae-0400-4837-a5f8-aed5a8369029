// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Label is the golang structure for table label.
type Label struct {
	Id           uint   `json:"id"           orm:"id"             description:""`
	BelongId     uint   `json:"belongId"     orm:"belong_id"      description:"分组/站点id"`
	SelfId       uint   `json:"selfId"       orm:"self_id"        description:"自身id(如果belong非0,那这个指向的是id)"`
	Belong       int    `json:"belong"       orm:"belong"         description:"所属(0:自身 1:分组 2:站点)"`
	Name         string `json:"name"         orm:"name"           description:"名称"`
	Clicks       int    `json:"clicks"       orm:"clicks"         description:"点击数"`
	RelNewsCount int    `json:"relNewsCount" orm:"rel_news_count" description:"文章数"`
	Sort         int    `json:"sort"         orm:"sort"           description:"排序"`
	IsUsed       int    `json:"isUsed"       orm:"is_used"        description:"是否常用(1:是 2:否)"`
	IsRecommend  int    `json:"isRecommend"  orm:"is_recommend"   description:"是否推荐(1:是 2:否)"`
	Status       int    `json:"status"       orm:"status"         description:"状态(1:显示 2:隐藏)"`
	SeoTitle     string `json:"seoTitle"     orm:"seo_title"      description:"seo标题"`
	SeoKeyword   string `json:"seoKeyword"   orm:"seo_keyword"    description:"seo关键词"`
	SeoDesc      string `json:"seoDesc"      orm:"seo_desc"       description:"seo描述"`
	Url          string `json:"url"          orm:"url"            description:"路由(唯一)"`
	Thumb        string `json:"thumb"        orm:"thumb"          description:"缩略图"`
	CreateTime   int64  `json:"createTime"   orm:"create_time"    description:"创建时间"`
	UpdateTime   int64  `json:"updateTime"   orm:"update_time"    description:"更新时间"`
	DeleteTime   int64  `json:"deleteTime"   orm:"delete_time"    description:"删除时间"`
	Creater      uint   `json:"creater"      orm:"creater"        description:"创建者"`
}
