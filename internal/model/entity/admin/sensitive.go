// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Sensitive is the golang structure for table sensitive.
type Sensitive struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	ClassId       int    `json:"classId"       orm:"class_id"       description:"分类ID"`
	Word          string `json:"word"          orm:"word"           description:"敏感词"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"是否启用：1开 2关"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
