// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// News is the golang structure for table news.
type News struct {
	Id            uint   `json:"id"            orm:"id"              description:""`
	BelongColId   uint   `json:"belongColId"   orm:"belong_col_id"   description:"所属栏目id"`
	BelongColName string `json:"belongColName" orm:"belong_col_name" description:"所属栏目名称"`
	BelongGroupId uint   `json:"belongGroupId" orm:"belong_group_id" description:"所属分组"`
	BelongSiteId  uint   `json:"belongSiteId"  orm:"belong_site_id"  description:"所属站点"`
	Title         string `json:"title"         orm:"title"           description:"标题"`
	Desc          string `json:"desc"          orm:"desc"            description:"简介"`
	Label         string `json:"label"         orm:"label"           description:"标签"`
	SeoTitle      string `json:"seoTitle"      orm:"seo_title"       description:"seo标题"`
	SeoKeyword    string `json:"seoKeyword"    orm:"seo_keyword"     description:"seo关键词"`
	SeoDesc       string `json:"seoDesc"       orm:"seo_desc"        description:"seo描述"`
	FileName      string `json:"fileName"      orm:"file_name"       description:"自定义文件名"`
	Author        string `json:"author"        orm:"author"          description:"作者"`
	Resource      string `json:"resource"      orm:"resource"        description:"来源"`
	Views         int    `json:"views"         orm:"views"           description:"浏览量"`
	Sort          int    `json:"sort"          orm:"sort"            description:"排序"`
	Status        int    `json:"status"        orm:"status"          description:"状态(1:显示 2:隐藏)"`
	Attr          string `json:"attr"          orm:"attr"            description:"属性(1:视频)"`
	Thumb         string `json:"thumb"         orm:"thumb"           description:"缩略图"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"     description:"发布时间"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"     description:"修改时间"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"     description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"         description:"创建者"`
	IsHot         int    `json:"isHot"         orm:"is_hot"          description:"是否热门（1是 2否）"`
	IsAuto        int    `json:"isAuto"        orm:"is_auto"         description:"是否自动（1是0否）"`
	Pdf           string `json:"pdf"           orm:"pdf"             description:"pdf查看链接"`
}
