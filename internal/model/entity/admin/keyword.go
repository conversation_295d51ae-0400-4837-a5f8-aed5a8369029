// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Keyword is the golang structure for table keyword.
type Keyword struct {
	Id           uint   `json:"id"           orm:"id"            description:""`
	Belong       int    `json:"belong"       orm:"belong"        description:"所属(0:自身 1:分组 2:站点)"`
	BelongId     uint   `json:"belongId"     orm:"belong_id"     description:"分组/站点id"`
	SelfId       uint   `json:"selfId"       orm:"self_id"       description:"自身id(如果belong非0,那这个指向的是id)"`
	Name         string `json:"name"         orm:"name"          description:"名称"`
	Url          string `json:"url"          orm:"url"           description:"链接"`
	Status       int    `json:"status"       orm:"status"        description:"状态(1:启用 2:禁用)"`
	LinkProperty int    `json:"linkProperty" orm:"link_property" description:"链接属性(1:新窗口打开 2:nofollow)"`
	Remark       string `json:"remark"       orm:"remark"        description:"备注"`
	CreateTime   int64  `json:"createTime"   orm:"create_time"   description:"创建时间"`
	UpdateTime   int64  `json:"updateTime"   orm:"update_time"   description:"修改时间"`
	DeleteTime   int64  `json:"deleteTime"   orm:"delete_time"   description:"删除时间"`
	Creater      uint   `json:"creater"      orm:"creater"       description:"创建者"`
}
