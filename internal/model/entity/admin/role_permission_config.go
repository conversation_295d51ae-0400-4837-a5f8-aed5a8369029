// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// RolePermissionConfig is the golang structure for table role_permission_config.
type RolePermissionConfig struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	Name          string `json:"name"          orm:"name"           description:"角色名称"`
	RoleLevel     int    `json:"roleLevel"     orm:"role_level"     description:"角色层级：1管理员 2站长 3站员"`
	Label         string `json:"label"         orm:"label"          description:"角色名称（中）"`
	PermissionSet string `json:"permissionSet" orm:"permission_set" description:"权限集合：id数组"`
	MaskedFields  string `json:"maskedFields"  orm:"masked_fields"  description:"掩码字段 ：map结构{id: {k1:v1, k2:v2}}"`
	Remark        string `json:"remark"        orm:"remark"         description:"备注"`
	OrderBy       int    `json:"orderBy"       orm:"order_by"       description:"排序"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
