// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Account is the golang structure for table account.
type Account struct {
	Id                  uint   `json:"id"                  orm:"id"                     description:""`
	Account             string `json:"account"             orm:"account"                description:"帐号"`
	Password            string `json:"password"            orm:"password"               description:"密码"`
	NickName            string `json:"nickName"            orm:"nick_name"              description:"昵称"`
	Contact             string `json:"contact"             orm:"contact"                description:"联系方式"`
	Remark              string `json:"remark"              orm:"remark"                 description:"备注"`
	RoleId              uint   `json:"roleId"              orm:"role_id"                description:"角色id"`
	AuditPassword       string `json:"auditPassword"       orm:"audit_password"         description:"私人密码"`
	IsOnline            int    `json:"isOnline"            orm:"is_online"              description:"1:在线 2:离线"`
	IsAffect            int    `json:"isAffect"            orm:"is_affect"              description:"1:启用 2:停用"`
	LastSigninTime      int64  `json:"lastSigninTime"      orm:"last_signin_time"       description:"上次登录时间"`
	CreateTime          int64  `json:"createTime"          orm:"create_time"            description:"创建时间"`
	CreateAccount       string `json:"createAccount"       orm:"create_account"         description:"创建者"`
	UpdateTime          int64  `json:"updateTime"          orm:"update_time"            description:"更新时间"`
	UpdateAccount       string `json:"updateAccount"       orm:"update_account"         description:"更新者"`
	DeleteTime          int64  `json:"deleteTime"          orm:"delete_time"            description:"删除时间"`
	Creater             uint   `json:"creater"             orm:"creater"                description:"创建者"`
	IsRequireGoogleAuth int    `json:"isRequireGoogleAuth" orm:"is_require_google_auth" description:"是否谷歌验证码登录  1:需要 2:不用"`
	GoogleAuthSecret    string `json:"googleAuthSecret"    orm:"google_auth_secret"     description:"谷歌验证秘钥"`
	TemplateIds         string `json:"templateIds"         orm:"template_ids"           description:"可访问的模板id集合"`
}
