// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CronJob is the golang structure for table cron_job.
type CronJob struct {
	Id        uint   `json:"id"        orm:"id"         description:""`
	Name      string `json:"name"      orm:"name"       description:"任务名，纯用来显示"`
	Symbol    string `json:"symbol"    orm:"symbol"     description:"唯一标识，用来在代码中区分唯一的任务"`
	Status    int    `json:"status"    orm:"status"     description:"任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住"`
	MaxRetry  uint   `json:"maxRetry"  orm:"max_retry"  description:"失败后的最多重试次数 (0表示不进行重试"`
	UpdatedAt int64  `json:"updatedAt" orm:"updated_at" description:"更新时间，时间戳毫秒"`
	Param     string `json:"param"     orm:"param"      description:"这个任务的一些参数，是一个json"`
	RandDelay int    `json:"randDelay" orm:"rand_delay" description:"任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。"`
	Cron      string `json:"cron"      orm:"cron"       description:"crontab string"`
	Progress  string `json:"progress"  orm:"progress"   description:"当前进度，自定义的json"`
}
