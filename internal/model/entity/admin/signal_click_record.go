// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SignalClickRecord is the golang structure for table signal_click_record.
type SignalClickRecord struct {
	Id        int64       `json:"id"        orm:"id"         description:""`
	ProductId int         `json:"productId" orm:"product_id" description:"直播信号源id"`
	Clicks    int         `json:"clicks"    orm:"clicks"     description:"当天点击量"`
	RecordDay *gtime.Time `json:"recordDay" orm:"record_day" description:"当天日期"`
}
