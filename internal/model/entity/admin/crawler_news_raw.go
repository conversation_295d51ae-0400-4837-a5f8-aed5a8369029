// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CrawlerNewsRaw is the golang structure for table crawler_news_raw.
type CrawlerNewsRaw struct {
	Id            int64  `json:"id"            orm:"id"             description:""`
	Source        string `json:"source"        orm:"source"         description:"来源 （比如新浪体育"`
	Docid         string `json:"docid"         orm:"docid"          description:"在原始来源处的唯一id"`
	Title         string `json:"title"         orm:"title"          description:"标题"`
	Url           string `json:"url"           orm:"url"            description:"原始链接"`
	Intro         string `json:"intro"         orm:"intro"          description:"摘要"`
	CompetitionId int    `json:"competitionId" orm:"competition_id" description:"关联的赛事id,  为0表示还未关联"`
	SourceCate    string `json:"sourceCate"    orm:"source_cate"    description:"在原始来源里的分类标识 （比如新浪就是media_name)"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`
	Content       string `json:"content"       orm:"content"        description:"文章正文"`
	Type          int    `json:"type"          orm:"type"           description:"类型。 1足球，2篮球"`
	IsVideo       int    `json:"isVideo"       orm:"is_video"       description:"是否视频(1是 2否)"`
	Language      string `json:"language"      orm:"language"       description:"语言-cn中文 en英文"`
}
