// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// SelectorConfigTab is the golang structure for table selector_config_tab.
type SelectorConfigTab struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	SelectType    string `json:"selectType"    orm:"select_type"    description:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	TabName       string `json:"tabName"       orm:"tab_name"       description:"栏目名称"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 开 2 关]"`
	IsList        int    `json:"isList"        orm:"is_list"        description:"栏目内容 [ 1 列表  2 功能配置 ]"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
