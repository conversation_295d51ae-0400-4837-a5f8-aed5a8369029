// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// AccountLoginLog is the golang structure for table account_login_log.
type AccountLoginLog struct {
	Id          uint   `json:"id"          orm:"id"           description:""`
	AccountId   uint   `json:"accountId"   orm:"account_id"   description:"账户id"`
	AccountName string `json:"accountName" orm:"account_name" description:"账户名"`
	SigninTime  int64  `json:"signinTime"  orm:"signin_time"  description:"登录时间"`
	Ip          string `json:"ip"          orm:"ip"           description:"登录ip（ip6长度为39字符）"`
	IpRegion    string `json:"ipRegion"    orm:"ip_region"    description:"ip地址位置"`
	OperType    int    `json:"operType"    orm:"oper_type"    description:"操作类型 1 登入 2 登出 3 修改密码"`
	Status      int    `json:"status"      orm:"status"       description:"状态 1 成功 2 失败"`
	DeviceId    string `json:"deviceId"    orm:"device_id"    description:"设备编号"`
	DeviceType  string `json:"deviceType"  orm:"device_type"  description:"chrome ie"`
	Creater     uint   `json:"creater"     orm:"creater"      description:"创建者"`
}
