// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// SelectorConfigContent is the golang structure for table selector_config_content.
type SelectorConfigContent struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	SelectType    string `json:"selectType"    orm:"select_type"    description:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	Title         string `json:"title"         orm:"title"          description:"选择器类型对应的配置名称"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"`
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`
	Remark        string `json:"remark"        orm:"remark"         description:"备注"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
	Extra         string `json:"extra"         orm:"extra"          description:"额外数据：如注册商底下的平台账号[{\"account\":\"xx\"}]"`
}
