// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// PermissionNode is the golang structure for table permission_node.
type PermissionNode struct {
	Id      uint   `json:"id"      orm:"id"       description:""`
	PId     uint   `json:"pId"     orm:"p_id"     description:"父权限id（顶级为0）"`
	Name    string `json:"name"    orm:"name"     description:"菜单编号（权限编号）"`
	Label   string `json:"label"   orm:"label"    description:"显示名称（中），逗号分隔"`
	OrderBy int    `json:"orderBy" orm:"order_by" description:"排序"`
}
