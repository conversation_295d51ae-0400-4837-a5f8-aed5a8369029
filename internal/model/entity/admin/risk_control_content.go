// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// RiskControlContent is the golang structure for table risk_control_content.
type RiskControlContent struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	TabType       int    `json:"tabType"       orm:"tab_type"       description:"分类 [1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单 ； 不包含 5 地区屏蔽,  6 CC防御]"`
	Content       string `json:"content"       orm:"content"        description:"（1） tab_type = 1时 对应 IP白名单地址；  （2） tab_type = 2时 对应 IP黑名单地址；  （3） tab_type = 3时 对应 UA白名单配置；  （4） tab_type = 4时 对应 UA黑名单配置"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 开 2 关]"`
	Remark        string `json:"remark"        orm:"remark"         description:"备注"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
