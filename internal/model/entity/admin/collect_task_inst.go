// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CollectTaskInst is the golang structure for table collect_task_inst.
type CollectTaskInst struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	TaskId     uint   `json:"taskId"     orm:"task_id"     description:"关联 collect_task.id"`
	Symbol     string `json:"symbol"     orm:"symbol"      description:"关联 collect_task.symbol"`
	InstSymbol string `json:"instSymbol" orm:"inst_symbol" description:"这个任务自己的标识"`
	RetryTimes uint   `json:"retryTimes" orm:"retry_times" description:"已经重试几次了"`
	Status     int    `json:"status"     orm:"status"      description:"1: 未完成， 2:已完成且成功， 3:执行失败，"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间,时间戳毫秒"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间,时间戳毫秒"`
	RunStatus  string `json:"runStatus"  orm:"run_status"  description:"本次任务执行的上下文信息，生成自collect_task.run_status"`
}
