// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CollectTask is the golang structure for table collect_task.
type CollectTask struct {
	Id            uint   `json:"id"            orm:"id"              description:""`
	Name          string `json:"name"          orm:"name"            description:"任务名，纯用来显示"`
	Symbol        string `json:"symbol"        orm:"symbol"          description:"唯一标识，用来在代码中区分唯一的任务"`
	RunDuration   int    `json:"runDuration"   orm:"run_duration"    description:"任务执行间隔，单位秒， 0表示不可用"`
	Status        int    `json:"status"        orm:"status"          description:"任务状态. 1开启， 2禁用，3因为子任务一直失败而卡住"`
	MaxRetry      uint   `json:"maxRetry"      orm:"max_retry"       description:"失败后的最多重试次数 (0表示不进行重试"`
	UpdatedAt     int64  `json:"updatedAt"     orm:"updated_at"      description:"更新时间，时间戳毫秒"`
	RunStatus     string `json:"runStatus"     orm:"runStatus"       description:"这个任务的当前上下文状态，在代码中自定义，是一个json"`
	Param         string `json:"param"         orm:"param"           description:"这个任务的一些参数，是一个json"`
	CurrentInstId uint   `json:"currentInstId" orm:"current_inst_id" description:"当前正在执行的任务实例的id, 每次产生新任务前都要先检查一下当前任务是否已经完成"`
	RandDelay     int    `json:"randDelay"     orm:"rand_delay"      description:"任务执行随机延后rand(0,rand_delay) 秒，把任务执行打散，单位秒。"`
}
