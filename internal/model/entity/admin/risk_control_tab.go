// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// RiskControlTab is the golang structure for table risk_control_tab.
type RiskControlTab struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	TabType       int    `json:"tabType"       orm:"tab_type"       description:"栏目分类[1 IP白名单, 2 IP黑名单, 3 UA白名单, 4 UA黑名单,  5 地区屏蔽,  6 CC防御]"`
	TabName       string `json:"tabName"       orm:"tab_name"       description:"栏目名称"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 开 2 关]"`
	IsPass        int    `json:"isPass"        orm:"is_pass"        description:"类型[ 1 白名单命中通过 2 黑名单命中不通过]"`
	IsList        int    `json:"isList"        orm:"is_list"        description:"栏目内容 [ 1 列表  2 功能配置 ]"`
	Metas         string `json:"metas"         orm:"metas"          description:"功能属性：tab_type = 5时 对应 地区屏蔽配置 格式：    {\"BlockUserSwitch\": 1, \"BlockSpiderSwitch\": 1}             屏蔽用户：1开 2关     屏蔽蜘蛛：1开 2关 ；  tab_type = 6时 对应 CC防御配置  格式要求如下： {\"DefenseSwitch\": 1, \"TriggerFrequency\": 1}    防御开关 ：1开 2关     触发频率：90-150左右"`
	Priority      int    `json:"priority"      orm:"priority"       description:"访问优先级"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
