// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// AccountNodeConfig is the golang structure for table account_node_config.
type AccountNodeConfig struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	Name          string `json:"name"          orm:"name"           description:"节点名称"`
	Level         int    `json:"level"         orm:"level"          description:"层级"`
	ParentId      uint   `json:"parentId"      orm:"parent_id"      description:"父id 第一层级为0"`
	ApiNode       string `json:"apiNode"       orm:"api_node"       description:"对应的api接口名称"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
