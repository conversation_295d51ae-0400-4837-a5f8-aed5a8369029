// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// StoreCloudConfig is the golang structure for table store_cloud_config.
type StoreCloudConfig struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	Type          string `json:"type"          orm:"type"           description:"存储类型[local 本地存储, aws AWS亚马逊云存储]"`
	Config        string `json:"config"        orm:"config"         description:"配置"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
