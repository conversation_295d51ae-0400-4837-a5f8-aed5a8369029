// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// PermissionAttrs is the golang structure for table permission_attrs.
type PermissionAttrs struct {
	Id           uint   `json:"id"           orm:"id"            description:""`
	PermissionId uint   `json:"permissionId" orm:"permission_id" description:"权限id"`
	UrlPath      string `json:"urlPath"      orm:"url_path"      description:"api接口路径"`
	MaskedFields string `json:"maskedFields" orm:"masked_fields" description:"脱敏字段配置"`
	OrderBy      int    `json:"orderBy"      orm:"order_by"      description:"排序"`
}
