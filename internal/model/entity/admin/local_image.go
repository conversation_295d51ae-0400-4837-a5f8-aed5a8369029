// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// LocalImage is the golang structure for table local_image.
type LocalImage struct {
	Table      string `json:"table"      orm:"table"       description:"表名"`
	Id         int    `json:"id"         orm:"id"          description:"表中的id"`
	Column     string `json:"column"     orm:"column"      description:"表中字段名"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:""`
	Url        string `json:"url"        orm:"url"         description:""`
	OldUrl     string `json:"oldUrl"     orm:"old_url"     description:""`
}
