// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CrawlerNewsComment is the golang structure for table crawler_news_comment.
type CrawlerNewsComment struct {
	Id            int64  `json:"id"            orm:"id"             description:""`
	Source        string `json:"source"        orm:"source"         description:"来源 （比如新浪体育"`
	Mid           string `json:"mid"           orm:"mid"            description:"消息id"`
	Docid         string `json:"docid"         orm:"docid"          description:"在原始来源处的唯一id"`
	Content       string `json:"content"       orm:"content"        description:"评论内容"`
	CompetitionId int    `json:"competitionId" orm:"competition_id" description:"关联的赛事id,  为0表示还未关联"`
	Lid           int    `json:"lid"           orm:"lid"            description:"关联的赛事id,  为0表示还未关联"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`
	Type          int    `json:"type"          orm:"type"           description:"类型。 1足球，2篮球"`
}
