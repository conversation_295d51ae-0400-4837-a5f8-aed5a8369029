// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Attachment is the golang structure for table attachment.
type Attachment struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	CategoryId    uint   `json:"categoryId"    orm:"category_id"    description:"分类id"`
	Name          string `json:"name"          orm:"name"           description:"名称"`
	Key           string `json:"key"           orm:"key"            description:"对象key"`
	Size          int64  `json:"size"          orm:"size"           description:"大小(单位字节)"`
	Type          int    `json:"type"          orm:"type"           description:"类型[1  图片 2视频]"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
