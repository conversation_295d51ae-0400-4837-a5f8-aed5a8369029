// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// AccountSiteLink is the golang structure for table account_site_link.
type AccountSiteLink struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	AccountId     uint   `json:"accountId"     orm:"account_id"     description:"账号id"`
	GroupId       int    `json:"groupId"       orm:"group_id"       description:"组ID"`
	SiteId        uint   `json:"siteId"        orm:"site_id"        description:"站点id"`
	IsAffect      int    `json:"isAffect"      orm:"is_affect"      description:"1:启用 2:停用"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者ID"`
}
