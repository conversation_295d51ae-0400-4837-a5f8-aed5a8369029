// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// LabelOtherRelation is the golang structure for table label_other_relation.
type LabelOtherRelation struct {
	LabelId  uint `json:"labelId"  orm:"label_id"  description:"标签id"`
	RelId    uint `json:"relId"    orm:"rel_id"    description:"关联者id"`
	RelType  int  `json:"relType"  orm:"rel_type"  description:"关联者类型(1:新闻资讯 2:足球比赛 3:篮球比赛 4:xxx......)"`
	Creater  uint `json:"creater"  orm:"creater"   description:"创建者"`
	BelongId uint `json:"belongId" orm:"belong_id" description:"分组/站点id"`
	Belong   int  `json:"belong"   orm:"belong"    description:"所属(0:自身 1:分组 2:站点)"`
}
