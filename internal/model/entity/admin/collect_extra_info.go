// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CollectExtraInfo is the golang structure for table collect_extra_info.
type CollectExtraInfo struct {
	CategoryId   int    `json:"categoryId"   orm:"category_id"  description:"本seo属于哪个分类 1足球赛事 2足球球队 3足球球员 4篮球赛事 5篮球球队 6篮球球员 ..."`
	BelongId     int    `json:"belongId"     orm:"belong_id"    description:"属于对应的category中的哪个id的数据。比如当category=1时，它表示足球赛事id"`
	ViewCount    int    `json:"viewCount"    orm:"view_count"   description:"浏览量"`
	ReleaseTime  int64  `json:"releaseTime"  orm:"release_time" description:"更新时间"`
	OrderWeight  int    `json:"orderWeight"  orm:"order_weight" description:"排序权重"`
	Introduction string `json:"introduction" orm:"introduction" description:"简介(联赛、球队、球员)"`
	IsHot        int    `json:"isHot"        orm:"is_hot"       description:"是否热门(1是 2否)"`
	IsTop        int    `json:"isTop"        orm:"is_top"       description:"是否置顶(1是 2否)"`
}
