// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// SinglePage is the golang structure for table single_page.
type SinglePage struct {
	Id            uint   `json:"id"            orm:"id"              description:""`
	BelongGroupId uint   `json:"belongGroupId" orm:"belong_group_id" description:"所属分组id"`
	BelongSiteId  uint   `json:"belongSiteId"  orm:"belong_site_id"  description:"所属站点id"`
	Name          string `json:"name"          orm:"name"            description:"页面名称"`
	Url           string `json:"url"           orm:"url"             description:"url"`
	Desc          string `json:"desc"          orm:"desc"            description:"简介"`
	Status        int    `json:"status"        orm:"status"          description:"状态(1:启用 2:禁用)"`
	Sort          int    `json:"sort"          orm:"sort"            description:"排序"`
	SeoTitle      string `json:"seoTitle"      orm:"seo_title"       description:"seo标题"`
	SeoKeyword    string `json:"seoKeyword"    orm:"seo_keyword"     description:"seo关键词"`
	SeoDesc       string `json:"seoDesc"       orm:"seo_desc"        description:"seo描述"`
	Thumb         string `json:"thumb"         orm:"thumb"           description:"缩略图"`
	Banner        string `json:"banner"        orm:"banner"          description:"banner图"`
	Type          int    `json:"type"          orm:"type"            description:"类型(1:关于我们 2:联系我们)"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"     description:"创建时间"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"     description:"修改时间"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"     description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"         description:"创建者"`
}
