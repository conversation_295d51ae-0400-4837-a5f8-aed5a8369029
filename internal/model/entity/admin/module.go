// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Module is the golang structure for table module.
type Module struct {
	Id   string `json:"id"   orm:"id"   description:"对应collect_xxx_competition id"`
	Name string `json:"name" orm:"name" description:"名称"`
}
