// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// StoreBaseConfig is the golang structure for table store_base_config.
type StoreBaseConfig struct {
	Id                    uint   `json:"id"                    orm:"id"                      description:""`
	Type                  string `json:"type"                  orm:"type"                    description:"存储类型[local 本地存储, aws AWS亚马逊云存储]"`
	ThumbnailLargeWidth   int    `json:"thumbnailLargeWidth"   orm:"thumbnail_large_width"   description:"缩略大图宽"`
	ThumbnailLargeHeight  int    `json:"thumbnailLargeHeight"  orm:"thumbnail_large_height"  description:"缩略大图高"`
	ThumbnailMediumWidth  int    `json:"thumbnailMediumWidth"  orm:"thumbnail_medium_width"  description:"缩略中图宽"`
	ThumbnailMediumHeight int    `json:"thumbnailMediumHeight" orm:"thumbnail_medium_height" description:"缩略中图高"`
	ThumbnailSmallWidth   int    `json:"thumbnailSmallWidth"   orm:"thumbnail_small_width"   description:"缩略小图宽"`
	ThumbnailSmallHeight  int    `json:"thumbnailSmallHeight"  orm:"thumbnail_small_height"  description:"缩略小图高"`
	IsOpenWatermark       int    `json:"isOpenWatermark"       orm:"is_open_watermark"       description:"是否开启水印： 1:开 2:关"`
	WatermarkType         int    `json:"watermarkType"         orm:"watermark_type"          description:"水印类型： 1:图片 2:文字"`
	WatermarkContent      string `json:"watermarkContent"      orm:"watermark_content"       description:"水印图片或水印文字"`
	WatermarkLocation     int    `json:"watermarkLocation"     orm:"watermark_location"      description:"水印位置"`
	WatermarkOpacity      int    `json:"watermarkOpacity"      orm:"watermark_opacity"       description:"水印透明度"`
	WatermarkRotation     int    `json:"watermarkRotation"     orm:"watermark_rotation"      description:"水印倾斜度"`
	WatermarkHorizontal   int    `json:"watermarkHorizontal"   orm:"watermark_horizontal"    description:"水印横坐标偏移量"`
	WatermarkVertical     int    `json:"watermarkVertical"     orm:"watermark_vertical"      description:"水印纵坐标偏移量"`
	CreateTime            int64  `json:"createTime"            orm:"create_time"             description:"创建时间"`
	CreateAccount         string `json:"createAccount"         orm:"create_account"          description:"创建者"`
	UpdateTime            int64  `json:"updateTime"            orm:"update_time"             description:"更新时间"`
	UpdateAccount         string `json:"updateAccount"         orm:"update_account"          description:"更新者"`
	DeleteTime            int64  `json:"deleteTime"            orm:"delete_time"             description:"删除时间"`
}
