// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// AttachmentCategory is the golang structure for table attachment_category.
type AttachmentCategory struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	Pid           uint   `json:"pid"           orm:"pid"            description:"父分类"`
	Type          int    `json:"type"          orm:"type"           description:"类型[1  图片 2视频]"`
	Name          string `json:"name"          orm:"name"           description:"名称"`
	OrderBy       int    `json:"orderBy"       orm:"order_by"       description:"排序值(值大排前)"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
