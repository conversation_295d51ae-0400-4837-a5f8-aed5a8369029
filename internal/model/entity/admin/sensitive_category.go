// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// SensitiveCategory is the golang structure for table sensitive_category.
type SensitiveCategory struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	CategoryId    int    `json:"categoryId"    orm:"category_id"    description:"分类ID"`
	Name          string `json:"name"          orm:"name"           description:"敏感词"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"是否启用：1开 2关"`
	Desc          string `json:"desc"          orm:"desc"           description:"敏感词描述"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
}
