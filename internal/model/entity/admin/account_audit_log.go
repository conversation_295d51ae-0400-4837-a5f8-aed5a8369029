// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// AccountAuditLog is the golang structure for table account_audit_log.
type AccountAuditLog struct {
	Id          int    `json:"id"          orm:"id"           description:""`
	AdminId     uint   `json:"adminId"     orm:"admin_id"     description:"管理员id"`
	Account     string `json:"account"     orm:"account"      description:"管理员账号"`
	LoginIp     string `json:"loginIp"     orm:"login_ip"     description:"登录IP"`
	NodeId      int    `json:"nodeId"      orm:"node_id"      description:"节点ID"`
	Path        string `json:"path"        orm:"path"         description:"操作路径  eg: 会员管理/会员列表/所有玩家"`
	Object      string `json:"object"      orm:"object"       description:"操作对象 eg：会员属性 | 黑名单 | 产品名称 |"`
	ModifyType  int    `json:"modifyType"  orm:"modify_type"  description:"修改类型：1新增 2编辑 3删除 4批量编辑 5下载 6上传 7覆盖"`
	ModifyItem  string `json:"modifyItem"  orm:"modify_item"  description:"操作属性  ；eg： 标签 | 状态 | 备注 | 踢下线 | 优质"`
	ValueBefore string `json:"valueBefore" orm:"value_before" description:"修改前记录"`
	ValueAfter  string `json:"valueAfter"  orm:"value_after"  description:"修改后记录"`
	DiffOld     string `json:"diffOld"     orm:"diff_old"     description:"修改前变动的值"`
	DiffNew     string `json:"diffNew"     orm:"diff_new"     description:"修改后变动的值"`
	ExtendInfo  string `json:"extendInfo"  orm:"extend_info"  description:"扩展信息 eg：存放批量编辑影响的会员等"`
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`
	Creater     uint   `json:"creater"     orm:"creater"      description:"创建者"`
}
