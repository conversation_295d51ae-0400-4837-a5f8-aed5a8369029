// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CmsLoginConfig is the golang structure for table cms_login_config.
type CmsLoginConfig struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"后台开关： 1:开 2:关"`
	Title         string `json:"title"         orm:"title"          description:"后台名称"`
	Url           string `json:"url"           orm:"url"            description:"后台网址"`
	IsIpBind      int    `json:"isIpBind"      orm:"is_ip_bind"     description:"IP绑定： 1:开 2:关"`
	IsMacBind     int    `json:"isMacBind"     orm:"is_mac_bind"    description:"机器码绑定： 1:开 2:关"`
	IsGoogleBind  int    `json:"isGoogleBind"  orm:"is_google_bind" description:"谷歌验证码绑定： 1:开 2:关"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
