// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// Robots is the golang structure for table robots.
type Robots struct {
	Id         uint   `json:"id"         orm:"id"          description:""`
	Name       string `json:"name"       orm:"name"        description:"name"`
	Content    string `json:"content"    orm:"content"     description:"content"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"修改时间"`
	DeleteTime int64  `json:"deleteTime" orm:"delete_time" description:"删除时间"`
	Creater    uint   `json:"creater"    orm:"creater"     description:"创建者"`
}
