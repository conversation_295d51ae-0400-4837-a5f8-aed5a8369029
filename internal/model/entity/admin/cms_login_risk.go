// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CmsLoginRisk is the golang structure for table cms_login_risk.
type CmsLoginRisk struct {
	Id            uint   `json:"id"            orm:"id"             description:""`
	TabType       int    `json:"tabType"       orm:"tab_type"       description:"分类 [1 IP绑定管理, 2 机器码管理]"`
	Content       string `json:"content"       orm:"content"        description:"（1） tab_type = 1时 对应 IP地址（2） tab_type = 2时 对应机器码"`
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 开 2 关]"`
	Remark        string `json:"remark"        orm:"remark"         description:"备注"`
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`
	Creater       uint   `json:"creater"       orm:"creater"        description:"创建者"`
}
