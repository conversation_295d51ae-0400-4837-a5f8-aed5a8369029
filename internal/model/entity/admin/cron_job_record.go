// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package admin

// CronJobRecord is the golang structure for table cron_job_record.
type CronJobRecord struct {
	Id       int    `json:"id"       orm:"id"       description:""`
	Symbol   string `json:"symbol"   orm:"symbol"   description:"cron_job表里的标识"`
	RunAt    int64  `json:"runAt"    orm:"run_at"   description:"执行时间"`
	Status   int    `json:"status"   orm:"status"   description:"成功还是失败。 1成功"`
	Progress string `json:"progress" orm:"progress" description:"当前执行的状态"`
	Retry    int    `json:"retry"    orm:"retry"    description:"这是第几次重试"`
}
