// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserSigninLog is the golang structure for table user_signin_log.
type UserSigninLog struct {
	BigintUnsigned  uint   `json:"bigintUnsigned"  orm:"bigint unsigned"   description:""`
	UserId          uint   `json:"userId"          orm:"user_id"           description:"会员id"`
	VipLevel        int    `json:"vipLevel"        orm:"vip_level"         description:"会员当时vip等级"`
	SigninTime      int64  `json:"signinTime"      orm:"signin_time"       description:"登录时间"`
	Ip              string `json:"ip"              orm:"ip"                description:"登录ip（ip6长度为39字符）"`
	IpRegion        string `json:"ipRegion"        orm:"ip_region"         description:"ip地址位置"`
	DeviceId        string `json:"deviceId"        orm:"device_id"         description:"设备编号"`
	DeviceOs        string `json:"deviceOs"        orm:"device_os"         description:"设备系统（ios，android，mac，windows，。。。）"`
	DeviceOsVersion string `json:"deviceOsVersion" orm:"device_os_version" description:"设备系统版本号"`
	DeviceType      string `json:"deviceType"      orm:"device_type"       description:"设备类型（mobile手机，desktop台式，pad平板，。。。其他）"`
	AppType         string `json:"appType"         orm:"app_type"          description:"应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	AppVersion      string `json:"appVersion"      orm:"app_version"       description:"应用版本号"`
	Host            string `json:"host"            orm:"host"              description:"登录域名"`
}
