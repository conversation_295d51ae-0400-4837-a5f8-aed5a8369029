// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserKvData is the golang structure for table user_kv_data.
type UserKvData struct {
	Id         uint64 `json:"id"         orm:"id"          description:"唯一标识，自增主键"`
	UserId     uint64 `json:"userId"     orm:"user_id"     description:"所属用户的唯一 ID"`
	KeyPath    string `json:"keyPath"    orm:"key_path"    description:"以 dot 分隔的键路径，例如 \"profile.theme.color\""`
	ValueData  string `json:"valueData"  orm:"value_data"  description:"对应键的 JSON 数据值，支持任意结构"`
	ValueSize  int    `json:"valueSize"  orm:"value_size"  description:"value_data 的 UTF-8 字节长度，用于配额限制"`
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间"`
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"最后更新时间"`
}
