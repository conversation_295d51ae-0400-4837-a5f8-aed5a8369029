// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserInfo is the golang structure for table user_info.
type UserInfo struct {
	Id                   uint64 `json:"id"                   orm:"id"                      description:""`
	UserId               uint64 `json:"userId"               orm:"user_id"                 description:"关联 user 表主键"`
	Email                string `json:"email"                orm:"email"                   description:"邮箱地址"`
	BindEmailTime        int64  `json:"bindEmailTime"        orm:"bind_email_time"         description:"邮箱绑定时间"`
	BindRealNameTime     int64  `json:"bindRealNameTime"     orm:"bind_real_name_time"     description:"真实姓名绑定时间"`
	BindPhoneTime        int64  `json:"bindPhoneTime"        orm:"bind_phone_time"         description:"手机号绑定时间"`
	PhoneNum             string `json:"phoneNum"             orm:"phone_num"               description:"手机号"`
	PasswordModifyTime   int64  `json:"passwordModifyTime"   orm:"password_modify_time"    description:"登录密码支付密码最近修改时间"`
	CountryId            uint   `json:"countryId"            orm:"country_id"              description:"国家id"`
	Language             string `json:"language"             orm:"language"                description:"语言 : zh-CN:中文, id:Indonesian, en:English"`
	YearOfBirth          int    `json:"yearOfBirth"          orm:"year_of_birth"           description:"出生年"`
	MonthOfBirth         int    `json:"monthOfBirth"         orm:"month_of_birth"          description:"出生月"`
	DayOfBirth           int    `json:"dayOfBirth"           orm:"day_of_birth"            description:"出生日"`
	Avatar               string `json:"avatar"               orm:"avatar"                  description:"头像url"`
	Gender               string `json:"gender"               orm:"gender"                  description:"性别：0未知 1男 2女"`
	Nickname             string `json:"nickname"             orm:"nickname"                description:"昵称"`
	NicknameModifyTime   int64  `json:"nicknameModifyTime"   orm:"nickname_modify_time"    description:"昵称最近一次修改时间"`
	FirstName            string `json:"firstName"            orm:"first_name"              description:"第一个名字"`
	MiddleName           string `json:"middleName"           orm:"middle_name"             description:"中间名字"`
	LastName             string `json:"lastName"             orm:"last_name"               description:"最后一个名字"`
	Version              int64  `json:"version"              orm:"version"                 description:"该记录的版本号"`
	IdentityCard         string `json:"identityCard"         orm:"identity_card"           description:"身份证号码"`
	IdentityCardImgs     string `json:"identityCardImgs"     orm:"identity_card_imgs"      description:"身份证图片"`
	Contact              string `json:"contact"              orm:"contact"                 description:"社交联系方式 wechat qq等"`
	Address              string `json:"address"              orm:"address"                 description:"住址"`
	CreateAccount        string `json:"createAccount"        orm:"create_account"          description:"创建者账号"`
	CreateType           int    `json:"createType"           orm:"create_type"             description:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`
	UpdateAccount        string `json:"updateAccount"        orm:"update_account"          description:"更新者账号"`
	UpdateType           int    `json:"updateType"           orm:"update_type"             description:"更新者来源"`
	DataType             int    `json:"dataType"             orm:"data_type"               description:"数据类型:1正式数据;2测试数据"`
	Source               int    `json:"source"               orm:"source"                  description:"注册来源( 1直客，2代理，3邀请，4后台）"`
	IsOnline             int    `json:"isOnline"             orm:"is_online"               description:"是否在线：1是  2 否"`
	SigninCount          int    `json:"signinCount"          orm:"signin_count"            description:"登录次数"`
	LastSigninTime       int64  `json:"lastSigninTime"       orm:"last_signin_time"        description:"最后一次登录时间"`
	LastSigninIp         string `json:"lastSigninIp"         orm:"last_signin_ip"          description:"最后登录ip"`
	LastSigninDeviceId   string `json:"lastSigninDeviceId"   orm:"last_signin_device_id"   description:"最后登录设备号"`
	LastSigninAppType    int    `json:"lastSigninAppType"    orm:"last_signin_app_type"    description:"最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	LastSigninAppVersion string `json:"lastSigninAppVersion" orm:"last_signin_app_version" description:"最近登录应用类型版本号"`
	CreateTime           int64  `json:"createTime"           orm:"create_time"             description:"创建时间"`
	UpdateTime           int64  `json:"updateTime"           orm:"update_time"             description:"更新时间"`
}
