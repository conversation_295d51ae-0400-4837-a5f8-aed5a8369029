// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package user_account_svc

// UserSignupLog is the golang structure for table user_signup_log.
type UserSignupLog struct {
	Id              uint64 `json:"id"              orm:"id"                description:""`
	UserId          uint64 `json:"userId"          orm:"user_id"           description:"关联 user 表主键"`
	Ip              string `json:"ip"              orm:"ip"                description:"注册ip"`
	IpRegion        string `json:"ipRegion"        orm:"ip_region"         description:"注册IP地理区域"`
	DeviceId        string `json:"deviceId"        orm:"device_id"         description:"注册设备号（设备指纹）"`
	DeviceOs        string `json:"deviceOs"        orm:"device_os"         description:"注册设备系统（android,ios,windows,mac,...）"`
	DeviceOsVersion string `json:"deviceOsVersion" orm:"device_os_version" description:"注册设备系统版本号"`
	DeviceType      int    `json:"deviceType"      orm:"device_type"       description:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"`
	AppType         int    `json:"appType"         orm:"app_type"          description:"应用类型（1:android  2: ios，3:h5，4:web，5:其他）"`
	AppVersion      string `json:"appVersion"      orm:"app_version"       description:"注册应用类型版本号"`
	CreateTime      int64  `json:"createTime"      orm:"create_time"       description:"创建时间（注册时间）"`
	UpdateTime      int64  `json:"updateTime"      orm:"update_time"       description:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`
}
