#!/bin/bash

#################################### 更新亚马逊 ##################################

# 跳板机地址和用户名
server="43.198.135.200"
user="ec2-user"
private_key="/Users/<USER>/Downloads/tools/gtcms"  # 本地私钥路径

# 编译二进制文件
gf build -o aapi main.go

# 将二进制文件上传到目标服务器
#sudo scp -i "$private_key" ./aapi "$user@$server":/home/<USER>/gtcms/
sudo scp -i "$private_key" "$user@$server":/home/<USER>/pprof/pprof.uapi.alloc_objects.alloc_space.inuse_objects.inuse_space.003.pb.gz /Users/<USER>/pprof
#sudo scp -i "$private_key" "$user@$server":/home/<USER>/gtcms_struct.sql /Users/<USER>


# 判断执行是否成功
if [ $? -eq 0 ]; then
    echo "远程脚本执行成功"
else
    echo "远程脚本执行失败"
fi
