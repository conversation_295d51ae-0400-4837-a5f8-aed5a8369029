package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type NewsListReq struct {
	g.Meta        `path:"/news/list" tags:"新闻资讯" method:"post" summary:"新闻资讯列表"`
	Title         *string `json:"title" dc:"标题"`
	BelongColId   *uint   `json:"belongColId" dc:"所属栏目id"`
	BelongGroupId *uint   `json:"belongGroupId" dc:"所属组id"`
	BelongSiteId  *uint   `json:"belongSiteId" dc:"所属站点id"`
	Status        *int    `json:"status"            dc:"状态(1:显示 2:隐藏)"`
	DomainName    *string `json:"domainName" dc:"所属域名"`
	IsHot         *int    `json:"isHot"       dc:"是否热门"`
	IsAi          *int    `json:"isAi"       dc:"是否ai生成"`
	ListReq
}

type NewsListRes struct {
	ListRes
	List []NewsItem `json:"list" dc:"列表"`
}

type NewsItem struct {
	Id              uint   `json:"id"                dc:""`
	BelongColId     uint   `json:"belongColId" dc:"所属栏目id"`
	BelongColName   string `json:"belongColName" dc:"所属栏目名称"`
	BelongGroupId   uint   `json:"belongGroupId" dc:"所属组id"`
	BelongGroupName string `json:"belongGroupName" dc:"所属组"`
	BelongSiteId    uint   `json:"belongSiteId" dc:"所属站点id"`
	BelongSiteName  string `json:"belongSiteName" dc:"所属站点"`
	Title           string `json:"title"       dc:"标题"`
	Desc            string `json:"desc"        dc:"简介"`
	Content         string `json:"content"     dc:"内容"`
	Label           string `json:"label"       dc:"标签"`
	SeoTitle        string `json:"seoTitle"    dc:"seo标题"`
	SeoKeyword      string `json:"seoKeyword"  dc:"seo关键词"`
	SeoDesc         string `json:"seoDesc"     dc:"seo描述"`
	FileName        string `json:"fileName"    dc:"自定义文件名"`
	Author          string `json:"author"      dc:"作者"`
	Resource        string `json:"resource"    dc:"来源"`
	Views           int    `json:"views"       dc:"浏览量"`
	Sort            int    `json:"sort"        dc:"排序"`
	Status          int    `json:"status"      dc:"状态(1:显示 2:隐藏)"`
	Attr            string `json:"attr"        dc:"属性"`
	Thumb           string `json:"thumb"       dc:"缩略图"`
	CreateTime      int64  `json:"createTime"  dc:"发布时间"`
	Creater         uint   `json:"creater"      dc:"创建者"`
	IsHot           int    `json:"isHot"       dc:"是否热门"`
	IsAi            int    `json:"isAi"       dc:"是否ai生成"`
}

type NewsAddReq struct {
	g.Meta        `path:"/news/add" tags:"新闻资讯" method:"post" summary:"添加新闻资讯"`
	BelongColId   uint   `v:"required" json:"belongColId" dc:"所属栏目id"`
	BelongColName string `v:"required" json:"belongColName" dc:"所属栏目名称"`
	BelongGroupId uint   `v:"required" json:"belongGroupId" dc:"所属组id"`
	BelongSiteId  uint   `v:"required" json:"belongSiteId" dc:"所属站点id"`
	Title         string `v:"required" json:"title"       dc:"标题"`
	Desc          string `json:"desc"        dc:"简介"`
	Content       string `json:"content"     dc:"内容"`
	Label         string `json:"label"       dc:"标签"`
	SeoTitle      string `json:"seoTitle"    dc:"seo标题"`
	SeoKeyword    string `json:"seoKeyword"  dc:"seo关键词"`
	SeoDesc       string `json:"seoDesc"     dc:"seo描述"`
	FileName      string `json:"fileName"    dc:"自定义文件名"`
	Author        string `json:"author"      dc:"作者"`
	Resource      string `json:"resource"    dc:"来源"`
	Views         int    `json:"views"       dc:"浏览量"`
	Sort          int    `json:"sort"        dc:"排序"`
	Status        int    `json:"status"      dc:"状态(1:显示 2:隐藏)"`
	Attr          string `json:"attr"        dc:"属性(1:视频)"`
	Thumb         string `json:"thumb"       dc:"缩略图"`
	CreateTime    int64  `json:"createTime"  dc:"发布时间"`
	IsAuto        int    `json:"isAuto" dc:"是否自动发布的(1:是 0:否)"`
	IsAi          int    `json:"isAi" dc:"是否ai生成(1:是 0:否)"`
}

type NewsAddRes struct {
	Id int64 `json:"id" dc:"新添记录的id"`
}

type NewsEditReq struct {
	g.Meta        `path:"/news/edit" tags:"新闻资讯" method:"post" summary:"编辑新闻资讯"`
	Id            uint    `v:"required" json:"id"         dc:"id"`
	BelongColId   *uint   `json:"belongColId" dc:"所属栏目id"`
	BelongColName *string `json:"belongColName"       dc:"所属栏目名称"`
	BelongGroupId *uint   `json:"belongGroupId" dc:"所属组id"`
	BelongSiteId  *uint   `json:"belongSiteId" dc:"所属站点id"`
	Title         *string `json:"title"       dc:"标题"`
	Desc          *string `json:"desc"        dc:"简介"`
	Content       *string `json:"content"     dc:"内容"`
	Label         *string `json:"label"       dc:"标签"`
	SeoTitle      *string `json:"seoTitle"    dc:"seo标题"`
	SeoKeyword    *string `json:"seoKeyword"  dc:"seo关键词"`
	SeoDesc       *string `json:"seoDesc"     dc:"seo描述"`
	FileName      *string `json:"fileName"    dc:"自定义文件名"`
	Author        *string `json:"author"      dc:"作者"`
	Resource      *string `json:"resource"    dc:"来源"`
	Views         *int    `json:"views"       dc:"浏览量"`
	Sort          *int    `json:"sort"        dc:"排序"`
	Status        *int    `json:"status"      dc:"状态(1:显示 2:隐藏)"`
	Attr          *string `json:"attr"        dc:"属性"`
	Thumb         *string `json:"thumb"       dc:"缩略图"`
	IsHot         *int    `json:"isHot"       dc:"是否热门"`
}

type NewsDeleteReq struct {
	g.Meta `path:"/news/delete" tags:"新闻资讯" method:"post" summary:"删除新闻资讯"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type NewsOneReq struct {
	g.Meta `path:"/news/one" method:"post" tags:"新闻资讯" summary:"新闻资讯->获取(点击编辑时，需要调用此接口)"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type NewsOneRes struct {
	NewsItem
}

type NewsUpdateStatusReq struct {
	g.Meta `path:"/news/updateStatus" tags:"新闻资讯" method:"post" summary:"批量更新新闻资讯状态"`
	Ids    []uint `v:"required" json:"id"         dc:"id"`
	Status int    `v:"required" json:"status"            dc:"状态(1:显示 2:隐藏)"`
}

type NewsImportTxtReq struct {
	g.Meta      `path:"/news/importTxt" method:"post" mime:"multipart/form-data" tags:"新闻管理" summary:"批量导入txt文件"`
	Files       []*ghttp.UploadFile `json:"files" type:"files" dc:"选择导入文件"`
	SiteGroupId uint                `json:"siteGroupId" dc:"站点分组id"`
	SiteIds     []uint              `json:"siteIds" dc:"站点id"`
	ColumnsId   uint                `json:"columnsId" dc:"栏目id"`
}
