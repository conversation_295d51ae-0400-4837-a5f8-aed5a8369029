package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type CmsLoginRiskAddReq struct {
	g.<PERSON>a  `path:"/cmsLoginRisk/add" method:"post" tags:"后台设置" summary:"IP机器码管理-新增"`
	TabType int    `v:"required|in:1,2" json:"tabType"       dc:"分类 [1 IP绑定管理, 2 机器码管理]"`
	Content string `v:"required"  json:"content"       dc:"1) tab_type = 1时对应IP地址 2) tab_type = 2时对应机器码"`
	IsOpen  int    `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	Remark  string `json:"remark"        dc:"备注"`
}

type CmsLoginRiskDeleteReq struct {
	g.Meta `path:"/cmsLoginRisk/delete" method:"post" tags:"后台设置" summary:"IP机器码管理-删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type CmsLoginRiskEditReq struct {
	g.<PERSON>a  `path:"/cmsLoginRisk/edit" method:"post" tags:"后台设置" summary:"IP机器码管理-编辑"`
	Id      uint   `v:"required" json:"id"`
	TabType int    `v:"required|in:1,2" json:"tabType"       dc:"分类 [1 IP绑定管理, 2 机器码管理]"`
	Content string `v:"required" json:"content"       dc:"1) tab_type = 1时对应 IP地址 2) tab_type = 2时 对应机器码"`
	IsOpen  int    `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	Remark  string `json:"remark"        dc:"备注"`
}

type CmsLoginRiskDetailReq struct {
	g.Meta `path:"/cmsLoginRisk/detail" method:"post" tags:"后台设置" summary:"IP机器码管理-详情"`
	Id     uint `v:" " json:"id"`
}
type CmsLoginRiskDetailRes struct {
	CmsLoginRiskVo
}

type CmsLoginRiskListReq struct {
	g.Meta `path:"/cmsLoginRisk/list" method:"post" tags:"后台设置" summary:"IP机器码管理-列表"`
	ListReq
	TabType int    `v:"in:1,2" json:"tabType"       dc:"分类 [1 IP绑定管理, 2 机器码管理]"`
	Key     string `json:"key"       dc:"1) tab_type = 1时对应 IP地址 2) tab_type = 2时 对应机器码"`
	IsOpen  int    `v:"in:1,2" json:"isOpen"        dc:"状态 [ 1 开 2 关]"`

	StartTime int64 `json:"startTime" dc:"开始时间"`
	EndTime   int64 `json:"endTime" dc:"结束时间"`
}
type CmsLoginRiskListRes struct {
	ListRes
	List []*CmsLoginRiskVo `json:"list"`
}

type CmsLoginRiskVo struct {
	Id         uint   `json:"id"            dc:""`
	TabType    int    `json:"tabType"       dc:"分类 [1 IP绑定管理, 2 机器码管理]"`
	Content    string `json:"content"       dc:"1) tab_type = 1时对应 IP地址 2) tab_type = 2时 对应机器码"`
	IsOpen     int    `json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	Remark     string `json:"remark"        dc:"备注"`
	CreateTime int64  `json:"createTime" dc:"创建时间"`
}
