package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type PermissionMgrListReq struct {
	g.Meta `path:"/permissionMgr/list" method:"post" tags:"权限管理-V2" summary:"列表"`
	ListReq
}
type PermissionMgrListRes struct {
	ListRes
	Items []*PermissionNode `json:"items" dc:"权限列表"`
}

type PermissionNode struct {
	Id      uint   `json:"id"      dc:""`
	PId     uint   `json:"pId"     dc:"父权限id（顶级为0）"`
	Name    string `json:"name"    dc:"菜单编号（权限编号）"`
	Label   string `json:"label"   dc:"显示名称（中），逗号分隔"`
	OrderBy int    `json:"orderBy" dc:"排序"`
	*PermissionAttrs
	ChildNodes []*PermissionNode `json:"childNodes" dc:"子权限"`
}

type PermissionAttrs struct {
	UrlPath         string            `json:"urlPath"      dc:"api路径"`
	MapMaskedFields map[string]string `json:"mapMaskedFields" dc:"掩码字段 map结构 eg: <账号:List.Account>"`
}

type PermissionMgrAddReq struct {
	g.Meta  `path:"/permissionMgr/add" method:"post" tags:"权限管理-V2" summary:"新增"`
	PId     uint   `v:"required" json:"pId" dc:"父权限id，顶级为0"`
	Name    string `v:"required" json:"name" dc:"菜单编号（权限编号）"`
	Label   string `v:"required" json:"label" dc:"显示名称"`
	OrderBy int    `json:"orderBy" dc:"排序，默认0"`
	*PermissionAttrs
}

type PermissionMgrEditReq struct {
	g.Meta  `path:"/permissionMgr/edit" method:"post" tags:"权限管理-V2" summary:"编辑"`
	Id      uint   `v:"required|min:0" json:"id"`
	PId     uint   `v:"required" json:"pId" dc:"父权限id，顶级为0"`
	Name    string `v:"required" json:"name" dc:"菜单编号（权限编号）"`
	Label   string `v:"required" json:"label" dc:"显示名称"`
	OrderBy int    `json:"orderBy" dc:"排序，默认0"`
	*PermissionAttrs
}

type PermissionMgrDeleteReq struct {
	g.Meta `path:"/permissionMgr/delete" method:"post" tags:"权限管理-V2" summary:"删除"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}

type PermissionMgrDetailReq struct {
	g.Meta `path:"/permissionMgr/detail" method:"post" tags:"权限管理-V2" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}
type PermissionMgrDetailRes struct {
	PermissionNode
}

type PermissionMgrChildReq struct {
	g.Meta `path:"/permissionMgr/childNodes" method:"post" tags:"权限管理-V2" summary:"子权限"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}
type PermissionMgrChildRes struct {
	Child []*PermissionNode
}

type PermissionMgrMaskReq struct {
	g.Meta `path:"/permissionMgr/parseMark" method:"post" tags:"权限管理-V2" summary:"测试敏感词"`
	Id     uint `v:"required" json:"id" dc:"权限id"`
}
