package v1

import "github.com/gogf/gf/v2/frame/g"

// 体育数据 球员信息

type CollectFootballPlayerListReq struct {
	g.Meta `path:"/collect/football/player/list" method:"post" tags:"体育数据" summary:"足球球员列表"`
	ListReq
	Id     int    `json:"id" dc:"按球队id过滤(数组中只会有一条)"`
	Name   string `json:"name" dc:"按名称过滤"`
	TeamId int    `json:"teamId" dc:"按球队id进行过滤（即地区）"`
	IsHot  int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectFootballPlayerListRes struct {
	ListRes
	Items []FootballPlayer `json:"items" dc:"球员数据"`
}

type CollectFootballPlayerEditReq struct {
	g.Meta `path:"/collect/football/player/edit" method:"post" tags:"体育数据" summary:"足球球员编辑"`
	Id     int `json:"id" dc:"球员id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo *string `json:"logo" dc:"logo url 相对路径"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectFootballPlayerEditRes struct {
	EmptyDataRes
}

type CollectBasketballPlayerListReq struct {
	g.Meta `path:"/collect/basketball/player/list" method:"post" tags:"体育数据" summary:"篮球球员列表"`
	ListReq
	Id     int    `json:"id" dc:"按球队id过滤(数组中只会有一条)"`
	Name   string `json:"name" dc:"按名称过滤"`
	TeamId int    `json:"teamId" dc:"按球队id进行过滤（即地区）"`
	IsHot  int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectBasketballPlayerListRes struct {
	ListRes
	Items []BasketballPlayer `json:"items" dc:"球员数据"`
}

type CollectBasketballPlayerEditReq struct {
	g.Meta `path:"/collect/basketball/player/edit" method:"post" tags:"体育数据" summary:"篮球球员编辑"`
	Id     int `json:"id" dc:"球员id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo     *string `json:"logo" dc:"logo url 相对路径"`
	National *int    `json:"national"      description:"是否国家队，1-是、0-否"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectBasketballPlayerEditRes struct {
	EmptyDataRes
}

type FootballPlayer struct {
	Id int `json:"id"                  description:""`
	//CoachId             int    `json:"coachId"             description:"教练id"`
	CollectName
	CollectShortName
	Logo                string `json:"logo"                description:"球队logo"`
	LogoFull            string `json:"logoFull"                description:"球队logo"`
	CountryId           int    `json:"countryId" dc:"国家id"`
	Nationality         string `json:"nationality" dc:"国籍"`
	NationalLogo        string `json:"nationalLogo" dc:"国家队球员logo"`
	NationalLogoFull    string `json:"nationalLogoFull" dc:"国家队球员logo"`
	Birthday            int    `json:"birthday" dc:"生日 时间戳"`
	Age                 int    `json:"age" dc:"年龄"`
	Height              int    `json:"height" dc:"身高"`
	Weight              int    `json:"weight" dc:"体重"`
	MarketValue         int    `json:"marketValue"         description:"市值"`
	MarketValueCurrency string `json:"marketValueCurrency" description:"市值单位"`
	ContractUntil       int    `json:"contractUntil" dc:"合同截止时间，时间戳"`
	Position            string `json:"position"            description:"擅长位置,F-前锋、M-中场、D-后卫、G-守门员、其他为未知"`
	SEOInfo
	ExtraInfo
}

type BasketballPlayer struct {
	Id int `json:"id"            description:""`
	//CoachId       int    `json:"coachId"       description:"教练id"`
	CollectName
	CollectShortName
	Logo             string `json:"logo"          description:"球队logo"`
	LogoFull         string `json:"logoFull"                description:"球队logo"`
	CountryId        int    `json:"countryId"       description:"国家id"`
	NationalLogo     string `json:"nationalLogo"    description:"球员logo(国家队，"`
	NationalLogoFull string `json:"nationalLogoFull"    description:"球员logo(国家队"`
	Birthday         int    `json:"birthday"        description:"生日"`
	Age              int    `json:"age"             description:"年龄"`
	Height           int    `json:"height"          description:"身高"`
	Weight           int    `json:"weight"          description:"体重"`
	ContractUntil    string `json:"contractUntil"   description:"合同截止时间"`
	Position         string `json:"position"        description:"擅长位置"`
	Drafted          string `json:"drafted"         description:"选秀顺位"`
	LeagueCareerAge  int    `json:"leagueCareerAge" description:"联盟球龄"`
	Salary           int    `json:"salary"          description:"年薪$"`
	ShirtNumber      int    `json:"shirtNumber"     description:"球衣号"`
	School           string `json:"school"          description:"毕业学校"`
	City             string `json:"city"            description:"城市"`

	SEOInfo
	ExtraInfo
}
