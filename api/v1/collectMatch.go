package v1

import "github.com/gogf/gf/v2/frame/g"

// 体育数据 比赛信息

type CollectFootballMatchListReq struct {
	g.Meta `path:"/collect/football/match/list" method:"post" tags:"体育数据" summary:"足球比赛列表"`
	ListReq
	Id              int    `json:"id" dc:"按比赛id过滤(数组中只会有一条)"`
	CompetitionName string `json:"competitionName" dc:"赛事名称"`
	HomeName        string `json:"homeName" dc:"按主队名称过滤"`
	AwayName        string `json:"awayName" dc:"按客队名称过滤"`
	MatchTsStart    int64  `json:"matchTsStart" dc:"比赛开赛时间，时间戳秒"`
	MatchTsEnd      int64  `json:"matchTsEnd" dc:"比赛开赛时间，时间戳秒"`
	IsTop           int    `json:"isTop" dc:"是否置顶 1:是 2:否"`
	StatusIds       int    `json:"statusIds" dc:"格式：1,2,3。比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+ 取消/中断/推迟... 详见文档（注意：不同项目的状态id不同"`
}

type CollectFootballMatchListRes struct {
	ListRes
	Items []FootballMatch `json:"items" dc:"球员数据"`
}

type CollectFootballMatchEditReq struct {
	g.Meta  `path:"/collect/football/match/edit" method:"post" tags:"体育数据" summary:"足球比赛编辑"`
	Id      int     `json:"id" dc:"比赛id"`
	Title   *string `json:"title" dc:"标题"`
	MatchTs *int64  `json:"matchTs" dc:"比赛开赛时间，时间戳秒"`

	SEOInfoEdit
	ExtraInfoEdit
}

type VideoLive struct {
	PushUrl    string `json:"pushUrl" dc:"推流地址"`
	SourceName string `json:"sourceName" dc:"来源名"`
}

type VideoRecord struct {
	Url        string `json:"url" dc:"录播地址"`
	SourceName string `json:"sourceName" dc:"来源名"`
}

type CollectFootballMatchEditRes struct {
	EmptyDataRes
}

type CollectBasketballMatchListReq struct {
	g.Meta `path:"/collect/basketball/match/list" method:"post" tags:"体育数据" summary:"篮球比赛列表"`
	ListReq
	Id              int    `json:"id" dc:"按比赛id过滤(数组中只会有一条)"`
	CompetitionName string `json:"competitionName" dc:"赛事名称"`
	HomeName        string `json:"homeName" dc:"按主队名称过滤"`
	AwayName        string `json:"awayName" dc:"按客队名称过滤"`
	MatchTsStart    int64  `json:"matchTsStart" dc:"比赛开赛时间，时间戳秒"`
	MatchTsEnd      int64  `json:"matchTsEnd" dc:"比赛开赛时间，时间戳秒"`
	IsTop           int    `json:"isTop" dc:"是否置顶 1:是 2:否"`
	StatusIds       int    `json:"statusIds" dc:"格式：1,2,3。比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+ 取消/中断/推迟... 详见文档（注意：不同项目的状态id不同"`
}

type CollectBasketballMatchListRes struct {
	ListRes
	Items []BasketballMatch `json:"items" dc:"球员数据"`
}

type CollectBasketballMatchEditReq struct {
	g.Meta  `path:"/collect/basketball/match/edit" method:"post" tags:"体育数据" summary:"篮球比赛编辑"`
	Id      int     `json:"id" dc:"球员id"`
	Title   *string `json:"title" dc:"标题"`
	MatchTs *int64  `json:"matchTs" dc:"比赛开赛时间，时间戳秒"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectBasketballMatchEditRes struct {
	EmptyDataRes
}

type FootballMatch struct {
	Id            int64  `json:"id"                  description:""`
	SeasonId      int    `json:"seasonId"      description:"赛季id"`
	CompetitionId int    `json:"competitionId" description:"赛事id"`
	HomeTeamId    int    `json:"homeTeamId"    description:"主队id"`
	AwayTeamId    int    `json:"awayTeamId"    description:"客队id"`
	Kind          int    `json:"kind"          description:"比赛类型  1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无"`
	StatusId      int    `json:"statusId"      description:"比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+ 取消/中断/推迟... 详见文档（注意：不同项目的状态id不同"`
	MatchTime     string `json:"matchTime"     description:"比赛时间， yyyy-mm-dd HH:MM:SS"`
	MatchTs       int64  `json:"matchTs"       description:"比赛时间的时间戳"`
	VenueId       int    `json:"venueId"       description:"场馆id"`
	HomeScores    string `json:"homeScores"    description:"主队比分"`
	AwayScores    string `json:"awayScores"    description:"客队比分"`

	Title           string        `json:"title" dc:"比赛标题"`
	CompetitionName string        `json:"competitionName" description:"赛事名"`
	HomeName        string        `json:"homeName" dc:"主队名"`
	AwayName        string        `json:"awayName" dc:"客队名"`
	Lives           []VideoLive   `json:"lives" dc:"直播源"`
	Records         []VideoRecord `json:"records" dc:"录播源"`
	SEOInfo
	ExtraInfo
}

type BasketballMatch struct {
	Id              int           `json:"id"            description:""`
	SeasonId        int           `json:"seasonId"       description:"赛季id"`
	CompetitionId   int           `json:"competitionId"  description:"赛事id"`
	HomeTeamId      int           `json:"homeTeamId"     description:"主队id"`
	AwayTeamId      int           `json:"awayTeamId"     description:"客队id"`
	Kind            int           `json:"kind"           description:"比赛类型  1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无"`
	StatusId        int           `json:"statusId"       description:"状态id, 见文档"`
	MatchTime       string        `json:"matchTime"      description:"比赛时间， yyyy-mm-dd HH:MM:SS"`
	MatchTs         int64         `json:"matchTs"        description:"比赛时间的时间戳"`
	VenueId         int           `json:"venueId"        description:"场馆id"`
	HomeScores      string        `json:"homeScores"     description:"主队比分"`
	AwayScores      string        `json:"awayScores"     description:"客队比分"`
	OverTimeScores  string        `json:"overTimeScores" description:"加时赛比分"`
	Title           string        `json:"title" dc:"比赛标题"`
	CompetitionName string        `json:"competitionName" description:"赛事名"`
	HomeName        string        `json:"homeName" dc:"主队名"`
	AwayName        string        `json:"awayName" dc:"客队名"`
	Lives           []VideoLive   `json:"lives" dc:"直播源"`
	Records         []VideoRecord `json:"records" dc:"录播源"`
	SEOInfo
	ExtraInfo
}

type CollectSnookerMatchListReq struct {
	g.Meta `path:"/collect/snooker/match/list" method:"post" tags:"体育数据" summary:"台球比赛列表"`
	ListReq
	Id              int    `json:"id" dc:"按比赛id过滤(数组中只会有一条)"`
	CompetitionName string `json:"competitionName" dc:"赛事名称"`
	HomeName        string `json:"homeName" dc:"按主队名称过滤"`
	AwayName        string `json:"awayName" dc:"按客队名称过滤"`
	MatchTsStart    int64  `json:"matchTsStart" dc:"比赛开赛时间，时间戳秒"`
	MatchTsEnd      int64  `json:"matchTsEnd" dc:"比赛开赛时间，时间戳秒"`
	IsTop           int    `json:"isTop" dc:"是否置顶 1:是 2:否"`
	StatusIds       int    `json:"statusIds" dc:"格式：1,2,3。比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+ 取消/中断/推迟... 详见文档（注意：不同项目的状态id不同"`
}

type CollectSnookerMatchListRes struct {
	ListRes
	Items []SnookerMatch `json:"items" dc:"球员数据"`
}

type CollectSnookerMatchEditReq struct {
	g.Meta  `path:"/collect/snooker/match/edit" method:"post" tags:"体育数据" summary:"台球比赛编辑"`
	Id      int     `json:"id" dc:"球员id"`
	Title   *string `json:"title" dc:"标题"`
	MatchTs *int64  `json:"matchTs" dc:"比赛开赛时间，时间戳秒"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectSnookerMatchEditRes struct {
	EmptyDataRes
}

type SnookerMatch struct {
	Id            int64  `json:"id"                  description:""`
	SeasonId      int    `json:"seasonId"      description:"赛季id"`
	CompetitionId int    `json:"competitionId" description:"赛事id"`
	HomeTeamId    int    `json:"homeTeamId"    description:"主队id"`
	AwayTeamId    int    `json:"awayTeamId"    description:"客队id"`
	Kind          int    `json:"kind"          description:"比赛类型  1-常规赛、2-季后赛、3-季前赛、4-全明星、5-杯赛、6-附加赛、0-无"`
	StatusId      int    `json:"statusId"      description:"比赛状态 0异常，1未开场，2上半，3中场，4下半，5加时，7点球，8完赛 9+ 取消/中断/推迟... 详见文档（注意：不同项目的状态id不同"`
	MatchTime     string `json:"matchTime"     description:"比赛时间， yyyy-mm-dd HH:MM:SS"`
	MatchTs       int64  `json:"matchTs"       description:"比赛时间的时间戳"`
	VenueId       int    `json:"venueId"       description:"场馆id"`
	HomeScores    string `json:"homeScores"    description:"主队比分"`
	AwayScores    string `json:"awayScores"    description:"客队比分"`

	Title           string        `json:"title" dc:"比赛标题"`
	CompetitionName string        `json:"competitionName" description:"赛事名"`
	HomeName        string        `json:"homeName" dc:"主队名"`
	AwayName        string        `json:"awayName" dc:"客队名"`
	Lives           []VideoLive   `json:"lives" dc:"直播源"`
	Records         []VideoRecord `json:"records" dc:"录播源"`
	SEOInfo
	ExtraInfo
}
