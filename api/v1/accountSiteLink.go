package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type AccountSiteLinkListReq struct {
	g.Meta `path:"/accountSiteLink/list" method:"post" tags:"站员站点配置" summary:"列表"`
	ListReq
	AccountId uint `v:"required" json:"accountId" dc:"站员账号id"`
	GroupId   uint `json:"groupId"        dc:"组id"`
	SiteId    uint `json:"siteId"        dc:"站点id"`
	//StartTime int64 `json:"startTime" dc:"开始时间"`
	//EndTime   int64 `json:"endTime" dc:"结束时间"`
}

type AccountSiteLinkListRes struct {
	ListRes
	List []*AccountSiteLinkVo `json:"list"`
}

type AccountSiteLinkVo struct {
	Id        uint   `json:"id"            dc:"主键ID"`
	GroupId   uint   `json:"groupId"       dc:"组id"`
	GroupName string `json:"groupName"     dc:"分组名称"`
	SiteId    uint   `json:"siteId"        dc:"站点id"`
	SiteName  string `json:"siteName"      dc:"站点名称"`
	IsAffect  int    `json:"isAffect"      dc:"1:已配置 2:未配置"`
	//AccountId   uint   `json:"accountId"     dc:"站员账号id"`
	//AccountName string `json:"accountName"   dc:"账号名"`
}

type AccountSiteLinkAddReq struct {
	g.Meta    `path:"/accountSiteLink/add" method:"post" tags:"站员站点配置" summary:"新增"`
	AccountId uint `v:"required" json:"accountId"     dc:"站员账号id"`
	GroupId   uint `json:"groupId"       dc:"组ID"`
	SiteId    uint `json:"siteId"        dc:"站点id"`
}

type AccountSiteLinkDeleteReq struct {
	g.Meta    `path:"/accountSiteLink/delete" method:"post" tags:"站员站点配置" summary:"删除"`
	AccountId uint `v:"required" json:"accountId"     dc:"账号id"`
	Id        uint `v:"required" json:"id" dc:"id"`
}

//type AccountSiteLinkEditReq struct {
//	g.Meta `path:"/accountSiteLink/edit" method:"post" tags:"站员站点配置" summary:"编辑"`
//	Id     uint `json:"id"            dc:""`
//	IsOpen int  `json:"isOpen"        dc:"是否启用：1开 2关"`
//}

type AccountSiteLinkDetailReq struct {
	g.Meta `path:"/accountSiteLink/detail" method:"post" tags:"站员站点配置" summary:"详情"`
	Id     uint `v:" " json:"id"`
}
type AccountSiteLinkDetailRes struct {
	AccountSiteLinkVo
}
