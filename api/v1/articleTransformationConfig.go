package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type ArticleTransformationConfigAddReq struct {
	g.Meta         `path:"/articleTransformationConfig/add" method:"post" tags:"第三方配置" summary:"新增"`
	TabType        int    `json:"tabType"        dc:"栏目分类[1 翻译接口, 2 伪原创接口, 3 其他接口]"`
	Choice         int    `json:"choice"         dc:"选择[1 百度翻译, 2 腾讯翻译, 3 有道翻译]"`
	IsOpen         int    `json:"isOpen"         dc:"启用 [ 1 开 2 关]"`
	AppId          string `json:"appId"          dc:"AppID"`
	Token          string `json:"token"          dc:"token"`
	IsAutoDetect   int    `json:"isAutoDetect"   dc:"自动检测 [ 1 开  2 关 ]"`
	SourceLanguage int    `json:"sourceLanguage" dc:"源语言[ 1 中文  2 英文 ]"`
	DestLanguage   int    `json:"destLanguage"   dc:"目标语言[ 1 中文 2 英文 ]"`
	Domain         int    `json:"domain"          dc:"领域[ 1 体育 ]"`
}

type ArticleTransformationConfigDeleteReq struct {
	g.Meta `path:"/articleTransformationConfig/delete" method:"post" tags:"第三方配置" summary:"删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type ArticleTransformationConfigEditReq struct {
	g.Meta         `path:"/articleTransformationConfig/edit" method:"post" tags:"第三方配置" summary:"编辑"`
	Id             uint   `json:"id"             dc:""`
	TabType        int    `json:"tabType"        dc:"栏目分类[1 翻译接口, 2 伪原创接口, 3 其他接口]"`
	Choice         int    `json:"choice"         dc:"选择[1 百度翻译, 2 腾讯翻译, 3 有道翻译]"`
	IsOpen         int    `json:"isOpen"         dc:"启用 [ 1 开 2 关]"`
	AppId          string `json:"appId"          dc:"AppID"`
	Token          string `json:"token"          dc:"token"`
	IsAutoDetect   int    `json:"isAutoDetect"   dc:"自动检测 [ 1 开  2 关 ]"`
	SourceLanguage int    `json:"sourceLanguage" dc:"源语言[ 1 中文  2 英文 ]"`
	DestLanguage   int    `json:"destLanguage"   dc:"目标语言[ 1 中文 2 英文 ]"`
	Domain         int    `json:"domain"          dc:"领域[ 1 体育 ]"`
}

type ArticleTransformationConfigDetailReq struct {
	g.Meta `path:"/articleTransformationConfig/detail" method:"post" tags:"第三方配置" summary:"详情"`
	Id     uint `v:" " json:"id"`
}
type ArticleTransformationConfigDetailRes struct {
	ArticleTransformationConfigVo
}

type ArticleTransformationConfigListReq struct {
	g.Meta `path:"/articleTransformationConfig/list" method:"post" tags:"第三方配置" summary:"列表"`
	ListReq
	TabType int `json:"tabType"       dc:"栏目分类[1 翻译接口, 2 伪原创接口, 3 其他接口]"`
}
type ArticleTransformationConfigListRes struct {
	ListRes
	List []*ArticleTransformationConfigVo `json:"list"`
}

type ArticleTransformationConfigVo struct {
	Id             uint   `json:"id"             dc:""`
	TabType        int    `json:"tabType"        dc:"栏目分类[1 翻译接口, 2 伪原创接口, 3 其他接口]"`
	Choice         int    `json:"choice"         dc:"选择[1 百度翻译, 2 腾讯翻译, 3 有道翻译]"`
	IsOpen         int    `json:"isOpen"         dc:"启用 [ 1 开 2 关]"`
	AppId          string `json:"appId"          dc:"AppID"`
	Token          string `json:"token"          dc:"token"`
	IsAutoDetect   int    `json:"isAutoDetect"   dc:"自动检测 [ 1 开  2 关 ]"`
	SourceLanguage int    `json:"sourceLanguage" dc:"源语言[ 1 中文  2 英文 ]"`
	DestLanguage   int    `json:"destLanguage"   dc:"目标语言[ 1 中文 2 英文 ]"`
	Domain         int    `json:"domain"          dc:"领域[ 1 体育 ]"`
}
