package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type HomePageInfoReq struct {
	g.Meta `path:"/home/<USER>" method:"post" tags:"后台首页" summary:"默认数据"`
}

type HomePageInfoRes struct {
	Website           StatWebsite     `json:"website" dc:"网点数据"`
	SpiderEngines     []*SpiderEngine `json:"spiderEngines" dc:"搜索引擎描述"`
	SpiderPvYesterday []*SpiderPvHour `json:"spiderPvYesterday" dc:"昨天蜘蛛统计"`
	SpiderPvToday     []*SpiderPvHour `json:"spiderPvToday" dc:"今日蜘蛛统计"`

	RankSitePvs    []*RankSitePv    `json:"rankSitePvs"      dc:"访问排行"`
	RankInclusions []*RankInclusion `json:"rankInclusions"  dc:"收录排行"`
	RankTags       []*RankTag       `json:"rankTags"      dc:"热门标签"`
}

type StatWebsite struct {
	NumUptime uint `json:"numUptime" dc:"运行天数"`
	NumSite   uint `json:"numSite"   dc:"总站个数"`
	NumNews   uint `json:"numNews"   dc:"新闻资讯"`
	NumLive   uint `json:"numLive"   dc:"赛场直播场"`
	NumColumn uint `json:"numColumn" dc:"栏目总数"`
	NumKey    uint `json:"numKey"    dc:"关键词总数"`
	NumTag    uint `json:"numTag"    dc:"Tag标签总数"`
	NumLink   uint `json:"numLink"   dc:"友情链接数"`
}

type SpiderEngine struct {
	EngineId int    `json:"engineId" dc:"搜索引擎ID"`
	Icon     string `json:"icon" dc:"图标"`
	Title    string `json:"title" dc:"标题"`
	NumVisit uint   `json:"numVisit" dc:"访问总数"`
}
type SpiderPvHour struct {
	Date     *gtime.Time `json:"date"     dc:"汇总日期"`
	Hour     int         `json:"hour"     dc:"汇总小时"`
	EngineId int         `json:"engineId" dc:"搜索引擎ID"`
	NumVisit uint        `json:"numVisit" dc:"访问次数"`
}

type RankSitePv struct {
	RankComm
}

type RankInclusion struct {
	RankComm
}

type RankComm struct {
	Rank       uint        `json:"rank"       dc:"排名"`
	Date       *gtime.Time `json:"date"     dc:"汇总日期"`
	SiteId     int         `json:"siteId"   dc:"站点ID"`
	SiteClass  string      `json:"siteClass" dc:"站点分组"`
	SiteName   string      `json:"siteName" dc:"站点名称"`
	SiteDomain string      `json:"siteDomain" dc:"站点域名"`
	NumPv      uint        `json:"numPv"       dc:"今日访问数"`
}

type RankTag struct {
	SiteId    int    `json:"siteId"   dc:"站点ID"`
	SiteClass string `json:"siteClass" dc:"站点分组"`
	Tag       string `json:"tag" dc:"标签"`
	NumPv     uint   `json:"numPv"       dc:"今日访问数"`
}

type HomePageSpiderSearchReq struct {
	g.Meta `path:"/home/<USER>" method:"post" tags:"后台首页" summary:"蜘蛛统计查询"`
	Id     uint `v:"required" json:"id" dc:"蜘蛛ID"`
}

type HomePageSpiderSearchRes struct {
	SpiderPvYesterday []*SpiderPvHour `json:"spiderPvYesterday" dc:"昨天蜘蛛统计"`
	SpiderPvToday     []*SpiderPvHour `json:"spiderPvToday" dc:"今日蜘蛛统计"`
}
