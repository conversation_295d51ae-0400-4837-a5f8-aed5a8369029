package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type SelectorConfigAddReq struct {
	g.Meta     `path:"/selectorConfig/add" method:"post" tags:"数据配置" summary:"新增"`
	SelectType string `v:"required" json:"selectType"    dc:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	Title      string `v:"required" json:"title"         dc:"选择器类型对应的配置名称"`
	IsOpen     int    `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 启用  2 禁用]"`
	Sort       int    `json:"sort"          dc:"排序"`
	Remark     string `json:"remark"        dc:"备注"`
	Extra      string `json:"extra"    d:"{}"    dc:"额外数据：如注册商底下的平台账号{'account':['11', '22', '33']}"`
}

type SelectorConfigExtra struct {
	Account string `json:"account" dc:"账号"`
}

type SelectorConfigDeleteReq struct {
	g.Meta `path:"/selectorConfig/delete" method:"post" tags:"数据配置" summary:"删除"`
	Id     uint `v:"required" json:"id" dc:"id"`
}

type SelectorConfigEditReq struct {
	g.Meta     `path:"/selectorConfig/edit" method:"post" tags:"数据配置" summary:"编辑"`
	Id         uint   `v:"required" json:"id"`
	SelectType string `v:"required" json:"selectType"    dc:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	Title      string `v:"required" json:"title"         dc:"选择器类型对应的配置名称"`
	IsOpen     int    `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 启用  2 禁用]"`
	Sort       int    `json:"sort"          dc:"排序"`
	Remark     string `json:"remark"        dc:"备注"`
	Extra      string `json:"extra"        dc:"额外数据：如注册商底下的平台账号{'account':['11', '22', '33']}"`
}

type SelectorConfigDetailReq struct {
	g.Meta `path:"/selectorConfig/detail" method:"post" tags:"数据配置" summary:"详情"`
	Id     uint `v:" " json:"id"`
}
type SelectorConfigDetailRes struct {
	SelectorConfigVo
}

type SelectorConfigListReq struct {
	g.Meta `path:"/selectorConfig/list" method:"post" tags:"数据配置" summary:"列表"`
	ListReq
	SelectType string `json:"selectType"    dc:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	Key        string `json:"key"       dc:"名称（模糊匹配）"`
	IsOpen     int    `json:"isOpen"        dc:"状态 [ 1 开 2 关]"`

	StartTime int64 `json:"startTime" dc:"开始时间"`
	EndTime   int64 `json:"endTime" dc:"结束时间"`
}
type SelectorConfigListRes struct {
	ListRes
	List []*SelectorConfigVo `json:"list"`
}

type SelectorConfigVo struct {
	Id         uint   `json:"id"            dc:""`
	SelectType string ` json:"selectType"    dc:"选择器类型[1 站点语言, 2 文章属性, 3 域名状态, 4 域名平台,  5 域名后缀]"`
	Title      string `  json:"title"         dc:"选择器类型对应的配置名称"`
	IsOpen     int    ` json:"isOpen"        dc:"状态 [ 1 启用  2 禁用]"`
	Sort       int    `json:"sort"          dc:"排序"`
	Remark     string `json:"remark"        dc:"备注"`
	Extra      string `json:"extra"        dc:"额外数据：如注册商底下的平台账号{'account':['11', '22', '33']}"`
}
