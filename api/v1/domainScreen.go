package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type DomainScreenDropcatch struct {
	Code int             `json:"code"` // 返回响应代码
	Msg  string          `json:"msg"`  // 返回文字描述
	Data []DropcatchData `json:"data"` // 返回数据
}

type DropcatchData struct {
	ID           int    `json:"id"`                // 预订ID
	Domain       string `json:"domain"`            // 预订域名
	RegDate      string `json:"reg_date"`          // 注册时间
	DeletionDate string `json:"deletion_date"`     // 删除日期
	DeletionType int    `json:"deletion_type"`     // 删除类型
	DeletionName string `json:"deletion_typename"` // 删除类型名称
	Length       int    `json:"length"`            // 长度
	Valuation    string `json:"valuation"`         // 估价
	DA           int    `json:"da"`                // DA
	PA           int    `json:"pa"`                // PA
	HA           int    `json:"ha"`                // 历史评分
	IsBackorder  int    `json:"is_backorder"`      // 是否可预订
	WebsiteAge   int    `json:"website_age"`       // 建站年龄
	DomainAge    int    `json:"domain_age"`        // 域名年龄
	SogouRecord  int    `json:"sogou_record"`      // 搜狗收录
	BaiduRecord  int    `json:"baidu_record"`      // 百度收录
	WechatStatus int    `json:"wechat_status"`     // 微信检测
	QQStatus     int    `json:"qq_status"`         // QQ检测
	GwallStatus  int    `json:"gwall_status"`      // 被墙检测
}

type DomainScreenReq struct {
	g.Meta `path:"/domainScreen/list" tags:"域名筛选" method:"post" summary:"域名筛选"`
	ListReq
	ExcValue     *string `json:"excValue" dc:"域名排除内容"`
	Tld          *string `json:"tld" dc:"域名后缀(多个用英文逗号分隔)"`
	DeletionDate *string `json:"deletionDate" dc:"删除时间(格式:yyyy-mm-dd)"`
	DeletionType *string `json:"deletionType" dc:"删除类型(多个用英文逗号分隔)"`
	RegYearMin   *int    `json:"regYearMin" dc:"注册年限最小值(格式:2022)"`
	RegYearMax   *int    `json:"regYearMax" dc:"注册年限最大值(格式:2024)"`
	GwallStatus  *int    `json:"gwallStatus" dc:"被墙检测"` //
	Sortby       *string `json:"sortby" dc:"排序字段"`
}

type DomainScreenRes struct {
	ListRes
	List []DropcatchData `json:"list"`
}
