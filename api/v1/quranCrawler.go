package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// QuranCrawlerStartReq 古兰经数据爬取请求
type QuranCrawlerStartReq struct {
	g.Meta `path:"/quran-crawler/start" method:"get" summary:"开始爬取古兰经数据" tags:"古兰经爬虫"`
	Force  bool `json:"force" dc:"是否强制重新爬取（覆盖已存在数据），否则就是增量更新"`
}

// QuranCrawlerStartRes 古兰经数据爬取响应
type QuranCrawlerStartRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"响应消息"`
}

// QuranCrawlerStatusReq 古兰经数据状态查询请求
type QuranCrawlerStatusReq struct {
	g.Meta `path:"/quran-crawler/status" method:"get" summary:"查询古兰经数据状态" tags:"古兰经爬虫"`
}

// QuranCrawlerStatusRes 古兰经数据状态响应
type QuranCrawlerStatusRes struct {
	TotalSurat     int                 `json:"totalSurat" dc:"总章节数"`
	CompletedSurat int                 `json:"completedSurat" dc:"完整章节数"`
	TotalAyat      int                 `json:"totalAyat" dc:"总经文数"`
	CrawledAyat    int                 `json:"crawledAyat" dc:"已爬取经文数"`
	TotalTafsir    int                 `json:"totalTafsir" dc:"总注释数"`
	CrawledTafsir  int                 `json:"crawledTafsir" dc:"已爬取注释数"`
	SuratStatus    []*QuranSuratStatus `json:"suratStatus" dc:"章节状态详情"`
	CrawlProgress  *QuranCrawlProgress `json:"crawlProgress" dc:"爬取进度"`
}

// QuranSuratStatus 章节状态
type QuranSuratStatus struct {
	Nomor         int    `json:"nomor" dc:"章节编号"`
	NamaLatin     string `json:"namaLatin" dc:"章节名称"`
	JumlahAyat    int    `json:"jumlahAyat" dc:"经文总数"`
	CrawledAyat   int    `json:"crawledAyat" dc:"已爬取经文数"`
	CrawledTafsir int    `json:"crawledTafsir" dc:"已爬取注释数"`
	LastUpdate    string `json:"lastUpdate" dc:"最后更新时间"`
}

// QuranCrawlProgress 爬取进度
type QuranCrawlProgress struct {
	IsRunning    bool   `json:"isRunning" dc:"是否正在爬取"`
	CurrentSurat int    `json:"currentSurat" dc:"当前爬取章节"`
	CurrentStep  string `json:"currentStep" dc:"当前步骤: surat_list/ayat/tafsir"`
	StartTime    string `json:"startTime" dc:"开始时间"`
	ErrorCount   int    `json:"errorCount" dc:"错误次数"`
	LastError    string `json:"lastError" dc:"最后错误信息"`
}

// QuranCrawlerSuratListReq 获取章节列表请求
type QuranCrawlerSuratListReq struct {
	g.Meta `path:"/quran-crawler/surat/list" method:"get" summary:"获取古兰经章节列表" tags:"古兰经爬虫"`
	Page   int `json:"page" d:"1" dc:"页码"`
	Size   int `json:"size" d:"20" dc:"每页数量"`
}

// QuranCrawlerSuratListRes 章节列表响应
type QuranCrawlerSuratListRes struct {
	List  []*QuranSuratItem `json:"list" dc:"章节列表"`
	Total int               `json:"total" dc:"总数"`
	Page  int               `json:"page" dc:"当前页"`
	Size  int               `json:"size" dc:"每页数量"`
}

// QuranSuratItem 章节项目
type QuranSuratItem struct {
	Id          int    `json:"id" dc:"ID"`
	Nomor       int    `json:"nomor" dc:"章节编号"`
	Nama        string `json:"nama" dc:"阿拉伯语名称"`
	NamaLatin   string `json:"namaLatin" dc:"拉丁化名称"`
	JumlahAyat  int    `json:"jumlahAyat" dc:"经文数量"`
	TempatTurun string `json:"tempatTurun" dc:"降示地点"`
	Arti        string `json:"arti" dc:"含义"`
	Audio       string `json:"audio" dc:"音频URL"`
	Status      int    `json:"status" dc:"状态"`
}

// QuranCrawlerSuratDetailReq 获取章节详情请求
type QuranCrawlerSuratDetailReq struct {
	g.Meta `path:"/quran-crawler/surat/:nomor" method:"get" summary:"获取古兰经章节详情" tags:"古兰经爬虫"`
	Nomor  int `json:"nomor" v:"required|between:1,114" dc:"章节编号(1-114)"`
}

// QuranCrawlerSuratDetailRes 章节详情响应
type QuranCrawlerSuratDetailRes struct {
	Surat  *QuranSuratItem `json:"surat" dc:"章节信息"`
	Ayat   []*QuranAyat    `json:"ayat" dc:"经文列表"`
	Tafsir []*QuranTafsir  `json:"tafsir" dc:"注释列表"`
}

// QuranAyat 经文
type QuranAyat struct {
	Id      int    `json:"id" dc:"ID"`
	AyatId  int    `json:"ayatId" dc:"经文全局ID"`
	SurahId int    `json:"surahId" dc:"章节ID"`
	Nomor   int    `json:"nomor" dc:"经文编号"`
	Ar      string `json:"ar" dc:"阿拉伯语"`
	Tr      string `json:"tr" dc:"音译"`
	Idn     string `json:"idn" dc:"印尼语翻译"`
}

// QuranTafsir 注释
type QuranTafsir struct {
	Id        int    `json:"id" dc:"ID"`
	TafsirId  int    `json:"tafsirId" dc:"注释全局ID"`
	SurahId   int    `json:"surahId" dc:"章节ID"`
	AyatNomor int    `json:"ayatNomor" dc:"经文编号"`
	Tafsir    string `json:"tafsir" dc:"注释内容"`
}
