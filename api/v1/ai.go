package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type AiGenReq struct {
	g.Meta `path:"/ai/gen" tags:"ai生成管理" method:"post" summary:"ai生成管理"`
	Items  []AiGenItem `json:"items" dc:"生成内容"`
}

type AiGenItem struct {
	Keywords    []string `v:"required" json:"keywords" dc:"关键词列表"`
	Describe    string   `v:"required" json:"describe" dc:"描述"`
	SiteGroupId uint     `v:"required" json:"siteGroupId" dc:"站点分组id"`
	ColumnId    int      `json:"columnId" dc:"栏目id，随机传递0" d:"0"`
}

type AiGenRes struct {
	Num int `json:"num" dc:"成功生成数量"`
}
