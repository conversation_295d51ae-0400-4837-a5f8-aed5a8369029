package v1

import "github.com/gogf/gf/v2/frame/g"

type CollectCountryReq struct {
	g.<PERSON>a `path:"/collect/country" method:"post" tags:"体育数据" summary:"国家列表"`
}

type CollectCountryRes struct {
	Items []CollectCountry `json:"items" dc:"国家数据"`
}

type CollectCountry struct {
	CollectName
	Id         int    `json:"id" dc:"国家id"`
	CategoryId int    `json:"categoryId" dc:"分类id - 见分类列表"`
	Logo       string `json:"logo" dc:"国家logo"`
}
