package v1

import "github.com/gogf/gf/v2/frame/g"

// 采集数据的赛事数据

type CollectFootballCompetitionListReq struct {
	g.Meta `path:"/collect/football/competition/list" method:"post" tags:"体育数据" summary:"足球赛事列表"`
	ListReq
	Id         int    `json:"id" dc:"按赛事id过滤(数组中只会有一条)"`
	Name       string `json:"name" dc:"按名称过滤"`
	CategoryId int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	Type       int    `json:"type" dc:"按类型过滤(杯赛联赛)"`
	IsHot      int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectFootballCompetitionListRes struct {
	ListRes
	Items []FootballCompetition `json:"items" dc:"赛事数据"`
}

type CollectFootballCompetitionEditReq struct {
	g.<PERSON>a `path:"/collect/football/competition/edit" method:"post" tags:"体育数据" summary:"足球赛事编辑"`
	Id     int `json:"id" dc:"赛事id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo *string `json:"logo" dc:"logo url 相对路径"`
	Type *int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectFootballCompetitionEditRes struct {
	EmptyDataRes
}

type CollectBasketballCompetitionListReq struct {
	g.Meta `path:"/collect/basketball/competition/list" method:"post" tags:"体育数据" summary:"篮球赛事列表"`
	ListReq
	Id         int    `json:"id" dc:"按赛事id过滤(数组中只会有一条)"`
	Name       string `json:"name" dc:"按名称过滤"`
	CategoryId int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	Type       int    `json:"type" dc:"按类型过滤(杯赛联赛)"`
	IsHot      int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectSnookerCompetitionListReq struct {
	g.Meta `path:"/collect/snooker/competition/list" method:"post" tags:"体育数据" summary:"Snooker赛事列表"`
	ListReq
	Id         int    `json:"id" dc:"按赛事id过滤(数组中只会有一条)"`
	Name       string `json:"name" dc:"按名称过滤"`
	CategoryId int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	Type       int    `json:"type" dc:"按类型过滤(杯赛联赛)"`
	IsHot      int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}
type CollectBasketballCompetitionListRes struct {
	ListRes
	Items []BasketballCompetition `json:"items" dc:"赛事数据"`
}
type CollectSnookerCompetitionListRes struct {
	ListRes
	Items []SnookerCompetition `json:"items" dc:"赛事数据"`
}
type CollectSnookerCompetitionEditReq struct {
	g.Meta `path:"/collect/snooker/competition/edit" method:"post" tags:"体育数据" summary:"Snooker赛事编辑"`
	Id     int `json:"id" dc:"赛事id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo *string `json:"logo" dc:"logo url 相对路径"`
	Type *int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfoEdit
	ExtraInfoEdit
}
type CollectBasketballCompetitionEditReq struct {
	g.Meta `path:"/collect/basketball/competition/edit" method:"post" tags:"体育数据" summary:"篮球赛事编辑"`
	Id     int `json:"id" dc:"赛事id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo *string `json:"logo" dc:"logo url 相对路径"`
	Type *int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfoEdit
	ExtraInfoEdit
}
type CollectSnookerCompetitionEditRes struct {
	EmptyDataRes
}
type CollectBasketballCompetitionEditRes struct {
	EmptyDataRes
}
type SnookerCompetition struct {
	Id         int `json:"id" dc:"赛事id, int, 是纳米数据的唯一id"`
	CategoryId int `json:"categoryId" dc:"分类id"`
	CountryId  int `json:"countryId" dc:"国家id"`
	CollectName
	CollectShortName
	Logo     string `json:"logo" dc:"logo url 相对路径"`
	LogoFull string `json:"logoFull" dc:"logo url 绝对路径"`
	Type     int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfo
	ExtraInfo
}
type FootballCompetition struct {
	Id         int `json:"id" dc:"赛事id, int, 是纳米数据的唯一id"`
	CategoryId int `json:"categoryId" dc:"分类id"`
	CountryId  int `json:"countryId" dc:"国家id"`
	CollectName
	CollectShortName
	Logo     string `json:"logo" dc:"logo url 相对路径"`
	LogoFull string `json:"logoFull" dc:"logo url 绝对路径"`
	Type     int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfo
	ExtraInfo
}

type BasketballCompetition struct {
	Id         int `json:"id" dc:"赛事id, int, 是纳米数据的唯一id"`
	CategoryId int `json:"categoryId" dc:"分类id"`
	CountryId  int `json:"countryId" dc:"国家id"`
	CollectName
	CollectShortName
	Logo     string `json:"logo" dc:"logo url 相对路径"`
	LogoFull string `json:"logoFull" dc:"logo url 绝对路径"`
	Type     int    `json:"type" dc:"0-未知、1-联赛、2-杯赛、3-友谊赛"`
	SEOInfo
	ExtraInfo
}

type SEOInfo struct {
	SEOTitle   string `json:"seoTitle" dc:"SEO标题"`
	SEOKeyword string `json:"seoKeyword" dc:"SEO关键词"`
	SEODesc    string `json:"seoDesc" dc:"SEO描述"`
	SEOTags    string `json:"seoTags" dc:"SEO标签"`
}
type SEOInfoEdit struct {
	SEOTitle   *string `json:"seoTitle" dc:"SEO标题"`
	SEOKeyword *string `json:"seoKeyword" dc:"SEO关键词"`
	SEODesc    *string `json:"seoDesc" dc:"SEO描述"`
	SEOTags    *string `json:"seoTags" dc:"SEO标签"`
}

type ExtraInfo struct {
	OrderWeight int   `json:"orderWeight" dc:"排序权重"`
	ViewCount   int   `json:"viewCount" dc:"浏览量"`
	ReleaseTime int64 `json:"releaseTime" dc:"发布时间， 时间戳秒"`
	IsHot       int   `json:"isHot" dc:"是否热门(1是 2否)"`
	IsTop       int   `json:"isTop" dc:"是否置顶(1是 2否)"`
}
type ExtraInfoEdit struct {
	OrderWeight *int   `json:"orderWeight" dc:"排序权重"`
	ViewCount   *int   `json:"viewCount" dc:"浏览量"`
	ReleaseTime *int64 `json:"releaseTime" dc:"发布时间， 时间戳秒"`
	IsHot       *int   `json:"isHot" dc:"是否热门(1是 2否)"`
	IsTop       *int   `json:"isTop" dc:"是否置顶(1是 2否)"`
}

type CollectName struct {
	NameZh  string `json:"name_zh" dc:"中文名"`
	NameZht string `json:"name_zht" dc:"粤语名"`
	NameEn  string `json:"name_en" dc:"英文名"`
}
type CollectShortName struct {
	ShortNameZh  string `json:"short_name_zh" dc:"中文简称"`
	ShortNameZht string `json:"short_name_zht" dc:"粤语简称"`
	ShortNameEn  string `json:"short_name_En" dc:"英文简称"`
}
type CollectNameEdit struct {
	NameZh  *string `json:"name_zh" dc:"中文名"`
	NameZht *string `json:"name_zht" dc:"粤语名"`
	NameEn  *string `json:"name_en" dc:"英文名"`
}
type CollectShortNameEdit struct {
	ShortNameZh  *string `json:"short_name_zh" dc:"中文简称"`
	ShortNameZht *string `json:"short_name_zht" dc:"粤语简称"`
	ShortNameEn  *string `json:"short_name_En" dc:"英文简称"`
}
