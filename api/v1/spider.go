package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type SpiderListReq struct {
	g.<PERSON>a `path:"/spider/list" tags:"蜘蛛管理" method:"post" summary:"蜘蛛列表"`
	Name   *string `json:"name"       dc:"名称"`
	Status *int    `json:"status"     dc:"状态(1:启用 2:禁用)"`
	ListReq
}

type SpiderListRes struct {
	ListRes
	List []SpiderItem `json:"list" dc:"蜘蛛列表"`
}

type SpiderItem struct {
	Id                     uint   `json:"id"           dc:""`
	Name                   string `json:"name"                   dc:"名称"`
	Logo                   string `json:"logo"                   dc:"标志"`
	IdentificationCriteria int    `json:"identificationCriteria" dc:"识别依据(1:仅UA识别 2:仅IP识别 3:UA+IP双识别 4:UA或IP识别)"`
	BlockIp                string `json:"blockIp"                dc:"屏蔽ip"`
	Status                 int    `json:"status"                 dc:"状态(1:启用 2:禁用)"`
	Sort                   int    `json:"sort"                   dc:"排序"`
	CreateTime             int64  `json:"createTime"             dc:"创建时间"`
}

type SpiderAddReq struct {
	g.Meta                 `path:"/spider/add" tags:"蜘蛛管理" method:"post" summary:"添加蜘蛛"`
	Name                   string `v:"required" json:"name"                   dc:"名称"`
	Logo                   string `json:"logo"                   dc:"标志"`
	IdentificationCriteria int    `json:"identificationCriteria" dc:"识别依据(1:仅UA识别 2:仅IP识别 3:UA+IP双识别 4:UA或IP识别)"`
	BlockIp                string `json:"blockIp"                dc:"屏蔽ip" d:"{}"`
	Status                 int    `v:"required" json:"status"                 dc:"状态(1:启用 2:禁用)"`
	Sort                   int    `json:"sort"                   dc:"排序"`
	CreateTime             int64  `json:"createTime"             dc:"创建时间"`
}

type SpiderAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SpiderEditReq struct {
	g.Meta                 `path:"/spider/edit" tags:"蜘蛛管理" method:"post" summary:"编辑蜘蛛"`
	Id                     uint    `v:"required" json:"id"         dc:"id"`
	Name                   *string `json:"name"                   dc:"名称"`
	Logo                   *string `json:"logo"                   dc:"标志"`
	IdentificationCriteria *int    `json:"identificationCriteria" dc:"识别依据(1:仅UA识别 2:仅IP识别 3:UA+IP双识别 4:UA或IP识别)"`
	BlockIp                *string `json:"blockIp"                dc:"屏蔽ip"`
	Status                 *int    `json:"status"                 dc:"状态(1:启用 2:禁用)"`
	Sort                   *int    `json:"sort"                   dc:"排序"`
}

type SpiderDeleteReq struct {
	g.Meta `path:"/spider/delete" tags:"蜘蛛管理" method:"post" summary:"删除蜘蛛"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type SpiderOneReq struct {
	g.Meta `path:"/spider/one" method:"post" tags:"蜘蛛管理" summary:"蜘蛛->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SpiderOneRes struct {
	SpiderItem
}

type SpiderFirewallListReq struct {
	g.Meta        `path:"/spiderFirewall/list" tags:"蜘蛛管理" method:"post" summary:"蜘蛛防火墙列表"`
	BelongGroupId *uint `json:"belongGroupId"    dc:"所属分组"`
	Status        *int  `json:"status"     dc:"状态(1:启用 2:禁用)"`
	ListReq
}

type SpiderFirewallListRes struct {
	ListRes
	List []SpiderFirewallItem `json:"list" dc:"蜘蛛防火墙列表"`
}

type SpiderFirewallItem struct {
	Id              uint             `json:"id"           dc:""`
	BelongGroupId   uint             `json:"belongGroupId" dc:"所属分组id"`
	BelongGroupName string           `json:"belongGroupName" dc:"所属分组"`
	BlockSpider     string           `json:"blockSpider" dc:"屏蔽蜘蛛"`
	Config          []SpiderFirewall `json:"config"        dc:"蜘蛛id列表"`
	Status          int              `json:"status"                 dc:"状态(1:启用 2:禁用)"`
	CreateTime      int64            `json:"createTime"             dc:"创建时间"`
}

type SpiderFirewall struct {
	Id   int    `json:"id"                   dc:"id"`
	Name string `json:"name"                   dc:"名称"`
}

type SpiderFirewallAddReq struct {
	g.Meta        `path:"/spiderFirewall/add" tags:"蜘蛛管理" method:"post" summary:"添加蜘蛛防火墙"`
	BelongGroupId uint             `v:"required" json:"belongGroupId" dc:"所属分组id"`
	Config        []SpiderFirewall `json:"config"        dc:"蜘蛛id列表" d:"[]"`
	Status        int              `v:"required|in:1,2" json:"status"                 dc:"状态(1:启用 2:禁用)"`
}

type SpiderFirewallAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SpiderFirewallEditReq struct {
	g.Meta        `path:"/spiderFirewall/edit" tags:"蜘蛛管理" method:"post" summary:"编辑蜘蛛防火墙"`
	Id            uint              `v:"required" json:"id"         dc:"id"`
	BelongGroupId *uint             `json:"belongGroupId" dc:"所属分组id"`
	Config        *[]SpiderFirewall `json:"config"        dc:"蜘蛛id列表" `
	Status        *int              `json:"status"                 dc:"状态(1:启用 2:禁用)"`
}

type SpiderFirewallDeleteReq struct {
	g.Meta `path:"/spiderFirewall/delete" tags:"蜘蛛管理" method:"post" summary:"删除蜘蛛防火墙"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type SpiderLogListReq struct {
	g.Meta     `path:"/spiderLog/list" tags:"蜘蛛管理" method:"post" summary:"蜘蛛日志列表"`
	StartTime  *int64  `json:"startTime"    dc:"开始时间"`
	EndTime    *int64  `json:"endTime"    dc:"结束时间"`
	SpiderName *string `json:"spiderName"     dc:"蜘蛛名称-下拉列表(/spider/list)"`
	DomainName *string `json:"domainName"     dc:"域名名称-下拉列表(/domain/options)"`
	Ip         *string `json:"ip"             dc:"IP"`
	StatusCode *int    `json:"statusCode"    dc:"状态码"`
	ListReq
}

type SpiderLogListRes struct {
	ListRes
	List []SpiderLogItem `json:"list" dc:"蜘蛛日志列表"`
}

type SpiderLogItem struct {
	Id         uint   `json:"id"           dc:""`
	Name       string `json:"name"          dc:"名称"`
	Ip         string `json:"ip"            dc:"IP"`
	City       string `json:"city"          dc:"城市"`
	AccessUrl  string `json:"accessUrl"     dc:"访问地址"`
	AccessTime int64  `json:"accessTime"    dc:"访问时间"`
	StatusCode int    `json:"statusCode"    dc:"状态码"`
}

type SpiderLogPullBlackReq struct {
	g.Meta `path:"/spiderLog/pullBlack" tags:"蜘蛛管理" method:"post" summary:"蜘蛛一键拉黑"`
	Ids    []uint `json:"ids"           dc:""`
}

type SpiderLogCheckReq struct {
	g.Meta `path:"/spiderLog/check" tags:"蜘蛛管理" method:"post" summary:"校验是否真蜘蛛"`
	Id     uint `json:"id"           dc:""`
}

type SpiderLogBlackListReq struct {
	g.Meta `path:"/spiderLog/blackList" tags:"蜘蛛管理" method:"post" summary:"蜘蛛拉黑列表"`
}

type SpiderBlack struct {
	Id uint   `json:"id"           dc:""`
	Ip string `json:"ip"           dc:"IP"`
}

type SpiderLogBlackListRes struct {
	List []*SpiderBlack `json:"list" dc:"拉黑列表"`
}

type SpiderLogRemoveBlackReq struct {
	g.Meta `path:"/spiderLog/removeBlack" tags:"蜘蛛管理" method:"post" summary:"蜘蛛黑名单移除"`
	Ids    []uint `json:"ids"           dc:""`
}
