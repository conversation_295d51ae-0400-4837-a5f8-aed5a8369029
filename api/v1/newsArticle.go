package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type NewArticle struct {
	Author      string        `json:"author" dc:"创建人"`
	CategoryId  uint          `v:"required" json:"category_id" dc:"分类id"`
	PublishTime string        `json:"publish_time" dc:"发布时间，日期格式，如：2025-08-04 00:00:00"`
	CoverImgs   string        `v:"required" json:"cover_imgs"      dc:"封面图"`
	ContentArr  []ContentItem `v:"required" json:"content_arr" dc:"内容数组"`
	IsRecommend uint          `v:"required|between:0,1" json:"is_recommend"      dc:"1:推荐 0:不推荐"`
	IsTop       uint          `v:"required|between:0,1" json:"is_top"      dc:"是否加入头条 1:是 0:否"`
	IsDraft     uint          `v:"required|between:0,1" json:"is_draft"      dc:"是否保存草稿 1:草稿 0:否"`
	IsPublish   uint          `v:"required|between:0,1" json:"is_publish"      dc:"是否发布 1:是 0:否"`
}

type ContentItem struct {
	LanguageType int    `v:"required|between:1,3" json:"language_type" dc:"语言类型 0：中文 1：英文 2：印尼语"`
	ArticleName  string `json:"article_name" dc:"文章名称"`
	Content      string `v:"required" json:"content" dc:"内容"`
}

type NewsArticleAddReq struct {
	g.Meta `path:"/newsArticle/add" method:"post" tags:"文章管理" summary:"新增"`
	NewArticle
}

type NewsArticleAddRes struct{}

type NewsArticleEditReq struct {
	g.Meta `path:"/newsArticle/edit" method:"post" tags:"文章管理" summary:"编辑"`
	Id     uint `v:"required" json:"id" dc:"文章id"`
	NewArticle
}

type NewsArticleEditRes struct{}

type NewsArticleInfoReq struct {
	g.Meta `path:"/newsArticle/info" method:"get,post" tags:"文章管理" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"文章id"`
}

type NewsArticleInfoRes struct {
	NewArticle
}

type NewsArticleListReq struct {
	g.Meta `path:"/newsArticle/list" method:"get,post" tags:"文章管理" summary:"列表"`
	ListReq
	CategoryId         int    `json:"category_id" d:"-1" dc:"分类id 默认-1：全部"`
	IsPublish          int    `json:"is_publish" d:"-1" dc:"是否发布 1:是 0:否  默认-1：全部"`
	IsTop              int    `json:"is_top" d:"-1" dc:"是否加入头条 1:是 0:否 默认-1：全部"`
	IsRecommend        int    `json:"is_recommend" d:"-1" dc:"是否推荐 1：是 0：否 默认-1：全部"`
	IsDraft            int    `json:"is_draft" d:"0" dc:"是否草稿 1:是 0:否 默认0：否"`
	ArticleName        string `json:"article_name" dc:"文章名称"`
	CreateTimeBegin    string `json:"create_time_begin" dc:"创建开始时间 如：2025-08-04 00:00:00"`
	CreateTimeEnd      string `json:"create_time_end" dc:"创建结束时间 如：2025-08-04 00:00:00"`
	TopTimeBegin       string `json:"top_time_begin" dc:"加入头条开始时间 如：2025-08-04 00:00:00"`
	TopTimeEnd         string `json:"top_time_end" dc:"加入头条结束时间 如：2025-08-04 00:00:00"`
	RecommendTimeBegin string `json:"recommend_time_begin" dc:"加入推荐开始时间 如：2025-08-04 00:00:00"`
	RecommendTimeEnd   string `json:"recommend_time_end" dc:"加入推荐结束时间 如：2025-08-04 00:00:00"`
}

type NewsArticleListRes struct {
	List []NewsArticleListItem `json:"list"`
	ListRes
}

type NewsArticleListItem struct {
	Id                uint              `json:"id" dc:"文章id"`
	CategoryId        uint              `json:"category_id" dc:"分类id"`
	CategoryName      string            `json:"category_name" dc:"分类名称"`
	ArticleName       string            `json:"article_name" dc:"文章名词"`
	Author            string            `json:"author" dc:"创建人"`
	CreateTime        string            `json:"create_time" dc:"创建时间"`
	PublishTime       string            `json:"publish_time" dc:"发布时间"`
	ArticleStatus     int               `json:"article_status" dc:"文章状态 1:草稿 2:发布"`
	ArticleStatusText string            `json:"article_status_text" dc:"文章状态文案"`
	IsRecommend       int               `json:"is_recommend" dc:"是否推荐 1：是 0：否"`
	IsRecommendText   string            `json:"is_recommend_text" dc:"是否推荐文案"`
	IsTop             int               `json:"is_top" dc:"是否加入头条 1:是 0:否"`
	IsTopText         string            `json:"is_top_text" dc:"是否加入头条文案"`
	IsPublish         int               `json:"is_publish" dc:"是否发布 1:是 0:否"`
	IsPublishText     string            `json:"is_publish_text" dc:"是否发布文案"`
	LanguageArr       []LanguageArrItem `json:"language_arr" dc:"语言列表"`
	CollectNum        int               `json:"collect_num" dc:"收藏数"`
	ShareNum          int               `json:"share_num" dc:"分享数"`
	ViewNum           int               `json:"view_num" dc:"浏览数"`
	SerialNum         int               `json:"serial_num" dc:"序号"`
}

type NewsArticleDeleteReq struct {
	g.Meta `path:"/newsArticle/delete" method:"post" tags:"文章管理" summary:"删除（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetRecommendReq struct {
	g.Meta `path:"/newsArticle/setRecommend" method:"post" tags:"文章管理" summary:"设置推荐（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetNotRecommendReq struct {
	g.Meta `path:"/newsArticle/setNotRecommend" method:"post" tags:"文章管理" summary:"设置不推荐（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetTopReq struct {
	g.Meta `path:"/newsArticle/setTop" method:"post" tags:"文章管理" summary:"加入头条（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetNotTopReq struct {
	g.Meta `path:"/newsArticle/setNotTop" method:"post" tags:"文章管理" summary:"取消加入头条（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetOfflineReq struct {
	g.Meta `path:"/newsArticle/setOffline" method:"post" tags:"文章管理" summary:"下线（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleSetOnlineReq struct {
	g.Meta `path:"/newsArticle/setOnline" method:"post" tags:"文章管理" summary:"上线（支持批量）"`
	Ids    []uint `v:"required" json:"ids" dc:"文章id数组"`
}

type NewsArticleDraftReq struct {
	g.Meta `path:"/newsArticle/draft" method:"get" tags:"文章管理" summary:"草稿箱"`
	ListReq
	ArticleName     string `json:"article_name" dc:"文章名称"`
	CreateTimeBegin string `json:"create_time_begin" dc:"创建开始时间 如：2025-08-04 00:00:00"`
	CreateTimeEnd   string `json:"create_time_end" dc:"创建结束时间 如：2025-08-04 00:00:00"`
	UpdateTimeBegin string `json:"update_time_begin" dc:"更新开始时间 如：2025-08-04 00:00:00"`
	UpdateTimeEnd   string `json:"update_time_end" dc:"更新结束时间 如：2025-08-04 00:00:00"`
}

type NewsArticleDraftItem struct {
	Id                uint              `json:"id" dc:"文章id"`
	ArticleName       string            `json:"article_name" dc:"文章名词"`
	CreateTime        string            `json:"create_time" dc:"创建时间"`
	UpdateTime        string            `json:"update_time" dc:"修改时间"`
	ArticleStatus     int               `json:"article_status" dc:"文章状态 1:草稿 2:发布"`
	ArticleStatusText string            `json:"article_status_text" dc:"文章状态文案"`
	LanguageArr       []LanguageArrItem `json:"language_arr" dc:"语言列表"`
}

type NewsArticleDraftRes struct {
	List []NewsArticleDraftItem `json:"list"`
	ListRes
}
