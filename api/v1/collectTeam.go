package v1

import "github.com/gogf/gf/v2/frame/g"

// 体育数据 球队信息

type CollectFootballTeamListReq struct {
	g.<PERSON>a `path:"/collect/football/team/list" method:"post" tags:"体育数据" summary:"足球球队列表"`
	ListReq
	Id            int    `json:"id" dc:"按球队id过滤(数组中只会有一条)"`
	Name          string `json:"name" dc:"按名称过滤"`
	CategoryId    int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	CompetitionId int    `json:"competitionId" dc:"按赛事id进行过滤"`
	IsHot         int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectFootballTeamListRes struct {
	ListRes
	Items []FootballTeam `json:"items" dc:"球员数据"`
}

type CollectFootballTeamEditReq struct {
	g.<PERSON>a `path:"/collect/football/team/edit" method:"post" tags:"体育数据" summary:"足球球队编辑"`
	Id     int `json:"id" dc:"球员id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo                *string `json:"logo" dc:"logo url 相对路径"`
	National            *int    `json:"national"            description:"是否国家队，1-是、0-否"`
	CountryLogo         *string `json:"countryLogo"         description:"国家队logo（为国家队时存在）"`
	FoundationTime      *int    `json:"foundationTime"      description:"成立时间, 时间戳"`
	Website             *string `json:"website"             description:"球队官网"`
	MarketValue         *int    `json:"marketValue"         description:"市值"`
	MarketValueCurrency *string `json:"marketValueCurrency" description:"市值单位"`
	TotalPlayers        *int    `json:"totalPlayers"        description:"总球员数，-1表示没有该字段数据"`
	ForeignPlayers      *int    `json:"foreignPlayers"      description:"非本土球员数，-1表示没有该字段数据"`
	NationalPlayers     *int    `json:"nationalPlayers"     description:"国家队球员数，-1表示没有该字段数据"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectFootballTeamEditRes struct {
	EmptyDataRes
}

type CollectBasketballTeamListReq struct {
	g.Meta `path:"/collect/basketball/team/list" method:"post" tags:"体育数据" summary:"篮球球队列表"`
	ListReq
	Id            int    `json:"id" dc:"按球员id过滤(数组中只会有一条)"`
	Name          string `json:"name" dc:"按名称过滤"`
	CategoryId    int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	Type          int    `json:"type" dc:"按类型过滤(杯赛联赛)"`
	CompetitionId int    `json:"competitionId" dc:"按赛事id进行过滤"`
	IsHot         int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectBasketballTeamListRes struct {
	ListRes
	Items []BasketballTeam `json:"items" dc:"球员数据"`
}

type CollectBasketballTeamEditReq struct {
	g.Meta `path:"/collect/basketball/team/edit" method:"post" tags:"体育数据" summary:"篮球球队编辑"`
	Id     int `json:"id" dc:"球员id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo     *string `json:"logo" dc:"logo url 相对路径"`
	National *int    `json:"national"      description:"是否国家队，1-是、0-否"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectBasketballTeamEditRes struct {
	EmptyDataRes
}

type FootballTeam struct {
	Id            int `json:"id"                  description:""`
	CompetitionId int `json:"competitionId"       description:"赛事id（球队所属联赛，杯赛不关联）"`
	CountryId     int `json:"countryId"           description:"国家id"`
	//CoachId             int    `json:"coachId"             description:"教练id"`
	CollectName
	CollectShortName
	Logo                string `json:"logo"                description:"球队logo"`
	LogoFull            string `json:"logoFull"                description:"球队logo"`
	National            int    `json:"national"            description:"是否国家队，1-是、0-否"`
	CountryLogo         string `json:"countryLogo"         description:"国家队logo（为国家队时存在）"`
	CountryLogoFull     string `json:"countryLogoFull"         description:"国家队logo（为国家队时存在）"`
	FoundationTime      int    `json:"foundationTime"      description:"成立时间"`
	Website             string `json:"website"             description:"球队官网"`
	VenueId             int    `json:"venueId"             description:"场馆id"`
	MarketValue         int    `json:"marketValue"         description:"市值"`
	MarketValueCurrency string `json:"marketValueCurrency" description:"市值单位"`
	TotalPlayers        int    `json:"totalPlayers"        description:"总球员数，-1表示没有该字段数据"`
	ForeignPlayers      int    `json:"foreignPlayers"      description:"非本土球员数，-1表示没有该字段数据"`
	NationalPlayers     int    `json:"nationalPlayers"     description:"国家队球员数，-1表示没有该字段数据"`
	SEOInfo
	ExtraInfo
}

type BasketballTeam struct {
	Id            int `json:"id"            description:""`
	CompetitionId int `json:"competitionId" description:"赛事id（球队所属联赛，杯赛不关联）"`
	CountryId     int `json:"countryId"     description:"国家id"`
	//CoachId       int    `json:"coachId"       description:"教练id"`
	CollectName
	CollectShortName
	Logo     string `json:"logo"          description:"球队logo"`
	LogoFull string `json:"logoFull"                description:"球队logo"`
	National int    `json:"national"      description:"是否国家队，1-是、0-否"`
	VenueId  int    `json:"venueId"       description:"场馆id"`
	SEOInfo
	ExtraInfo
}

type CollectSnookerTeamListReq struct {
	g.Meta `path:"/collect/snooker/team/list" method:"post" tags:"体育数据" summary:"Snooker球队列表"`
	ListReq
	Id            int    `json:"id" dc:"按球员id过滤(数组中只会有一条)"`
	Name          string `json:"name" dc:"按名称过滤"`
	CategoryId    int    `json:"categoryId" dc:"按分类id进行过滤（即地区）"`
	Type          int    `json:"type" dc:"按类型过滤(杯赛联赛)"`
	CompetitionId int    `json:"competitionId" dc:"按赛事id进行过滤"`
	IsHot         int    `json:"isHot" dc:"是否热门 1:是 2:否"`
}

type CollectSnookerTeamListRes struct {
	ListRes
	Items []SnookerTeam `json:"items" dc:"球员数据"`
}

type CollectSnookerTeamEditReq struct {
	g.Meta `path:"/collect/snooker/team/edit" method:"post" tags:"体育数据" summary:"Snooker球队编辑"`
	Id     int `json:"id" dc:"球员id"`
	CollectNameEdit
	CollectShortNameEdit
	Logo     *string `json:"logo" dc:"logo url 相对路径"`
	National *int    `json:"national"      description:"是否国家队，1-是、0-否"`
	SEOInfoEdit
	ExtraInfoEdit
}

type CollectSnookerTeamEditRes struct {
	EmptyDataRes
}

type SnookerTeam struct {
	Id            int `json:"id"                  description:""`
	CompetitionId int `json:"competitionId"       description:"赛事id（球队所属联赛，杯赛不关联）"`
	CountryId     int `json:"countryId"           description:"国家id"`
	//CoachId             int    `json:"coachId"             description:"教练id"`
	CollectName
	CollectShortName
	Logo                string `json:"logo"                description:"球队logo"`
	LogoFull            string `json:"logoFull"                description:"球队logo"`
	National            int    `json:"national"            description:"是否国家队，1-是、0-否"`
	CountryLogo         string `json:"countryLogo"         description:"国家队logo（为国家队时存在）"`
	CountryLogoFull     string `json:"countryLogoFull"         description:"国家队logo（为国家队时存在）"`
	FoundationTime      int    `json:"foundationTime"      description:"成立时间"`
	Website             string `json:"website"             description:"球队官网"`
	VenueId             int    `json:"venueId"             description:"场馆id"`
	MarketValue         int    `json:"marketValue"         description:"市值"`
	MarketValueCurrency string `json:"marketValueCurrency" description:"市值单位"`
	TotalPlayers        int    `json:"totalPlayers"        description:"总球员数，-1表示没有该字段数据"`
	ForeignPlayers      int    `json:"foreignPlayers"      description:"非本土球员数，-1表示没有该字段数据"`
	NationalPlayers     int    `json:"nationalPlayers"     description:"国家队球员数，-1表示没有该字段数据"`
	SEOInfo
	ExtraInfo
}
