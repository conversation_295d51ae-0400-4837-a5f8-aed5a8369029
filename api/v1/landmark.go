package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ==================== 地标类型相关接口 ====================

// LandmarkTypeListReq 地标类型列表请求
type LandmarkTypeListReq struct {
	g.Meta `path:"/landmarkType/list" method:"post" tags:"地标管理" summary:"地标类型列表"`
	ListReq
}

type LandmarkTypeListItem struct {
	Id          uint64            `json:"id" dc:"类型ID"`
	IconUrl     string            `json:"icon_url" dc:"图标URL"`
	TypeName    string            `json:"type_name" dc:"类型名称"`
	LanguageArr []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
	CreateTime  uint64            `json:"create_time" dc:"创建时间"`
}

type LandmarkTypeListRes struct {
	List []LandmarkTypeListItem `json:"list" dc:"类型列表"`
	ListRes
}

// LandmarkTypeCreateReq 新增地标类型请求
type LandmarkTypeCreateReq struct {
	g.Meta  `path:"/landmarkType/add" method:"post" tags:"地标管理" summary:"新增地标类型"`
	IconUrl string                     `v:"required" json:"icon_url" dc:"图标URL"`
	Content []LandmarkTypeCreateItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type LandmarkTypeCreateItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type LandmarkTypeCreateRes struct {
	Id int64 `json:"id" dc:"类型ID"`
}

// LandmarkTypeEditReq 编辑地标类型请求
type LandmarkTypeEditReq struct {
	g.Meta  `path:"/landmarkType/edit" method:"post" tags:"地标管理" summary:"编辑地标类型"`
	Id      int64                    `v:"required" json:"id" dc:"类型ID"`
	IconUrl string                   `v:"required" json:"icon_url" dc:"图标URL"`
	Content []LandmarkTypeEditItem `v:"required|array|required" json:"content" dc:"类型内容"`
}

type LandmarkTypeEditItem struct {
	LanguageType int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	TypeName     string `v:"required|length:1,30" json:"type_name" dc:"类型名称"`
}

type LandmarkTypeEditRes struct{}

// LandmarkTypeOneReq 获取单个地标类型请求
type LandmarkTypeOneReq struct {
	g.Meta `path:"/landmarkType/one" method:"post" tags:"地标管理" summary:"获取单个地标类型"`
	Id     int64 `v:"required" json:"id" dc:"类型ID"`
}

type LandmarkTypeOneRes struct {
	LandmarkTypeListItem
}

// LandmarkTypeDeleteReq 删除地标类型请求
type LandmarkTypeDeleteReq struct {
	g.Meta `path:"/landmarkType/delete" method:"post" tags:"地标管理" summary:"删除地标类型"`
	Id     int64 `v:"required" json:"id" dc:"类型ID"`
}

type LandmarkTypeDeleteRes struct{}

// LandmarkTypeOptionsReq 地标类型选项请求
type LandmarkTypeOptionsReq struct {
	g.Meta `path:"/landmarkType/options" method:"post" tags:"地标管理" summary:"地标类型选项"`
}

type LandmarkTypeOption struct {
	Value int64  `json:"value" dc:"类型ID"`
	Label string `json:"label" dc:"类型名称"`
}

type LandmarkTypeOptionsRes struct {
	Options []LandmarkTypeOption `json:"options" dc:"选项列表"`
}

// ==================== 地标相关接口 ====================

// LandmarkListReq 地标列表请求
type LandmarkListReq struct {
	g.Meta `path:"/landmark/list" method:"post" tags:"地标管理" summary:"地标列表"`
	TypeId *int64 `json:"type_id" dc:"地标类型ID"`
	ListReq
}

type LandmarkListItem struct {
	Id               uint64            `json:"id" dc:"地标ID"`
	TypeId           uint64            `json:"type_id" dc:"地标类型ID"`
	TypeName         string            `json:"type_name" dc:"地标类型名称"`
	LandmarkName     string            `json:"landmark_name" dc:"地标名称"`
	ShortDescription string            `json:"short_description" dc:"地标简介"`
	Address          string            `json:"address" dc:"地址"`
	Latitude         string            `json:"latitude" dc:"纬度"`
	Longitude        string            `json:"longitude" dc:"经度"`
	ImageUrl         string            `json:"image_url" dc:"图片URL"`
	SortOrder        uint              `json:"sort_order" dc:"排序值"`
	LanguageArr      []LanguageArrItem `json:"language_arr" dc:"支持的语言列表"`
	CreateTime       uint64            `json:"create_time" dc:"创建时间"`
}

type LandmarkListRes struct {
	List []LandmarkListItem `json:"list" dc:"地标列表"`
	ListRes
}

// LandmarkCreateReq 新增地标请求
type LandmarkCreateReq struct {
	g.Meta    `path:"/landmark/add" method:"post" tags:"地标管理" summary:"新增地标"`
	TypeId    int64                  `v:"required" json:"type_id" dc:"地标类型ID"`
	Latitude  string                 `v:"required" json:"latitude" dc:"纬度"`
	Longitude string                 `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string                 `v:"required" json:"image_url" dc:"图片URL"`
	SortOrder uint                   `v:"min:0|max:9999" json:"sort_order" dc:"排序值"`
	Content   []LandmarkCreateItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type LandmarkCreateItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type LandmarkCreateRes struct {
	Id int64 `json:"id" dc:"地标ID"`
}

// LandmarkEditReq 编辑地标请求
type LandmarkEditReq struct {
	g.Meta    `path:"/landmark/edit" method:"post" tags:"地标管理" summary:"编辑地标"`
	Id        int64                `v:"required" json:"id" dc:"地标ID"`
	TypeId    int64                `v:"required" json:"type_id" dc:"地标类型ID"`
	Latitude  string               `v:"required" json:"latitude" dc:"纬度"`
	Longitude string               `v:"required" json:"longitude" dc:"经度"`
	ImageUrl  string               `v:"required" json:"image_url" dc:"图片URL"`
	SortOrder uint                 `v:"min:0|max:9999" json:"sort_order" dc:"排序值"`
	Content   []LandmarkEditItem `v:"required|array|required" json:"content" dc:"地标内容"`
}

type LandmarkEditItem struct {
	LanguageType     int    `v:"required|in:0,1,2" json:"language_type" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"`
	LandmarkName     string `v:"required|length:1,100" json:"landmark_name" dc:"地标名称"`
	ShortDescription string `v:"length:0,100" json:"short_description" dc:"地标简介"`
	Address          string `v:"required|length:1,100" json:"address" dc:"地址"`
	InformationText  string `v:"required" json:"information_text" dc:"详细信息（富文本）"`
}

type LandmarkEditRes struct{}

// LandmarkOneReq 获取单个地标请求
type LandmarkOneReq struct {
	g.Meta `path:"/landmark/one" method:"post" tags:"地标管理" summary:"获取单个地标"`
	Id     int64 `v:"required" json:"id" dc:"地标ID"`
}

type LandmarkOneRes struct {
	LandmarkListItem
}

// LandmarkDeleteReq 删除地标请求
type LandmarkDeleteReq struct {
	g.Meta `path:"/landmark/delete" method:"post" tags:"地标管理" summary:"删除地标"`
	Id     int64 `v:"required" json:"id" dc:"地标ID"`
}

type LandmarkDeleteRes struct{}
