package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type ColumnsListReq struct {
	g.<PERSON>a   `path:"/column/list" tags:"栏目管理" method:"post" summary:"栏目列表"`
	Name     *string `json:"name" dc:"名称"`
	Status   *int    `json:"status" dc:"状态(1:显示;2:隐藏;)"`
	Belong   int     `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	Language string  `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
}
type ColumnsListRes struct {
	List []*ColumnsItem `json:"list" dc:"栏目列表"`
}

type ColumnsItem struct {
	Id              int            `json:"id"`
	Pid             int            `json:"pid" dc:"父级分类ID" d:"0"`
	Name            string         `json:"name" dc:"分类名称"`
	Alias           string         `json:"alias" dc:"别名"`
	Thumb           string         `json:"thumb" dc:"栏目缩略图"`
	Sort            int            `json:"sort" dc:"排序"`
	Level           int            `json:"level" dc:"排序" d:"分类层次"`
	Status          int            `json:"status" dc:"状态(1:显示 2:隐藏)，如果是显示，前端展示时，应该是勾选状态"`
	Desc            string         `json:"desc" dc:"描述"`
	LinkProperty    int            `dc:"链接属性(1:新窗口打开 2:nofollow)"`
	SeoTitle        string         `json:"seoTitle" dc:"seo标题"`
	SeoDesc         string         `json:"seoDesc" dc:"seo描述"`
	SeoKeyword      string         `json:"seoKeyword" dc:"seo关键词"`
	Banner          string         `json:"banner" dc:"banner图"`
	Selected        uint           `json:"selected" dc:"当前选中的栏目(1:被选中)"`
	Children        []*ColumnsItem `json:"children" dc:"子类"`
	ModuleId        string         `json:"moduleId" dc:"模块id"`
	ModuleName      string         `json:"moduleName" dc:"模块名称"`
	TdkTmplCategory int            `json:"tdkTmplCategory" dc:"栏目所属的tdk模版类型(1:赛程栏目 2:录像栏目 3:新闻栏目)"`
}

type ColumnsAddReq struct {
	g.Meta          `path:"/column/add" tags:"栏目管理" method:"post" summary:"添加栏目"`
	Pid             int    `v:"required#请选择父级分类ID" json:"pid" dc:"父级分类ID" d:"0"`
	Name            string `v:"required#分类名称" json:"name" dc:"分类名称"`
	Alias           string `v:"required#分类名称" json:"alias" dc:"别名"`
	Thumb           string `json:"thumb" dc:"栏目缩略图"`
	Sort            int    `json:"sort" dc:"排序"`
	Status          int    `json:"status" dc:"状态(1:显示;2:隐藏;)"`
	Desc            string `json:"desc" dc:"描述"`
	LinkProperty    int    `json:"linkProperty" dc:"链接属性(1:新窗口打开 2:nofollow)"`
	SeoTitle        string `json:"seoTitle" dc:"seo标题"`
	SeoDesc         string `json:"seoDesc" dc:"seo描述"`
	SeoKeyword      string `json:"seoKeyword" dc:"seo关键词"`
	Banner          string `json:"banner" dc:"banner图"`
	Belong          int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId        *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	ModuleId        string `json:"moduleId" dc:"数据源id"`
	TdkTmplCategory int    `json:"tdkTmplCategory" dc:"栏目所属的tdk模版类型(1:赛程栏目 2:录像栏目 3:新闻栏目)"`
	Language        string `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
}

type ColumnsAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type ColumnsEditReq struct {
	g.Meta          `path:"/column/edit" tags:"栏目管理" method:"post" summary:"编辑栏目"`
	Id              uint    `v:"required" json:"id"         dc:"id"`
	Pid             *int    `json:"pid" dc:"父级分类ID"`
	Name            *string `json:"name" dc:"分类名称"`
	Alias           *string `json:"alias" dc:"别名"`
	Thumb           *string `json:"thumb" dc:"栏目缩略图"`
	Sort            *int    `json:"sort" dc:"排序"`
	Status          *int    `json:"status" dc:"状态(1:显示 2:隐藏)"`
	Desc            *string `json:"desc" dc:"描述"`
	LinkProperty    *int    `json:"linkProperty" dc:"链接属性(1:新窗口打开 2:nofollow)"`
	SeoTitle        *string `json:"seoTitle" dc:"seo标题"`
	SeoDesc         *string `json:"seoDesc" dc:"seo描述"`
	SeoKeyword      *string `json:"seoKeyword" dc:"seo关键词"`
	Banner          *string `json:"banner" dc:"banner图"`
	Belong          int     `v:"required" json:"belong" dc:"所属功能(0:栏目 1:分组栏目 2:站点栏目)"`
	ModuleId        *string `json:"moduleId" dc:"数据源id"`
	TdkTmplCategory *int    `json:"tdkTmplCategory" dc:"栏目所属的tdk模版类型(1:赛程栏目 2:录像栏目 3:新闻栏目)"`
}

type ColumnsDeleteReq struct {
	g.Meta `path:"/column/delete" tags:"栏目管理" method:"post" summary:"删除栏目"`
	Id     uint `v:"required" json:"id"         dc:"id"`
	Belong int  `v:"required" json:"belong" dc:"所属功能(0:栏目 1:分组栏目 2:站点栏目)"`
}

type ColumnsOptionsReq struct {
	g.Meta   `path:"/column/options" method:"post" tags:"栏目管理" summary:"栏目树->选项(供选择器)"`
	Id       int    `json:"id" dc:"当前栏目id" d:"0"`
	Belong   int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:分组栏目 2:站点栏目)"`
	BelongId *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	Language string `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
	IsNews   int    `json:"isNews" dc:"是否新闻栏目(1:是 2:否)"`
}

type ColumnsOptionsRes struct {
	Options []*ColumnsItem `json:"options" dc:"选择器选项数栏目"`
}

type ColumnsGroupSiteAddReq struct {
	g.Meta   `path:"/column/groupSiteAdd" method:"post" tags:"栏目管理" summary:"添加分组栏目和站点栏目"`
	Ids      []uint `v:"required" json:"ids" dc:"栏目id，不推送的id，代表隐藏"`
	Belong   int    `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint  `v:"required" json:"belongId" dc:"所属功能id"`
	Language string `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
}

type ColumnsChooseRes struct {
	List []*ColumnsItem `json:"list" dc:"栏目列表"`
}

type ColumnsGroupSiteEditReq struct {
	g.Meta   `path:"/column/groupSiteEdit" tags:"栏目管理" method:"post" summary:"编辑分组栏目和站点栏目"`
	Belong   int    `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint  `v:"required" json:"belongId" dc:"所属功能id"`
	Language string `v:"required" json:"language" dc:"语言(cn:中文 en:英文 id:印尼)"`
}
type ColumnsGroupSiteEditRes struct {
	List []*ColumnsItem `json:"list" dc:"栏目列表"`
	Self int            `json:"self" dc:"当前栏目(1:是 2:否)，如果是，则在弹出编辑栏目框中写明-勾选可显示隐藏栏目，点击确定后提示编辑成；如果否，则写明-勾选可从栏目库选择栏目导入，点击确定后提示添加成功"`
}

type ColumnsAddBatchReq struct {
	g.Meta   `path:"/column/addBatch" tags:"栏目管理" method:"post" summary:"批量添加栏目"`
	Pid      uint  `v:"required#请选择父级ID" json:"pid" dc:"父级ID" d:"0"`
	Belong   int   `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId *uint `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
}

type ColumnsBulkShowReq struct {
	g.Meta   `path:"/column/bulkShow" tags:"栏目管理" method:"post" summary:"批量修改显示隐藏"`
	Ids      []uint `v:"required" json:"ids"        dc:"id"`
	Status   uint   `v:"required" json:"groupId"    dc:"状态(1:显示 2:隐藏)"`
	Belong   int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
}
