package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type SinglePageListReq struct {
	g.<PERSON>a     `path:"/singlePage/list" tags:"单页管理" method:"post" summary:"单页列表"`
	Name       *string `json:"name" dc:"名称"`
	GroupId    *uint   `json:"groupId"    dc:"所属分组"`
	SiteId     *uint   `json:"siteId"    dc:"所属站点"`
	Status     *int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
	DomainName *string `json:"domainName" dc:"所属域名"`
	ListReq
}

type SinglePageListRes struct {
	ListRes
	List []SinglePageItem `json:"list" dc:"单页列表"`
}

type SinglePageItem struct {
	Id              uint   `json:"id"           dc:""`
	BelongGroupId   uint   `json:"belongGroupId" dc:"所属分组id"`
	BelongSiteId    uint   `json:"belongSiteId"  dc:"所属站点id"`
	BelongGroupName string `json:"belongGroupName" dc:"所属分组名称"`
	BelongSiteName  string `json:"belongSiteName" dc:"所属站点名称"`
	Name            string `json:"name"          dc:"页面名称"`
	Url             string `json:"url"           dc:"url"`
	Desc            string `json:"desc"          dc:"简介"`
	Status          int    `json:"status"        dc:"状态(1:启用 2:禁用)"`
	Sort            int    `json:"sort"          dc:"排序"`
	SeoTitle        string `json:"seoTitle"      dc:"seo标题"`
	SeoKeyword      string `json:"seoKeyword"    dc:"seo关键词"`
	SeoDesc         string `json:"seoDesc"       dc:"seo描述"`
	Thumb           string `json:"thumb"         dc:"缩略图"`
	Banner          string `json:"banner"        dc:"banner图"`
	Type            int    `json:"type"          dc:"类型(1:关于我们 2:联系我们)"`
	CreateTime      int64  `json:"createTime"    dc:"创建时间"`
	Creater         uint   `json:"creater"      dc:"创建者"`
}

type SinglePageAddReq struct {
	g.Meta        `path:"/singlePage/add" tags:"单页管理" method:"post" summary:"添加单页"`
	BelongGroupId uint   `v:"required" json:"belongGroupId" dc:"所属分组id"`
	BelongSiteId  uint   `v:"required" json:"belongSiteId"  dc:"所属站点id"`
	Name          string `v:"required" json:"name"          dc:"页面名称"`
	Url           string `json:"url"           dc:"url"`
	Desc          string `json:"desc"          dc:"简介"`
	Status        int    `json:"status"        dc:"状态(1:启用 2:禁用)" d:"1"`
	Sort          int    `json:"sort"          dc:"排序"`
	SeoTitle      string `json:"seoTitle"      dc:"seo标题"`
	SeoKeyword    string `json:"seoKeyword"    dc:"seo关键词"`
	SeoDesc       string `json:"seoDesc"       dc:"seo描述"`
	Thumb         string `json:"thumb"         dc:"缩略图"`
	Banner        string `json:"banner"        dc:"banner图"`
	Type          int    `json:"type"          dc:"类型(1:关于我们 2:联系我们)"`
}

type SinglePageAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SinglePageEditReq struct {
	g.Meta        `path:"/singlePage/edit" tags:"单页管理" method:"post" summary:"编辑单页"`
	Id            uint    `v:"required" json:"id"         dc:"id"`
	BelongGroupId *uint   `json:"belongGroupId" dc:"所属分组id"`
	BelongSiteId  *uint   `json:"belongSiteId"  dc:"所属站点id"`
	Name          *string `json:"name"          dc:"页面名称"`
	Url           *string `json:"url"           dc:"url"`
	Desc          *string `json:"desc"          dc:"简介"`
	Status        *int    `json:"status"        dc:"状态(1:启用 2:禁用)"`
	Sort          *int    `json:"sort"          dc:"排序"`
	SeoTitle      *string `json:"seoTitle"      dc:"seo标题"`
	SeoKeyword    *string `json:"seoKeyword"    dc:"seo关键词"`
	SeoDesc       *string `json:"seoDesc"       dc:"seo描述"`
	Thumb         *string `json:"thumb"         dc:"缩略图"`
	Banner        *string `json:"banner"        dc:"banner图"`
	Type          *int    `json:"type"          dc:"类型(1:关于我们 2:联系我们)"`
}

type SinglePageDeleteReq struct {
	g.Meta `path:"/singlePage/delete" tags:"单页管理" method:"post" summary:"删除单页"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type SinglePageOneReq struct {
	g.Meta `path:"/singlePage/one" method:"post" tags:"单页管理" summary:"单页->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SinglePageOneRes struct {
	SinglePageItem
}
