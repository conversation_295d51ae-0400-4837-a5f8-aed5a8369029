package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type AccountAddReq struct {
	g.Meta   `path:"/accountMgr/add" method:"post" tags:"账户管理" summary:"新增"`
	Account  string `v:"required|passport|length:6,18#admin.account.required|gf.gvalid.rule.passport|admin.account.length" json:"account" dc:"账户"`
	Password string `v:"required|password3" json:"password" dc:"密码"`
	RoleId   uint   `v:"required" json:"roleId" dc:"角色id"`
	NickName string `v:"required" json:"nickName"      dc:"昵称"`
	//Contact       string `json:"contact"       dc:"联系方式"`
	Remark string `json:"remark"        dc:"备注"`
	//AuditPassword string `v:"required|length:6,6"  json:"auditPassword" dc:"私人密码"`
	IsAffect int `v:"required|between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
}

type AccountDeleteReq struct {
	g.Meta `path:"/accountMgr/delete" method:"post" tags:"账户管理" summary:"删除"`
	Id     uint `v:"required" json:"id" dc:"管理员id"`
}

type AccountEditReq struct {
	g.Meta   `path:"/accountMgr/edit" method:"post" tags:"账户管理" summary:"编辑"`
	Id       uint    `v:"required" json:"id"`
	Account  string  `v:"required|passport|length:6,18#admin.account.required|gf.gvalid.rule.passport|admin.account.length" json:"account" dc:"账户"`
	Password *string `v:"password3" json:"password" dc:"密码"`
	RoleId   uint    `json:"roleId" dc:"角色id"`
	NickName string  `json:"nickName"      dc:"昵称"`
	//Contact       string  `json:"contact"       dc:"联系方式"`
	Remark string `json:"remark"        dc:"备注"`
	//AuditPassword *string `v:"length:6,6"  json:"auditPassword" dc:"私人密码"`
	IsAffect            int `v:"between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
	IsRequireGoogleAuth int `json:"isRequireGoogleAuth" description:"谷歌验证码登录开关 1:需要 2:不用"`
}

type AccountDetailReq struct {
	g.Meta `path:"/accountMgr/detail" method:"post" tags:"账户管理" summary:"详情"`
	Id     uint `v:" " json:"id"`
}
type AccountDetailRes struct {
	AccountVo
}

type AccountSetStatusReq struct {
	g.Meta   `path:"/accountMgr/setStatus" method:"post" tags:"账户管理" summary:"启用设置"`
	Id       uint `v:"required" json:"id"`
	IsAffect int  `v:"required|between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
	//AuditPassword string `v:"required|length:6,6"  json:"auditPassword" dc:"私人密码"`
}

type AccountListReq struct {
	g.Meta `path:"/accountMgr/list" method:"post" tags:"账户管理" summary:"列表"`
	ListReq
	RoleId    *uint   `json:"roleId" dc:"角色ID"`
	Account   *string `json:"account" dc:"账户名称"`
	StartTime int64   `json:"startTime" dc:"开始时间"`
	EndTime   int64   `json:"endTime" dc:"结束时间"`
}
type AccountListRes struct {
	ListRes
	List []*AccountVo `json:"list"`
}

type AccountVo struct {
	Id      uint   `json:"id"            description:""`
	Account string `json:"account"       description:"帐号"`
	//Password       string `json:"password"      description:"密码"`
	GoogleAuthBind      int    `json:"googleAuthBind"      description:"谷歌登录 1:已绑定 2:未绑定"`
	NickName            string `json:"nickName"      description:"昵称"`
	Contact             string `json:"contact"       description:"联系方式"`
	Remark              string `json:"remark"        description:"备注"`
	RoleId              uint   `json:"roleId"        description:"角色id"`
	RoleName            string `json:"roleName"        description:"角色名称"`
	AuditPassword       string `json:"auditPassword" description:"私人密码"`
	IsOnline            int    `json:"isOnline"      description:"1:在线 2:离线"`
	IsAffect            int    `json:"isAffect"      description:"1:启用 2:停用"`
	CreateTime          int64  `json:"createTime"    description:"创建时间"`
	CreateAccount       string `json:"createAccount" description:"创建者"`
	UpdateTime          int64  `json:"updateTime"    description:"更新时间"`
	UpdateAccount       string `json:"updateAccount" description:"更新者"`
	Creater             uint   `json:"creater"        description:"创建者"`
	IsRequireGoogleAuth int    `json:"isRequireGoogleAuth" description:"谷歌开关 1:需要 2:不用"`
	GoogleAuthSecret    string `json:"googleAuthSecret"    description:"谷歌验证秘钥"`
	TemplateIds         []uint `json:"templateIds" description:"模板id集合"`
}

type AccountNameReq struct {
	g.Meta `path:"/accountMgr/name" method:"post" tags:"账户管理" summary:"Creator名称"`
}
type AccountNameRes struct {
	MapIdName map[uint]string `json:"mapIdName" dc:"账号ID名称对应表"`
}

type AccountSetGoogleAuthReq struct {
	g.Meta   `path:"/accountMgr/setGoogleAuth" method:"post" tags:"账户管理" summary:"谷歌开关设置"`
	Id       uint `v:"required" json:"id"`
	IsAffect int  `v:"required|between:1,2" json:"isAffect"      dc:"1:启用 2:停用"`
}

type AccountDetectionReq struct {
	g.Meta  `path:"/accountMgr/detection" method:"post" tags:"管理员操作" summary:"账号检测"`
	Account string `v:"required" json:"account"`
}
type AccountDetectionRes struct {
	IsExistGoogleOtp bool `json:"isExistGoogleOtp" dc:"谷歌验证码是否存在：ture是, false否"`
}

type AccountSetTemplateReq struct {
	g.Meta      `path:"/accountMgr/setTmpl" method:"post" tags:"管理员操作" summary:"模板设置"`
	Id          uint    `v:"required" json:"id"`
	TemplateIds *[]uint `json:"templateIds" dc:"模板id集合"`
}
